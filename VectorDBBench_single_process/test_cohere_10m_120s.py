#!/usr/bin/env python3
"""
Cohere 10M数据集120秒性能测试
测试16C64G资源限制下的性能表现
"""

import time
import random
import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Cohere10MBenchmark120s:
    def __init__(self, base_url="http://10.1.180.71:8001"):
        self.base_url = base_url
        self.dimension = 768
        self.test_duration = 120  # 120秒测试
        self.warmup_duration = 10  # 10秒预热
        
    def generate_query_vector(self):
        """生成768维随机查询向量"""
        return [random.gauss(0, 1) for _ in range(self.dimension)]
    
    def check_server_status(self):
        """检查服务器状态"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                logger.info("✅ 服务器连接正常")
                return True
            else:
                logger.error(f"❌ 服务器返回错误: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 服务器连接失败: {e}")
            return False
    
    def load_dataset(self):
        """加载Cohere 10M数据集"""
        logger.info("🔄 加载Cohere 10M数据集...")
        try:
            payload = {
                "case_config": {
                    "custom_case": {
                        "case_id": "Performance768D10M",
                        "dataset": "cohere/cohere_large_10m"
                    }
                }
            }
            
            response = requests.post(
                f"{self.base_url}/load", 
                json=payload, 
                timeout=600  # 10分钟超时
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 数据集加载成功: {result}")
                return True
            else:
                logger.error(f"❌ 数据集加载失败: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据集加载异常: {e}")
            return False
    
    def warmup_test(self):
        """预热测试，稳定系统性能"""
        logger.info(f"🔥 开始{self.warmup_duration}秒预热测试...")
        
        start_time = time.time()
        warmup_count = 0
        
        while time.time() - start_time < self.warmup_duration:
            try:
                query_vector = self.generate_query_vector()
                payload = {
                    "query": query_vector,
                    "k": 10
                }
                
                requests.post(f"{self.base_url}/search", json=payload, timeout=10)
                warmup_count += 1
                
            except Exception as e:
                logger.debug(f"预热请求失败: {e}")
        
        warmup_qps = warmup_count / self.warmup_duration
        logger.info(f"✅ 预热完成: {warmup_count} 次查询, 平均 {warmup_qps:.2f} QPS")
    
    def performance_test_120s(self):
        """120秒性能测试"""
        logger.info(f"🚀 开始{self.test_duration}秒性能测试...")
        
        start_time = time.time()
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        latencies = []
        
        # 实时统计
        last_report_time = start_time
        last_report_count = 0
        
        while time.time() - start_time < self.test_duration:
            try:
                query_vector = self.generate_query_vector()
                payload = {
                    "query": query_vector,
                    "k": 100  # Top-100搜索
                }
                
                req_start = time.time()
                response = requests.post(f"{self.base_url}/search", json=payload, timeout=30)
                req_end = time.time()
                
                total_requests += 1
                latency = req_end - req_start
                latencies.append(latency)
                
                if response.status_code == 200:
                    successful_requests += 1
                else:
                    failed_requests += 1
                    logger.debug(f"请求失败: {response.status_code}")
                
                # 每10秒报告一次进度
                current_time = time.time()
                if current_time - last_report_time >= 10:
                    elapsed = current_time - start_time
                    current_qps = (total_requests - last_report_count) / 10
                    overall_qps = total_requests / elapsed
                    remaining = self.test_duration - elapsed
                    
                    logger.info(f"📊 进度: {elapsed:.0f}s/{self.test_duration}s, "
                               f"当前QPS: {current_qps:.1f}, "
                               f"总体QPS: {overall_qps:.1f}, "
                               f"剩余: {remaining:.0f}s")
                    
                    last_report_time = current_time
                    last_report_count = total_requests
                
            except Exception as e:
                total_requests += 1
                failed_requests += 1
                logger.debug(f"请求异常: {e}")
        
        # 计算最终统计
        actual_duration = time.time() - start_time
        overall_qps = successful_requests / actual_duration
        
        # 延迟统计
        if latencies:
            avg_latency = sum(latencies) / len(latencies) * 1000  # 转换为毫秒
            p50_latency = sorted(latencies)[len(latencies)//2] * 1000
            p95_latency = sorted(latencies)[int(len(latencies)*0.95)] * 1000
            p99_latency = sorted(latencies)[int(len(latencies)*0.99)] * 1000
        else:
            avg_latency = p50_latency = p95_latency = p99_latency = 0
        
        # 输出详细结果
        logger.info("🎯 120秒性能测试完成!")
        logger.info("=" * 60)
        logger.info(f"📈 性能指标:")
        logger.info(f"   ⏱️  实际测试时间: {actual_duration:.1f}秒")
        logger.info(f"   📊 总请求数: {total_requests:,}")
        logger.info(f"   ✅ 成功请求: {successful_requests:,}")
        logger.info(f"   ❌ 失败请求: {failed_requests:,}")
        logger.info(f"   📈 成功率: {successful_requests/total_requests*100:.1f}%")
        logger.info(f"   🚀 QPS (每秒查询数): {overall_qps:.2f}")
        logger.info(f"   ⚡ 延迟统计 (毫秒):")
        logger.info(f"      - 平均延迟: {avg_latency:.2f}ms")
        logger.info(f"      - P50延迟: {p50_latency:.2f}ms")
        logger.info(f"      - P95延迟: {p95_latency:.2f}ms")
        logger.info(f"      - P99延迟: {p99_latency:.2f}ms")
        logger.info("=" * 60)
        
        return {
            "duration": actual_duration,
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "success_rate": successful_requests/total_requests*100,
            "qps": overall_qps,
            "avg_latency_ms": avg_latency,
            "p50_latency_ms": p50_latency,
            "p95_latency_ms": p95_latency,
            "p99_latency_ms": p99_latency
        }
    
    def run_benchmark(self):
        """运行完整的120秒基准测试"""
        logger.info("🎯 Cohere 10M数据集 120秒性能基准测试")
        logger.info("🔧 配置: 16核心64GB内存限制")
        logger.info("=" * 60)
        
        # 1. 检查服务器状态
        if not self.check_server_status():
            return None
        
        # 2. 加载数据集
        if not self.load_dataset():
            return None
        
        # 3. 预热测试
        self.warmup_test()
        
        # 4. 120秒性能测试
        results = self.performance_test_120s()
        
        # 5. 保存结果
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        result_file = f"cohere_10m_120s_benchmark_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 结果已保存到: {result_file}")
        return results

def main():
    """主函数"""
    benchmark = Cohere10MBenchmark120s()
    results = benchmark.run_benchmark()
    
    if results:
        logger.info("🎉 基准测试成功完成!")
        logger.info(f"🏆 最终QPS: {results['qps']:.2f}")
        logger.info(f"⚡ 平均延迟: {results['avg_latency_ms']:.2f}ms")
    else:
        logger.error("❌ 基准测试失败")

if __name__ == "__main__":
    main()
