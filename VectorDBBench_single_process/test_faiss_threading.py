#!/usr/bin/env python3
"""
测试FAISS多线程性能优化
分析不同线程配置对搜索性能的影响
"""

import os
import sys
import time
import numpy as np
import faiss
import threading
from concurrent.futures import ThreadPoolExecutor
import psutil
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_cpu_info():
    """获取CPU信息"""
    cpu_count = psutil.cpu_count(logical=False)  # 物理核心
    cpu_count_logical = psutil.cpu_count(logical=True)  # 逻辑核心
    
    logger.info(f"💻 CPU信息:")
    logger.info(f"   物理核心: {cpu_count}")
    logger.info(f"   逻辑核心: {cpu_count_logical}")
    logger.info(f"   超线程: {'是' if cpu_count_logical > cpu_count else '否'}")
    
    return cpu_count, cpu_count_logical

def create_test_index(n_vectors=100000, dim=768):
    """创建测试索引"""
    logger.info(f"🏗️ 创建测试索引: {n_vectors:,} 向量, {dim} 维")
    
    # 生成随机向量
    vectors = np.random.random((n_vectors, dim)).astype(np.float32)
    
    # 创建HNSW索引
    index = faiss.IndexHNSWFlat(dim, 16)
    index.hnsw.efConstruction = 200
    
    # 添加向量
    start_time = time.time()
    index.add(vectors)
    build_time = time.time() - start_time
    
    logger.info(f"✅ 索引构建完成: {build_time:.2f}s")
    return index

def test_openmp_threads(index, queries, thread_counts):
    """测试不同OpenMP线程数的性能"""
    logger.info("🧪 测试OpenMP线程数性能...")
    
    results = {}
    
    for thread_count in thread_counts:
        logger.info(f"📊 测试 {thread_count} 个OpenMP线程...")
        
        # 设置OpenMP线程数
        faiss.omp_set_num_threads(thread_count)
        os.environ['OMP_NUM_THREADS'] = str(thread_count)
        
        # 验证设置
        actual_threads = faiss.omp_get_max_threads()
        logger.info(f"   实际线程数: {actual_threads}")
        
        # 预热
        index.search(queries[:1], 10)
        
        # 性能测试
        search_times = []
        for i in range(5):  # 5次测试取平均
            start_time = time.time()
            distances, indices = index.search(queries, 10)
            search_time = time.time() - start_time
            search_times.append(search_time)
        
        avg_time = np.mean(search_times)
        std_time = np.std(search_times)
        qps = len(queries) / avg_time
        
        results[thread_count] = {
            'avg_time': avg_time,
            'std_time': std_time,
            'qps': qps
        }
        
        logger.info(f"   平均时间: {avg_time:.4f}s ± {std_time:.4f}s")
        logger.info(f"   QPS: {qps:.1f}")
    
    return results

def test_concurrent_requests(index, queries, concurrent_levels):
    """测试并发请求处理能力"""
    logger.info("🚀 测试并发请求处理...")
    
    def single_search(query_batch):
        """单个搜索任务"""
        return index.search(query_batch, 10)
    
    results = {}
    
    for concurrent in concurrent_levels:
        logger.info(f"📊 测试 {concurrent} 个并发请求...")
        
        # 将查询分成多个批次
        batch_size = len(queries) // concurrent
        query_batches = [queries[i:i+batch_size] for i in range(0, len(queries), batch_size)]
        query_batches = query_batches[:concurrent]  # 确保数量正确
        
        # 并发执行
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=concurrent) as executor:
            futures = [executor.submit(single_search, batch) for batch in query_batches]
            results_list = [future.result() for future in futures]
        
        total_time = time.time() - start_time
        total_queries = sum(len(batch) for batch in query_batches)
        qps = total_queries / total_time
        
        results[concurrent] = {
            'total_time': total_time,
            'total_queries': total_queries,
            'qps': qps
        }
        
        logger.info(f"   总时间: {total_time:.4f}s")
        logger.info(f"   总查询: {total_queries}")
        logger.info(f"   QPS: {qps:.1f}")
    
    return results

def test_batch_sizes(index, base_query, batch_sizes):
    """测试不同批次大小的性能"""
    logger.info("📦 测试批次大小性能...")
    
    results = {}
    
    for batch_size in batch_sizes:
        logger.info(f"📊 测试批次大小: {batch_size}")
        
        # 创建批次查询
        queries = np.tile(base_query, (batch_size, 1))
        
        # 性能测试
        search_times = []
        for i in range(3):  # 3次测试
            start_time = time.time()
            distances, indices = index.search(queries, 10)
            search_time = time.time() - start_time
            search_times.append(search_time)
        
        avg_time = np.mean(search_times)
        qps = batch_size / avg_time
        time_per_query = avg_time / batch_size
        
        results[batch_size] = {
            'avg_time': avg_time,
            'qps': qps,
            'time_per_query': time_per_query
        }
        
        logger.info(f"   平均时间: {avg_time:.4f}s")
        logger.info(f"   QPS: {qps:.1f}")
        logger.info(f"   单查询时间: {time_per_query:.6f}s")
    
    return results

def optimize_faiss_performance():
    """优化FAISS性能设置"""
    logger.info("⚡ 优化FAISS性能设置...")
    
    # CPU信息
    cpu_count, cpu_count_logical = get_cpu_info()
    
    # 推荐的线程配置
    recommended_threads = min(cpu_count, 16)  # 不超过16个线程
    
    logger.info(f"🔧 推荐配置:")
    logger.info(f"   OpenMP线程数: {recommended_threads}")
    
    # 设置环境变量
    env_vars = {
        'OMP_NUM_THREADS': str(recommended_threads),
        'MKL_NUM_THREADS': str(recommended_threads),
        'OPENBLAS_NUM_THREADS': str(recommended_threads),
        'VECLIB_MAXIMUM_THREADS': str(recommended_threads),
        'NUMEXPR_NUM_THREADS': str(recommended_threads),
    }
    
    for var, value in env_vars.items():
        os.environ[var] = value
        logger.info(f"   {var}={value}")
    
    # 设置FAISS线程数
    faiss.omp_set_num_threads(recommended_threads)
    
    # 验证设置
    actual_threads = faiss.omp_get_max_threads()
    logger.info(f"✅ FAISS实际线程数: {actual_threads}")
    
    return recommended_threads

def benchmark_single_vs_multi_process():
    """对比单进程多线程 vs 多进程的性能"""
    logger.info("⚖️ 对比单进程多线程 vs 多进程性能...")
    
    # 这里只能模拟，实际多进程测试需要单独脚本
    logger.info("📊 理论分析:")
    logger.info("单进程多线程优势:")
    logger.info("  ✅ 内存共享，无Copy-on-Write问题")
    logger.info("  ✅ 线程切换开销小")
    logger.info("  ✅ 缓存局部性好")
    logger.info("  ✅ 内存使用可控")
    
    logger.info("多进程优势:")
    logger.info("  ✅ 进程隔离，稳定性好")
    logger.info("  ✅ 可利用多CPU")
    logger.info("  ❌ 内存复制问题")
    logger.info("  ❌ 进程间通信开销")

def main():
    """主函数"""
    logger.info("🎯 FAISS多线程性能测试")
    logger.info("=" * 50)
    
    # 优化性能设置
    recommended_threads = optimize_faiss_performance()
    
    # 创建测试索引
    index = create_test_index(100000, 768)
    
    # 创建测试查询
    n_queries = 100
    queries = np.random.random((n_queries, 768)).astype(np.float32)
    base_query = queries[0:1]
    
    # 测试1: OpenMP线程数
    thread_counts = [1, 2, 4, 8, 16, 32]
    thread_counts = [t for t in thread_counts if t <= psutil.cpu_count()]
    openmp_results = test_openmp_threads(index, queries, thread_counts)
    
    # 测试2: 并发请求
    concurrent_levels = [1, 2, 4, 8]
    concurrent_results = test_concurrent_requests(index, queries, concurrent_levels)
    
    # 测试3: 批次大小
    batch_sizes = [1, 10, 50, 100, 500]
    batch_results = test_batch_sizes(index, base_query, batch_sizes)
    
    # 性能对比分析
    benchmark_single_vs_multi_process()
    
    # 输出最佳配置建议
    logger.info("🎯 最佳配置建议:")
    
    # 找到最佳OpenMP线程数
    best_thread_count = max(openmp_results.keys(), 
                           key=lambda x: openmp_results[x]['qps'])
    logger.info(f"   最佳OpenMP线程数: {best_thread_count}")
    logger.info(f"   最佳QPS: {openmp_results[best_thread_count]['qps']:.1f}")
    
    # 找到最佳批次大小
    best_batch_size = max(batch_results.keys(),
                         key=lambda x: batch_results[x]['qps'])
    logger.info(f"   最佳批次大小: {best_batch_size}")
    logger.info(f"   最佳单查询时间: {batch_results[best_batch_size]['time_per_query']:.6f}s")
    
    logger.info("🎉 测试完成!")

if __name__ == "__main__":
    main()
