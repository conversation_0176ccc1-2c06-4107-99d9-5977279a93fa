#!/usr/bin/env python3
"""
直接使用 FAISS 客户端进行完整基准测试
模拟 VectorDBBench 的测试流程但避免网络依赖
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import json
import csv
from pathlib import Path
from datetime import datetime
import traceback

# 添加项目路径
sys.path.insert(0, '/home/<USER>/VectorDBBench')

def create_comprehensive_benchmark():
    """创建全面的基准测试"""
    
    print("🔬 FAISS 完整基准测试 (模拟 VectorDBBench)")
    print("=" * 65)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        from vectordb_bench.backend.clients.faiss_local.faiss_local import FaissLocalClient
        from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
        from vectordb_bench.backend.clients.api import MetricType
        print("✅ FAISS 模块导入成功")
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    
    # 测试配置矩阵
    test_matrix = [
        {
            "name": "小规模高速测试",
            "n_train": 10000,
            "n_test": 500,
            "configs": [
                {"ef_search": 32, "k_values": [10, 50]},
                {"ef_search": 64, "k_values": [10, 50, 100]},
            ]
        },
        {
            "name": "中规模平衡测试", 
            "n_train": 30000,
            "n_test": 1000,
            "configs": [
                {"ef_search": 64, "k_values": [10, 50, 100]},
                {"ef_search": 128, "k_values": [50, 100]},
            ]
        },
        {
            "name": "大规模精度测试",
            "n_train": 50000,
            "n_test": 1000,
            "configs": [
                {"ef_search": 128, "k_values": [50, 100]},
                {"ef_search": 256, "k_values": [100]},
            ]
        }
    ]
    
    all_results = []
    test_summary = {
        "start_time": datetime.now().isoformat(),
        "test_environment": {
            "platform": "VectorDBBench + FAISS",
            "vector_dim": 1536,
            "metric_type": "COSINE",
            "index_type": "HNSW"
        },
        "tests": []
    }
    
    total_tests = sum(len(test["configs"]) * len(config["k_values"]) for test in test_matrix for config in test["configs"])
    completed_tests = 0
    
    print(f"📊 测试矩阵: {len(test_matrix)} 个规模 × 多种配置 = 总共 {total_tests} 个测试")
    
    for test_group in test_matrix:
        print(f"\n🎯 执行测试组: {test_group['name']}")
        print(f"   训练向量: {test_group['n_train']:,}")
        print(f"   测试查询: {test_group['n_test']:,}")
        
        # 生成测试数据
        np.random.seed(42)  # 固定种子确保可重现
        dim = 1536
        
        train_vectors = np.random.normal(0, 1, (test_group['n_train'], dim)).astype(np.float32)
        train_vectors = train_vectors / np.linalg.norm(train_vectors, axis=1, keepdims=True)
        
        test_queries = np.random.normal(0, 1, (test_group['n_test'], dim)).astype(np.float32)
        test_queries = test_queries / np.linalg.norm(test_queries, axis=1, keepdims=True)
        
        # 计算真实的 ground truth
        print(f"   🧮 计算 ground truth...")
        similarities = np.dot(test_queries, train_vectors.T)
        ground_truth = {}
        for k in [10, 50, 100]:
            ground_truth[k] = np.argsort(-similarities, axis=1)[:, :k]
        
        group_results = []
        
        for config in test_group['configs']:
            ef_search = config['ef_search']
            print(f"\n   🔍 测试配置: ef_search={ef_search}")
            
            # 创建客户端
            db_config = FaissLocalConfig(
                db_label=f"faiss_test_{test_group['name'].replace(' ', '_')}",
                index_type="HNSW"
            )
            
            case_config = HNSWConfig(
                m=16,
                ef_construction=200,
                ef_search=ef_search,
                metric_type=MetricType.COSINE,
            )
            
            client = FaissLocalClient(
                dim=dim,
                db_config=db_config,
                db_case_config=case_config,
                metric_type=MetricType.COSINE,
                drop_old=True
            )
            
            # 构建索引
            print(f"     🏗️  构建索引...")
            index_start = time.time()
            
            # 批量插入
            batch_size = 1000
            for i in range(0, len(train_vectors), batch_size):
                batch = train_vectors[i:i+batch_size]
                ids = list(range(i, min(i+batch_size, len(train_vectors))))
                client.insert_embeddings(batch.tolist(), ids)
            
            client.optimize()
            index_build_time = time.time() - index_start
            
            print(f"     ⏱️  索引构建完成: {index_build_time:.2f}s")
            
            # 测试不同的 k 值
            for k in config['k_values']:
                completed_tests += 1
                print(f"       📊 测试 k={k} ({completed_tests}/{total_tests})")
                
                # 预热
                warmup_queries = test_queries[:5]
                for query in warmup_queries:
                    client.search_embedding(query.tolist(), k=k)
                
                # 性能测试
                latencies = []
                search_results = []
                search_start = time.time()
                
                for i, query in enumerate(test_queries):
                    query_start = time.time()
                    result = client.search_embedding(query.tolist(), k=k)
                    query_end = time.time()
                    
                    latencies.append((query_end - query_start) * 1000)  # ms
                    search_results.append(result)
                
                total_search_time = time.time() - search_start
                
                # 计算性能指标
                qps = len(test_queries) / total_search_time
                avg_latency = np.mean(latencies)
                p50_latency = np.percentile(latencies, 50)
                p95_latency = np.percentile(latencies, 95)
                p99_latency = np.percentile(latencies, 99)
                
                # 计算真实召回率
                recall_scores = []
                gt_k = ground_truth[k]
                
                for i, result in enumerate(search_results):
                    if len(result) > 0:
                        # 确保结果是整数类型
                        result_ids = [int(r) for r in result[:k]]
                        gt_ids = set(gt_k[i])
                        found = len(set(result_ids) & gt_ids)
                        recall = found / k
                        recall_scores.append(recall)
                    else:
                        recall_scores.append(0.0)
                
                avg_recall = np.mean(recall_scores)
                
                # 记录结果
                result_entry = {
                    "test_group": test_group['name'],
                    "n_train": test_group['n_train'],
                    "n_test": test_group['n_test'],
                    "ef_search": ef_search,
                    "k": k,
                    "index_build_time_s": round(index_build_time, 2),
                    "qps": round(qps, 2),
                    "avg_latency_ms": round(avg_latency, 3),
                    "p50_latency_ms": round(p50_latency, 3),
                    "p95_latency_ms": round(p95_latency, 3),
                    "p99_latency_ms": round(p99_latency, 3),
                    "recall": round(avg_recall, 4),
                    "timestamp": datetime.now().isoformat()
                }
                
                all_results.append(result_entry)
                group_results.append(result_entry)
                
                print(f"         🚀 QPS: {qps:.1f}")
                print(f"         ⏱️  延迟: {avg_latency:.2f}ms (P99: {p99_latency:.2f}ms)")
                print(f"         🎯 召回率: {avg_recall:.1%}")
            
            # 清理客户端
            del client
        
        # 组结果摘要
        if group_results:
            best_qps = max(group_results, key=lambda x: x['qps'])
            best_recall = max(group_results, key=lambda x: x['recall'])
            
            print(f"\n   📈 {test_group['name']} 摘要:")
            print(f"      🏆 最佳QPS: {best_qps['qps']:.0f} (k={best_qps['k']}, ef_search={best_qps['ef_search']})")
            print(f"      🎯 最佳召回: {best_recall['recall']:.1%} (k={best_recall['k']}, ef_search={best_recall['ef_search']})")
        
        test_summary["tests"].append({
            "group_name": test_group['name'],
            "results": group_results
        })
    
    # 保存详细结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # JSON 格式
    json_file = f"/home/<USER>/VectorDBBench/faiss_comprehensive_benchmark_{timestamp}.json"
    with open(json_file, 'w') as f:
        json.dump({
            "summary": test_summary,
            "detailed_results": all_results
        }, f, indent=2, ensure_ascii=False)
    
    # CSV 格式
    csv_file = f"/home/<USER>/VectorDBBench/faiss_comprehensive_benchmark_{timestamp}.csv"
    with open(csv_file, 'w', newline='') as f:
        if all_results:
            writer = csv.DictWriter(f, fieldnames=all_results[0].keys())
            writer.writeheader()
            writer.writerows(all_results)
    
    # 生成摘要报告
    generate_final_report(all_results, timestamp)
    
    print(f"\n💾 结果已保存:")
    print(f"   📄 详细数据: {json_file}")
    print(f"   📊 CSV数据: {csv_file}")
    
    return True

def generate_final_report(results, timestamp):
    """生成最终报告"""
    
    report_file = f"/home/<USER>/VectorDBBench/faiss_benchmark_report_{timestamp}.txt"
    
    with open(report_file, 'w') as f:
        f.write("FAISS 完整基准测试报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"测试标识: {timestamp}\n\n")
        
        f.write("🔧 测试环境:\n")
        f.write("  - 向量维度: 1536D\n")
        f.write("  - 距离度量: COSINE\n")
        f.write("  - 索引类型: HNSW\n")
        f.write("  - HNSW参数: m=16, ef_construction=200\n\n")
        
        # 性能统计
        if results:
            qps_values = [r['qps'] for r in results]
            latency_values = [r['avg_latency_ms'] for r in results]
            recall_values = [r['recall'] for r in results]
            
            f.write("📊 性能统计:\n")
            f.write(f"  QPS范围: {min(qps_values):.0f} - {max(qps_values):.0f}\n")
            f.write(f"  延迟范围: {min(latency_values):.2f}ms - {max(latency_values):.2f}ms\n")
            f.write(f"  召回率范围: {min(recall_values):.1%} - {max(recall_values):.1%}\n\n")
            
            # 最佳配置
            best_qps = max(results, key=lambda x: x['qps'])
            best_recall = max(results, key=lambda x: x['recall'])
            best_latency = min(results, key=lambda x: x['avg_latency_ms'])
            
            f.write("🏆 最佳配置:\n")
            f.write(f"  最高QPS: {best_qps['qps']:.0f}\n")
            f.write(f"    配置: {best_qps['test_group']}, k={best_qps['k']}, ef_search={best_qps['ef_search']}\n")
            f.write(f"  最佳召回: {best_recall['recall']:.1%}\n")
            f.write(f"    配置: {best_recall['test_group']}, k={best_recall['k']}, ef_search={best_recall['ef_search']}\n")
            f.write(f"  最低延迟: {best_latency['avg_latency_ms']:.2f}ms\n")
            f.write(f"    配置: {best_latency['test_group']}, k={best_latency['k']}, ef_search={best_latency['ef_search']}\n\n")
            
            f.write(f"✅ 总测试数: {len(results)}\n")
            f.write("🎉 测试成功完成!\n")
    
    print(f"📋 详细报告已保存: {report_file}")
    
    # 控制台摘要
    print(f"\n🏆 最终性能摘要:")
    print("=" * 50)
    if results:
        best_qps = max(results, key=lambda x: x['qps'])
        best_recall = max(results, key=lambda x: x['recall'])
        
        print(f"🚀 峰值性能: {best_qps['qps']:.0f} QPS")
        print(f"   配置: k={best_qps['k']}, ef_search={best_qps['ef_search']}")
        print(f"   延迟: {best_qps['avg_latency_ms']:.2f}ms")
        
        print(f"🎯 最佳召回: {best_recall['recall']:.1%}")
        print(f"   配置: k={best_recall['k']}, ef_search={best_recall['ef_search']}")
        print(f"   QPS: {best_recall['qps']:.0f}")
        
        print(f"\n📊 总体范围:")
        qps_values = [r['qps'] for r in results]
        recall_values = [r['recall'] for r in results]
        print(f"   QPS: {min(qps_values):.0f} - {max(qps_values):.0f}")
        print(f"   召回率: {min(recall_values):.1%} - {max(recall_values):.1%}")

if __name__ == "__main__":
    try:
        success = create_comprehensive_benchmark()
        print(f"\n{'🎉 基准测试成功完成!' if success else '💥 基准测试失败!'}")
    except Exception as e:
        print(f"💥 测试过程中出现错误: {e}")
        traceback.print_exc()
        success = False
    
    sys.exit(0 if success else 1)
