#!/usr/bin/env python3
"""
带环境变量支持的资源限制配置脚本
支持通过环境变量动态设置资源限制
"""

import os
from resource_manager import ResourceManager

def get_resource_limits():
    """从环境变量获取资源限制配置"""
    max_cpu_cores = int(os.getenv('MAX_CPU_CORES', '16'))
    max_memory_gb = int(os.getenv('MAX_MEMORY_GB', '64'))
    
    return max_cpu_cores, max_memory_gb

def apply_resource_limits():
    """应用资源限制"""
    max_cpu_cores, max_memory_gb = get_resource_limits()
    
    resource_manager = ResourceManager(
        max_cpu_cores=max_cpu_cores, 
        max_memory_gb=max_memory_gb
    )
    
    resource_manager.apply_limits()
    print(f"✅ 资源限制已应用: {max_cpu_cores}核心, {max_memory_gb}GB内存")
    
    return resource_manager

# 使用示例
if __name__ == "__main__":
    # 可以通过环境变量配置:
    # export MAX_CPU_CORES=24
    # export MAX_MEMORY_GB=128
    # python dynamic_resource_manager.py
    
    resource_manager = apply_resource_limits()
    
    # 监控资源使用
    import time
    for i in range(3):
        stats = resource_manager.monitor_resources()
        time.sleep(2)
