#!/usr/bin/env python3
"""
专门诊断VectorDBBench 512并发卡住问题
"""

import sys
import os
import time
import psutil
import subprocess
import multiprocessing as mp
import threading
import signal
import logging
from concurrent.futures import ProcessPoolExecutor
import requests

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s: %(message)s')
log = logging.getLogger(__name__)

class ConcurrencyHangDiagnostic:
    def __init__(self):
        self.test_endpoint = "http://***********:8005/search"
        self.status_endpoint = "http://***********:8005/status"
        
    def check_system_resources(self):
        """检查系统资源状态"""
        print("🔍 系统资源检查")
        print("=" * 50)
        
        # 内存检查
        memory = psutil.virtual_memory()
        print(f"📊 内存状态:")
        print(f"   总内存: {memory.total // (1024**3)} GB")
        print(f"   可用内存: {memory.available // (1024**3)} GB")
        print(f"   使用率: {memory.percent}%")
        
        # CPU检查
        cpu_count = psutil.cpu_count()
        load_avg = os.getloadavg()
        print(f"📊 CPU状态:")
        print(f"   CPU核心数: {cpu_count}")
        print(f"   系统负载: {load_avg}")
        
        # 进程数检查
        current_processes = len(psutil.pids())
        print(f"📊 进程状态:")
        print(f"   当前进程数: {current_processes}")
        
        # 文件描述符检查
        try:
            result = subprocess.run(['bash', '-c', 'ulimit -n'], capture_output=True, text=True)
            max_files = result.stdout.strip()
            print(f"   最大文件描述符: {max_files}")
        except:
            print("   无法获取文件描述符限制")
        
        return {
            'memory_available_gb': memory.available // (1024**3),
            'memory_percent': memory.percent,
            'cpu_count': cpu_count,
            'load_avg': load_avg[0],
            'current_processes': current_processes
        }
    
    def test_server_connectivity(self):
        """测试服务器连接性"""
        print("\n🔍 服务器连接性测试")
        print("=" * 50)
        
        try:
            # 测试状态端点
            response = requests.get(self.status_endpoint, timeout=5)
            if response.status_code == 200:
                status = response.json()
                print(f"✅ 服务器状态正常: {status}")
                
                # 测试搜索端点
                import numpy as np
                test_query = np.random.random(768).tolist()
                search_response = requests.post(
                    self.test_endpoint,
                    json={"query": test_query, "topk": 10},
                    timeout=10
                )
                
                if search_response.status_code == 200:
                    print(f"✅ 搜索端点正常")
                    return True
                else:
                    print(f"❌ 搜索端点异常: {search_response.status_code}")
                    return False
            else:
                print(f"❌ 服务器状态异常: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 服务器连接失败: {e}")
            return False
    
    def test_process_creation_bottleneck(self):
        """测试进程创建瓶颈"""
        print("\n🔍 进程创建瓶颈测试")
        print("=" * 50)
        
        def dummy_worker(worker_id):
            """简单的工作进程"""
            time.sleep(1)
            return worker_id
        
        # 测试不同并发级别的进程创建
        test_levels = [100, 200, 300, 400, 500, 512]
        
        for level in test_levels:
            print(f"\n📊 测试 {level} 个进程创建...")
            
            start_time = time.time()
            try:
                with ProcessPoolExecutor(
                    max_workers=level,
                    mp_context=mp.get_context("spawn")
                ) as executor:
                    # 提交任务
                    futures = [executor.submit(dummy_worker, i) for i in range(level)]
                    
                    # 等待前10个任务完成（不等待全部）
                    completed = 0
                    for i, future in enumerate(futures):
                        try:
                            result = future.result(timeout=0.1)
                            completed += 1
                        except:
                            break
                        if i >= 9:  # 只等待前10个
                            break
                    
                    creation_time = time.time() - start_time
                    print(f"   创建时间: {creation_time:.2f}s")
                    print(f"   完成任务: {completed}/10")
                    
                    if creation_time > 30:  # 超过30秒认为有问题
                        print(f"   ⚠️ 进程创建过慢，可能是瓶颈")
                        return level
                        
            except Exception as e:
                print(f"   ❌ 进程创建失败: {e}")
                return level
        
        print(f"✅ 所有级别的进程创建都正常")
        return None
    
    def test_multiprocessing_manager_bottleneck(self):
        """测试multiprocessing.Manager瓶颈"""
        print("\n🔍 multiprocessing.Manager 瓶颈测试")
        print("=" * 50)
        
        def worker_with_manager(q, cond, worker_id):
            """使用Manager的工作进程"""
            try:
                q.put(worker_id)
                with cond:
                    cond.wait_for(lambda: False, timeout=1)  # 等待1秒后超时
                return worker_id
            except Exception as e:
                return f"error_{worker_id}_{e}"
        
        test_levels = [100, 200, 300, 400, 500, 512]
        
        for level in test_levels:
            print(f"\n📊 测试 {level} 个进程使用Manager...")
            
            start_time = time.time()
            try:
                with mp.Manager() as manager:
                    q = manager.Queue()
                    cond = manager.Condition()
                    
                    with ProcessPoolExecutor(
                        max_workers=level,
                        mp_context=mp.get_context("spawn")
                    ) as executor:
                        # 提交任务
                        futures = [
                            executor.submit(worker_with_manager, q, cond, i) 
                            for i in range(level)
                        ]
                        
                        # 等待队列填充
                        queue_start = time.time()
                        while q.qsize() < level and time.time() - queue_start < 30:
                            time.sleep(0.1)
                        
                        queue_fill_time = time.time() - queue_start
                        queue_size = q.qsize()
                        
                        print(f"   队列填充时间: {queue_fill_time:.2f}s")
                        print(f"   队列大小: {queue_size}/{level}")
                        
                        if queue_fill_time > 30 or queue_size < level * 0.9:
                            print(f"   ⚠️ Manager性能问题，可能是瓶颈")
                            return level
                        
            except Exception as e:
                print(f"   ❌ Manager测试失败: {e}")
                return level
        
        print(f"✅ Manager性能正常")
        return None
    
    def test_network_connection_limits(self):
        """测试网络连接限制"""
        print("\n🔍 网络连接限制测试")
        print("=" * 50)
        
        import socket
        import threading
        
        def create_connection(conn_id, results):
            """创建网络连接"""
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect(("***********", 8005))
                time.sleep(0.1)  # 保持连接一小段时间
                sock.close()
                results[conn_id] = "success"
            except Exception as e:
                results[conn_id] = f"error: {e}"
        
        # 测试并发连接数
        test_levels = [100, 200, 300, 400, 500, 512]
        
        for level in test_levels:
            print(f"\n📊 测试 {level} 个并发连接...")
            
            results = {}
            threads = []
            
            start_time = time.time()
            
            # 创建线程
            for i in range(level):
                thread = threading.Thread(target=create_connection, args=(i, results))
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=10)
            
            connection_time = time.time() - start_time
            success_count = sum(1 for r in results.values() if r == "success")
            
            print(f"   连接时间: {connection_time:.2f}s")
            print(f"   成功连接: {success_count}/{level}")
            
            if success_count < level * 0.9:
                print(f"   ⚠️ 网络连接限制，可能是瓶颈")
                return level
        
        print(f"✅ 网络连接正常")
        return None
    
    def simulate_vectordbbench_512(self):
        """模拟VectorDBBench的512并发流程"""
        print("\n🔍 模拟VectorDBBench 512并发流程")
        print("=" * 50)
        
        def mock_search_worker(test_data, q, cond, worker_id):
            """模拟VectorDBBench的搜索工作进程"""
            try:
                # 1. 同步阶段：加入队列
                q.put(1)
                print(f"Worker {worker_id}: 已加入队列")
                
                # 2. 等待同步信号
                with cond:
                    print(f"Worker {worker_id}: 等待同步信号")
                    cond.wait(timeout=60)  # 60秒超时
                    print(f"Worker {worker_id}: 收到同步信号")
                
                # 3. 模拟HTTP连接和搜索
                import requests
                session = requests.Session()
                
                # 执行几次搜索
                for i in range(3):
                    try:
                        response = session.post(
                            self.test_endpoint,
                            json={"query": test_data, "topk": 10},
                            timeout=10
                        )
                        if response.status_code == 200:
                            print(f"Worker {worker_id}: 搜索 {i+1} 成功")
                        else:
                            print(f"Worker {worker_id}: 搜索 {i+1} 失败 - {response.status_code}")
                    except Exception as e:
                        print(f"Worker {worker_id}: 搜索 {i+1} 异常 - {e}")
                
                return f"worker_{worker_id}_completed"
                
            except Exception as e:
                print(f"Worker {worker_id}: 异常 - {e}")
                return f"worker_{worker_id}_error_{e}"
        
        # 准备测试数据
        import numpy as np
        test_data = np.random.random(768).tolist()
        
        print(f"🚀 启动512进程模拟测试...")
        
        try:
            start_time = time.time()
            
            with mp.Manager() as manager:
                q = manager.Queue()
                cond = manager.Condition()
                
                print(f"📊 创建ProcessPoolExecutor(512)...")
                executor_start = time.time()
                
                with ProcessPoolExecutor(
                    max_workers=512,
                    mp_context=mp.get_context("spawn")
                ) as executor:
                    
                    executor_time = time.time() - executor_start
                    print(f"✅ ProcessPoolExecutor创建完成: {executor_time:.2f}s")
                    
                    print(f"📊 提交512个任务...")
                    submit_start = time.time()
                    
                    futures = [
                        executor.submit(mock_search_worker, test_data, q, cond, i)
                        for i in range(512)
                    ]
                    
                    submit_time = time.time() - submit_start
                    print(f"✅ 任务提交完成: {submit_time:.2f}s")
                    
                    print(f"📊 等待队列填充...")
                    queue_start = time.time()
                    
                    # 等待队列填充（最多等待60秒）
                    while q.qsize() < 512 and time.time() - queue_start < 60:
                        current_size = q.qsize()
                        if int(time.time() - queue_start) % 5 == 0:  # 每5秒报告一次
                            print(f"   队列进度: {current_size}/512")
                        time.sleep(0.5)
                    
                    queue_time = time.time() - queue_start
                    final_queue_size = q.qsize()
                    
                    print(f"✅ 队列填充完成: {queue_time:.2f}s, 大小: {final_queue_size}/512")
                    
                    if final_queue_size < 512:
                        print(f"❌ 队列填充不完整，可能卡在进程启动阶段")
                        return "queue_fill_incomplete"
                    
                    print(f"📊 发送同步信号...")
                    with cond:
                        cond.notify_all()
                    
                    print(f"📊 等待任务完成（最多等待30秒）...")
                    completed_count = 0
                    for i, future in enumerate(futures):
                        try:
                            result = future.result(timeout=0.1)
                            if "completed" in str(result):
                                completed_count += 1
                        except:
                            pass
                        
                        if i >= 10:  # 只检查前10个任务
                            break
                    
                    total_time = time.time() - start_time
                    
                    print(f"✅ 模拟测试完成:")
                    print(f"   总时间: {total_time:.2f}s")
                    print(f"   完成任务: {completed_count}/10 (样本)")
                    
                    return "simulation_completed"
                    
        except Exception as e:
            print(f"❌ 模拟测试失败: {e}")
            import traceback
            traceback.print_exc()
            return f"simulation_failed: {e}"
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🔬 VectorDBBench 512并发卡住问题诊断")
        print("=" * 60)
        
        # 1. 系统资源检查
        resources = self.check_system_resources()
        
        # 2. 服务器连接性测试
        server_ok = self.test_server_connectivity()
        if not server_ok:
            print("\n❌ 服务器连接有问题，无法继续测试")
            return
        
        # 3. 进程创建瓶颈测试
        process_bottleneck = self.test_process_creation_bottleneck()
        
        # 4. Manager瓶颈测试
        manager_bottleneck = self.test_multiprocessing_manager_bottleneck()
        
        # 5. 网络连接限制测试
        network_bottleneck = self.test_network_connection_limits()
        
        # 6. 完整模拟测试
        simulation_result = self.simulate_vectordbbench_512()
        
        # 7. 生成诊断报告
        self.generate_diagnosis_report(
            resources, process_bottleneck, manager_bottleneck, 
            network_bottleneck, simulation_result
        )
    
    def generate_diagnosis_report(self, resources, process_bottleneck, 
                                manager_bottleneck, network_bottleneck, simulation_result):
        """生成诊断报告"""
        print("\n" + "=" * 60)
        print("📋 512并发卡住问题诊断报告")
        print("=" * 60)
        
        print("🔍 问题定位:")
        
        bottlenecks = []
        
        if process_bottleneck:
            bottlenecks.append(f"进程创建瓶颈 (在{process_bottleneck}个进程时出现问题)")
        
        if manager_bottleneck:
            bottlenecks.append(f"multiprocessing.Manager瓶颈 (在{manager_bottleneck}个进程时出现问题)")
        
        if network_bottleneck:
            bottlenecks.append(f"网络连接限制 (在{network_bottleneck}个连接时出现问题)")
        
        if "incomplete" in simulation_result:
            bottlenecks.append("进程启动阶段卡住")
        elif "failed" in simulation_result:
            bottlenecks.append(f"模拟测试失败: {simulation_result}")
        
        if bottlenecks:
            print("❌ 发现的瓶颈:")
            for i, bottleneck in enumerate(bottlenecks, 1):
                print(f"   {i}. {bottleneck}")
        else:
            print("✅ 未发现明显瓶颈")
        
        print(f"\n📊 系统资源状态:")
        print(f"   可用内存: {resources['memory_available_gb']} GB")
        print(f"   内存使用率: {resources['memory_percent']}%")
        print(f"   CPU核心数: {resources['cpu_count']}")
        print(f"   系统负载: {resources['load_avg']:.2f}")
        print(f"   当前进程数: {resources['current_processes']}")
        
        print(f"\n💡 建议解决方案:")
        if process_bottleneck:
            print("1. 减少并发进程数到安全范围")
            print("2. 增加系统资源限制")
        if manager_bottleneck:
            print("3. 优化multiprocessing.Manager使用")
            print("4. 考虑使用更轻量的进程间通信方式")
        if network_bottleneck:
            print("5. 增加网络连接限制")
            print("6. 优化HTTP连接池配置")
        
        print("7. 考虑使用异步IO架构替代多进程架构")

def main():
    diagnostic = ConcurrencyHangDiagnostic()
    diagnostic.run_full_diagnosis()

if __name__ == "__main__":
    main()
