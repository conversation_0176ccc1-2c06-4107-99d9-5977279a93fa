#!/usr/bin/env python3
"""
🚀 解耦架构的FAISS客户端
- 专注索引类型和测试配置
- 不再关心数据集选择
- 参数完全解耦
"""

import argparse
import requests
import json
import sys
from datetime import datetime
from typing import Dict, Any, Optional

class DecoupledFaissClient:
    """解耦架构的FAISS客户端"""
    
    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        
    def get_server_capabilities(self) -> Dict[str, Any]:
        """获取服务器能力"""
        try:
            response = requests.get(f"{self.base_url}/capabilities")
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"获取服务器能力失败: {response.status_code}")
        except Exception as e:
            raise Exception(f"连接服务器失败: {e}")
    
    def list_datasets(self) -> Dict[str, Any]:
        """列出可用数据集"""
        try:
            response = requests.get(f"{self.base_url}/datasets")
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"获取数据集列表失败: {response.status_code}")
        except Exception as e:
            raise Exception(f"获取数据集列表失败: {e}")
    
    def run_test(self, test_request: Dict[str, Any]) -> Dict[str, Any]:
        """执行测试"""
        try:
            response = requests.post(f"{self.base_url}/test", json=test_request)
            if response.status_code == 200:
                return response.json()
            else:
                error_detail = response.text
                raise Exception(f"测试失败 ({response.status_code}): {error_detail}")
        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {e}")

def create_improved_parser():
    """创建改进的参数解析器 - 专注索引配置"""
    parser = argparse.ArgumentParser(
        description='解耦架构FAISS客户端 - 专注索引测试配置',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 测试HNSW索引性能
  python %(prog)s --index-type HNSW --index-params '{"m": 16, "ef_search": 64}'
  
  # 测试IVF索引精度，指定数据集偏好
  python %(prog)s --index-type IVF --test-type accuracy --prefer-dataset openai_500k
  
  # 测试Flat索引，要求1536维数据集
  python %(prog)s --index-type Flat --require-dim 1536 --min-size 50000
        """
    )

    # 连接配置
    parser.add_argument('--host', default='localhost', 
                       help='FAISS服务器地址')
    parser.add_argument('--port', type=int, default=8006, 
                       help='服务器端口')

    # 索引配置 (客户端核心关注点)
    parser.add_argument('--index-type', required=True,
                       choices=['HNSW', 'IVF', 'Flat', 'LSH'],
                       help='索引类型')
    
    parser.add_argument('--index-params', type=str,
                       help='索引参数JSON字符串，例如: {"m": 16, "ef_search": 64}')

    # 测试配置
    parser.add_argument('--test-type', default='performance',
                       choices=['performance', 'accuracy', 'load', 'stress'],
                       help='测试类型')
    
    parser.add_argument('--topk', type=int, default=100,
                       help='返回的近邻数量')
    
    parser.add_argument('--num-queries', type=int, default=1000,
                       help='查询次数')
    
    parser.add_argument('--concurrency', type=int, default=1,
                       help='并发度')

    # 数据集偏好 (可选，服务端决定)
    parser.add_argument('--prefer-dataset', type=str,
                       help='偏好的数据集名称')
    
    parser.add_argument('--require-dim', type=int,
                       help='要求的向量维度')
    
    parser.add_argument('--min-size', type=int,
                       help='最小数据集大小')
    
    parser.add_argument('--max-size', type=int,
                       help='最大数据集大小')

    # 输出配置
    parser.add_argument('--output', type=str,
                       help='结果输出文件路径')
    
    parser.add_argument('--verbose', action='store_true',
                       help='详细输出')

    return parser

def show_server_info(client: DecoupledFaissClient, verbose: bool = False):
    """显示服务器信息"""
    print("🔍 服务器能力检查")
    print("=" * 25)
    
    try:
        capabilities = client.get_server_capabilities()
        print(f"   📊 服务器状态: {capabilities['server_status']}")
        print(f"   🔧 支持的索引类型: {', '.join(capabilities['supported_index_types'])}")
        print(f"   📁 可用数据集数量: {len(capabilities['available_datasets'])}")
        
        if verbose:
            print("\n   📋 详细数据集信息:")
            for dataset in capabilities['available_datasets']:
                status = "✅ 已加载" if dataset['loaded'] else "📁 可用"
                print(f"      {status} {dataset['name']}")
                print(f"         维度: {dataset['dim']}, 大小: {dataset['size']}")
                print(f"         描述: {dataset['description']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 服务器连接失败: {e}")
        return False

def create_test_request(args) -> Dict[str, Any]:
    """根据参数创建测试请求"""
    request = {
        "index_type": args.index_type,
        "test_type": args.test_type,
        "topk": args.topk,
        "num_queries": args.num_queries,
        "concurrency": args.concurrency
    }
    
    # 索引参数
    if args.index_params:
        try:
            request["index_params"] = json.loads(args.index_params)
        except json.JSONDecodeError as e:
            raise ValueError(f"无效的index_params JSON: {e}")
    
    # 数据集偏好
    if args.prefer_dataset:
        request["prefer_dataset"] = args.prefer_dataset
    if args.require_dim:
        request["require_dim"] = args.require_dim
    if args.min_size:
        request["min_size"] = args.min_size
    if args.max_size:
        request["max_size"] = args.max_size
    
    return request

def display_test_results(result: Dict[str, Any], verbose: bool = False):
    """显示测试结果"""
    print("\n📊 测试结果")
    print("=" * 15)
    
    # 基本信息
    print(f"   🆔 测试ID: {result['test_id']}")
    print(f"   🕐 测试时间: {result['timestamp']}")
    
    # 使用的数据集
    dataset = result['selected_dataset']
    print(f"\n   📁 使用的数据集:")
    print(f"      📋 名称: {dataset['name']}")
    print(f"      📐 维度: {dataset['dim']}")
    print(f"      📊 大小: {dataset['size']:,} 个向量")
    print(f"      📝 描述: {dataset['description']}")
    
    # 索引信息
    index_info = result['index_info']
    print(f"\n   🔧 索引配置:")
    print(f"      🆔 索引ID: {index_info['index_id']}")
    print(f"      🏗️  索引类型: {index_info['index_type']}")
    if index_info['parameters']:
        print(f"      ⚙️  参数: {json.dumps(index_info['parameters'], indent=8)}")
    
    # 性能结果
    perf = result['performance']
    print(f"\n   🚀 性能指标:")
    print(f"      📈 QPS: {perf['qps']:,}")
    print(f"      ⏱️  平均延迟: {perf['avg_latency_ms']:.3f} ms")
    print(f"      📏 查询数量: {perf['num_queries']:,}")
    print(f"      🎯 TopK: {perf['topk']}")
    print(f"      ⏰ 总耗时: {perf['total_duration_s']:.3f} 秒")
    
    if verbose:
        print(f"\n   📋 原始请求:")
        request = result['request']
        print(f"      🔧 索引类型: {request['index_type']}")
        print(f"      🧪 测试类型: {request['test_type']}")
        if request.get('prefer_dataset'):
            print(f"      💝 偏好数据集: {request['prefer_dataset']}")
        if request.get('require_dim'):
            print(f"      📐 要求维度: {request['require_dim']}")

def save_results(result: Dict[str, Any], output_path: str):
    """保存结果到文件"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"   💾 结果已保存到: {output_path}")
    except Exception as e:
        print(f"   ❌ 保存结果失败: {e}")

def main():
    parser = create_improved_parser()
    args = parser.parse_args()
    
    print("🚀 解耦架构FAISS客户端")
    print("=" * 35)
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 连接地址: {args.host}:{args.port}")
    print()
    
    # 创建客户端
    client = DecoupledFaissClient(args.host, args.port)
    
    # 检查服务器状态
    if not show_server_info(client, args.verbose):
        sys.exit(1)
    
    # 显示测试配置
    print(f"\n🧪 测试配置")
    print("=" * 15)
    print(f"   🔧 索引类型: {args.index_type}")
    if args.index_params:
        try:
            params = json.loads(args.index_params)
            print(f"   ⚙️  索引参数: {json.dumps(params, indent=6)}")
        except:
            print(f"   ⚙️  索引参数: {args.index_params}")
    print(f"   🧪 测试类型: {args.test_type}")
    print(f"   🎯 TopK: {args.topk}")
    print(f"   📏 查询数量: {args.num_queries}")
    
    if args.prefer_dataset or args.require_dim or args.min_size:
        print(f"\n   💝 数据集偏好:")
        if args.prefer_dataset:
            print(f"      📋 偏好数据集: {args.prefer_dataset}")
        if args.require_dim:
            print(f"      📐 要求维度: {args.require_dim}")
        if args.min_size:
            print(f"      📊 最小大小: {args.min_size:,}")
        if args.max_size:
            print(f"      📊 最大大小: {args.max_size:,}")
    
    # 创建测试请求
    try:
        test_request = create_test_request(args)
    except ValueError as e:
        print(f"❌ 参数错误: {e}")
        sys.exit(1)
    
    # 执行测试
    print("\n⏳ 执行测试中...")
    try:
        start_time = datetime.now()
        result = client.run_test(test_request)
        end_time = datetime.now()
        
        total_time = (end_time - start_time).total_seconds()
        print(f"✅ 测试完成 (总耗时: {total_time:.2f}秒)")
        
        # 显示结果
        display_test_results(result, args.verbose)
        
        # 保存结果
        if args.output:
            save_results(result, args.output)
        
        print(f"\n🎊 测试成功完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
