#!/bin/bash
# FAISS VectorDBBench 环境设置

# 设置数据集路径
export DATASET_LOCAL_DIR="/home/<USER>/VectorDBBench/dataset"

# 设置AWS模拟配置（避免真实AWS访问）
export AWS_ACCESS_KEY_ID="fake_key_for_vectordb_bench"
export AWS_SECRET_ACCESS_KEY="fake_secret_for_vectordb_bench"
export AWS_DEFAULT_REGION="us-east-1"
export AWS_PROFILE="default"

echo "✅ FAISS环境已设置"
echo "   📂 数据集: $DATASET_LOCAL_DIR"
echo "   🔧 AWS: 模拟模式"

echo ""
echo "🚀 现在可以运行FAISS测试:"
echo "   # FAISS Local (推荐)"
echo "   vectordbbench faisslocalhnsw --case-type Performance1536D50K"
echo ""
echo "   # FAISS Remote (需要服务器)"
echo "   python -m vectordb_bench.cli.vectordbbench faissremote --uri localhost:8011 --case-type Performance1536D50K"
