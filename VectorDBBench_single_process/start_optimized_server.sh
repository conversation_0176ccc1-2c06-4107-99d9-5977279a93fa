#!/bin/bash
echo "停止现有FAISS服务..."
pkill -f "python3 smart_faiss_server.py"
sleep 2

echo "设置优化环境变量..."
export OMP_WAIT_POLICY=ACTIVE
export OMP_DYNAMIC=true
export OMP_NESTED=true
export OMP_PROC_BIND=close
export MKL_NUM_THREADS=64
export OPENBLAS_NUM_THREADS=64

echo "启动优化版FAISS服务器 - 高性能模式..."
# 使用更多CPU核心，增加OpenMP线程
nohup numactl -C 0-63 python3 smart_faiss_server.py --batch-size 10000 --base-delay 0.0001 --memory-threshold 85 --omp-threads 64 --port 8005 > faiss_server.log 2>&1 &

echo "服务已在后台启动，日志文件: faiss_server.log"
echo "5秒后开始监控..."
sleep 5
tail -f faiss_server.log

