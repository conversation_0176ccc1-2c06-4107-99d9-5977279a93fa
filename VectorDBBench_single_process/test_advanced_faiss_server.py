#!/usr/bin/env python3
"""
🚀 高级FAISS服务器测试脚本
演示预加载数据集的功能和优势
"""

import requests
import time
import json
from datetime import datetime

def test_advanced_server():
    """测试高级FAISS服务器"""
    server_url = "http://localhost:8005"
    
    print("🧪 高级FAISS服务器功能测试")
    print("=" * 50)
    
    # 1. 检查服务器状态
    print("\n📊 1. 服务器状态检查")
    try:
        response = requests.get(f"{server_url}/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   ✅ 服务器状态: {status['status']}")
            print(f"   📋 已加载数据集: {status['loaded_datasets']}")
            print(f"   🔧 可用索引: {status['available_indexes']}")
            print(f"   💾 内存使用: {status['memory_usage']}")
            print(f"   ⚙️ 配置: {json.dumps(status['config'], indent=6, ensure_ascii=False)}")
        else:
            print(f"   ❌ 服务器状态检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 连接服务器失败: {e}")
        return False
    
    # 2. 检查数据集列表
    print("\n📁 2. 数据集列表检查")
    try:
        response = requests.get(f"{server_url}/datasets")
        if response.status_code == 200:
            datasets_info = response.json()
            print(f"   📂 数据集根目录: {datasets_info['base_path']}")
            print(f"   📊 已加载数据集数量: {datasets_info['loaded_count']}")
            print("\n   📋 可用数据集:")
            
            for ds in datasets_info['datasets']:
                status_icon = "✅" if ds['loaded'] else ("📁" if ds['exists'] else "❌")
                print(f"      {status_icon} {ds['case_type']}")
                print(f"         路径: {ds['path']}")
                print(f"         存在: {ds['exists']}")
                print(f"         已加载: {ds['loaded']}")
                print(f"         描述: {ds['description']}")
                print()
    except Exception as e:
        print(f"   ❌ 获取数据集列表失败: {e}")
    
    # 3. 测试基准测试 (数据集已预加载，应该很快)
    print("\n🏃‍♂️ 3. 基准测试 (预加载数据集)")
    test_start_time = time.time()
    
    try:
        benchmark_request = {
            "case_type": "Performance1536D50K",
            "test_params": {
                "num_queries": 1000,
                "topk": 100
            }
        }
        
        print(f"   📤 发送基准测试请求: {benchmark_request['case_type']}")
        response = requests.post(f"{server_url}/benchmark", json=benchmark_request)
        
        if response.status_code == 200:
            results = response.json()
            test_end_time = time.time()
            total_time = test_end_time - test_start_time
            
            print(f"   ✅ 基准测试完成 (总耗时: {total_time:.2f}秒)")
            print(f"   📊 性能结果:")
            perf = results['performance']
            print(f"      🚀 QPS: {perf['qps']}")
            print(f"      ⏱️  平均延迟: {perf['average_latency_ms']} ms")
            print(f"      📏 查询数量: {perf['num_queries']}")
            print(f"      🎯 TopK: {perf['topk']}")
            
            dataset_info = results['dataset_info']
            print(f"   📋 数据集信息:")
            print(f"      📊 大小: {dataset_info['size']} 个向量")
            print(f"      📐 维度: {dataset_info['dim']} 维")
            print(f"      📝 描述: {dataset_info['description']}")
            
        else:
            print(f"   ❌ 基准测试失败: {response.status_code}")
            print(f"   📝 错误信息: {response.text}")
    
    except Exception as e:
        print(f"   ❌ 基准测试请求失败: {e}")
    
    # 4. 测试搜索功能
    print("\n🔍 4. 向量搜索测试")
    try:
        # 生成随机查询向量
        import numpy as np
        query_vector = np.random.random(1536).tolist()
        
        search_request = {
            "query": query_vector,
            "topk": 10
        }
        
        search_start_time = time.time()
        response = requests.post(f"{server_url}/search", json=search_request)
        search_end_time = time.time()
        
        if response.status_code == 200:
            search_results = response.json()
            search_time = (search_end_time - search_start_time) * 1000
            
            print(f"   ✅ 搜索完成 (耗时: {search_time:.2f}ms)")
            print(f"   🎯 使用索引: {search_results['index_name']}")
            print(f"   📊 返回结果数: {len(search_results['indices'])}")
            print(f"   📏 距离范围: {min(search_results['distances']):.4f} - {max(search_results['distances']):.4f}")
            
        else:
            print(f"   ❌ 搜索失败: {response.status_code}")
            print(f"   📝 错误信息: {response.text}")
    
    except Exception as e:
        print(f"   ❌ 搜索测试失败: {e}")
    
    return True

def test_dataset_loading_speed():
    """测试不同数据集加载方式的速度对比"""
    print("\n⚡ 5. 数据集加载速度对比")
    print("=" * 40)
    
    server_url = "http://localhost:8005"
    
    # 测试加载另一个数据集的速度
    print("📊 加载新数据集: Performance1536D500K")
    
    load_start_time = time.time()
    try:
        response = requests.post(f"{server_url}/load_dataset?case_type=Performance1536D500K&force_reload=false")
        load_end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            load_time = load_end_time - load_start_time
            
            print(f"   ✅ 加载完成 (耗时: {load_time:.2f}秒)")
            print(f"   📊 数据集信息:")
            ds = result['dataset']
            print(f"      📋 案例类型: {ds['case_type']}")
            print(f"      📊 大小: {ds['size']} 个向量")
            print(f"      📐 维度: {ds['dim']} 维")
            print(f"      📝 描述: {ds['description']}")
            
        else:
            print(f"   ❌ 加载失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 加载请求失败: {e}")

def show_architecture_comparison():
    """显示架构对比"""
    print("\n🏗️ 架构优势对比")
    print("=" * 30)
    
    print("📋 三种服务器架构对比:")
    print()
    
    print("   ❌ 原始架构 (错误设计):")
    print("      客户端: DATASET_LOCAL_DIR=/path --case-type Performance1536D50K")
    print("      问题: 客户端管理数据集，违反分布式原则")
    print()
    
    print("   ✅ Enhanced服务器 (基础版):")
    print("      服务器: DATASET_LOCAL_DIR=/path")
    print("      客户端: --case-type Performance1536D50K")
    print("      优点: 架构正确，动态加载")
    print("      缺点: 客户端需要等待数据加载")
    print()
    
    print("   🚀 Advanced服务器 (高级版):")
    print("      服务器: DATASET_CASE_TYPE=Performance1536D50K (预加载)")
    print("      客户端: --case-type Performance1536D50K")
    print("      优点: 零等待时间，预配置，更好体验")
    print()
    
    print("💡 关键改进:")
    print("   ✅ 启动时预加载指定数据集")
    print("   ✅ 自动创建FAISS索引")
    print("   ✅ 支持多种配置方式")
    print("   ✅ 零客户端等待时间")
    print("   ✅ 更好的生产环境部署体验")

def main():
    print("🚀 高级FAISS服务器完整测试")
    print("=" * 60)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("📋 测试目标:")
    print("   🎯 验证预加载数据集功能")
    print("   🎯 测试基准测试性能")
    print("   🎯 验证向量搜索功能")
    print("   🎯 对比不同架构的优势")
    print()
    
    # 执行测试
    success = test_advanced_server()
    
    if success:
        test_dataset_loading_speed()
        show_architecture_comparison()
        
        print("\n🎊 测试总结")
        print("=" * 20)
        print("✅ 高级FAISS服务器功能完全正常")
        print("✅ 预加载数据集机制工作正常")
        print("✅ 基准测试和搜索功能正常")
        print("✅ 架构改进显著提升用户体验")
        print()
        print("🚀 推荐用法:")
        print("   服务器端:")
        print("     DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset \\")
        print("     DATASET_CASE_TYPE=Performance1536D50K \\") 
        print("     AUTO_CREATE_INDEX=true \\")
        print("     python -m uvicorn vectordb_bench.backend.clients.faiss.advanced_server:app \\")
        print("       --host 0.0.0.0 --port 8005")
        print()
        print("   客户端:")
        print("     python simplified_remote_faiss_benchmark.py \\")
        print("       --host localhost --port 8005 \\")
        print("       --case-type Performance1536D50K")
        
    else:
        print("\n❌ 测试失败，请检查服务器状态")

if __name__ == "__main__":
    main()
