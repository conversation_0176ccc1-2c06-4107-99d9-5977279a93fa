#!/usr/bin/env python3
"""
修复VectorDBBench客户端维度不匹配问题 - 使用正确的768维case
"""

import os
import sys
import subprocess
import time

# 设置环境
os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'
sys.path.insert(0, '/home/<USER>/VectorDBBench')

def run_vectordb_benchmark_fixed():
    """运行修复后的VectorDBBench测试，使用正确的768维配置"""
    print("🔧 运行修复后的VectorDBBench测试...")
    print("=" * 50)
    
    # 使用命令行方式运行，确保参数正确
    cmd = [
        'python', '-m', 'vectordb_bench.cli.vectordbbench', 'faissremote',
        '--case-type', 'Performance768D1M',  # 明确使用768维case
        '--uri', 'http://127.0.0.1:8001',  # 使用uri参数
        '--index-type', 'HNSW',
        '--m', '16',
        '--ef-construction', '200',
        '--num-concurrency', '1,4',  # 减少并发数量以便快速测试
        '--concurrency-duration', '30',  # 减少测试时间
        '--k', '100',
        '--db-label', 'faiss_768d_fixed'
    ]
    
    print(f"📋 执行命令: {' '.join(cmd)}")
    print("🎯 关键配置:")
    print(f"   - Case: Performance768D1M (768维)")
    print(f"   - Server: 127.0.0.1:8001 (768维)")
    print(f"   - 预期: 维度匹配，无错误")
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=300,  # 5分钟超时
            cwd='/home/<USER>/VectorDBBench'
        )
        
        print(f"\n📤 退出码: {result.returncode}")
        
        if result.stdout:
            print("📄 标准输出:")
            print(result.stdout[-1000:])  # 显示最后1000字符
            
        if result.stderr:
            print("⚠️  标准错误:")
            print(result.stderr[-1000:])  # 显示最后1000字符
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时，但这可能是正常的（测试可能需要更长时间）")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def check_server_logs():
    """检查服务器日志中的维度匹配情况"""
    print("\n🔍 检查服务器端日志...")
    
    try:
        # 检查最近的服务器日志
        result = subprocess.run(
            ['tail', '-n', '50', '/home/<USER>/VectorDBBench/logs/vectordb_bench.log'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0 and result.stdout:
            output = result.stdout
            
            # 查找维度相关的日志
            lines = output.split('\n')
            dimension_lines = [line for line in lines if '维度' in line or 'dimension' in line.lower()]
            
            if dimension_lines:
                print("📊 维度相关日志:")
                for line in dimension_lines[-5:]:  # 最近5条
                    print(f"   {line}")
            else:
                print("📝 未发现维度相关的特殊日志")
                
            # 查找错误信息
            error_lines = [line for line in lines if 'error' in line.lower() or '错误' in line or 'warning' in line.lower()]
            if error_lines:
                print("⚠️  最近错误/警告:")
                for line in error_lines[-3:]:  # 最近3条
                    print(f"   {line}")
                    
        else:
            print("📋 无法读取日志文件")
            
    except Exception as e:
        print(f"❌ 检查日志失败: {e}")

def verify_dimension_consistency():
    """验证维度一致性"""
    print("\n✅ 验证维度一致性...")
    
    # 检查服务器维度
    try:
        import requests
        response = requests.get('http://127.0.0.1:8001/status', timeout=5)
        if response.status_code == 200:
            server_status = response.json()
            server_dim = server_status.get('dimension', 0)
            print(f"🖥️  服务器维度: {server_dim}")
        else:
            print("❌ 无法获取服务器状态")
            return False
    except Exception as e:
        print(f"❌ 连接服务器失败: {e}")
        return False
    
    # 检查VectorDBBench case维度
    try:
        from vectordb_bench.backend.cases import Performance768D1M
        case = Performance768D1M()
        case_dim = case.dataset.data.dim
        print(f"📊 VectorDBBench case维度: {case_dim}")
        
        # 验证匹配
        if server_dim == case_dim:
            print(f"✅ 维度匹配: 服务器({server_dim}) = VectorDBBench({case_dim})")
            return True
        else:
            print(f"❌ 维度不匹配: 服务器({server_dim}) ≠ VectorDBBench({case_dim})")
            return False
            
    except Exception as e:
        print(f"❌ 检查VectorDBBench case失败: {e}")
        return False

def main():
    print("🔧 VectorDBBench维度匹配修复程序")
    print("=" * 60)
    
    # 1. 验证维度一致性
    if not verify_dimension_consistency():
        print("❌ 维度不一致，请检查配置")
        return False
    
    # 2. 运行修复后的测试
    success = run_vectordb_benchmark_fixed()
    
    # 3. 检查服务器日志
    check_server_logs()
    
    # 4. 总结
    print("\n" + "=" * 60)
    if success:
        print("🎊 测试成功！VectorDBBench客户端和FAISS服务器维度匹配正确")
        print("✅ 解决方案:")
        print("   - 使用Performance768D1M case (768维)")
        print("   - FAISS服务器配置为768维")
        print("   - 客户端和服务器维度一致")
    else:
        print("⚠️  测试可能仍有问题，请检查:")
        print("   - 确保服务器已启动且配置正确")
        print("   - 确保数据集已正确加载")
        print("   - 检查网络连接")
    
    print("\n💡 最佳实践:")
    print("   - 始终确保VectorDBBench case和FAISS服务器使用相同维度")
    print("   - Performance768D1M → 768维服务器")
    print("   - Performance1536D50K → 1536维服务器")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
