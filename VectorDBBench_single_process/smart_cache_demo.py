#!/usr/bin/env python3
"""
简化的FAISS智能缓存演示，不下载大数据集
"""

import sys
import os
sys.path.append('/home/<USER>/VectorDBBench')

from vectordb_bench.backend.clients.faiss.faiss import FaissClient
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
import numpy as np

def demo_smart_cache():
    """演示智能缓存功能"""
    
    print("🚀 FAISS智能缓存演示")
    print("=" * 50)
    
    # 配置1: 匹配服务器配置 (应该使用缓存)
    print("\n📋 测试场景1: 配置匹配 (Flat, 768维)")
    print("-" * 30)
    
    db_config = FaissConfig(
        host='***********',
        port=8000,
        index_type='Flat'
    )
    
    db_case_config = FaissDBCaseConfig()
    
    try:
        client = FaissClient(
            dim=768,
            db_config=db_config,
            db_case_config=db_case_config,
            collection_name="test_collection"
        )
        
        # 模拟插入一些向量
        test_vectors = np.random.random((100, 768)).tolist()
        test_metadata = list(range(100))
        
        print(f"\n🔄 尝试插入 {len(test_vectors)} 个向量...")
        with client.init():
            inserted_count, error = client.insert_embeddings(test_vectors, test_metadata)
            
        if error is None:
            print(f"✅ 操作完成，处理了 {inserted_count} 个向量")
        else:
            print(f"❌ 插入失败: {error}")
            
    except Exception as e:
        print(f"❌ 客户端初始化失败: {e}")
    
    # 配置2: 索引类型不匹配 (应该重新创建)
    print("\n📋 测试场景2: 索引类型不匹配 (HNSW vs Flat)")
    print("-" * 30)
    
    db_config2 = FaissConfig(
        host='***********',
        port=8000,
        index_type='HNSW'  # 与服务器的Flat不匹配
    )
    
    db_case_config2 = FaissDBCaseConfig(m=16, ef_construction=200)
    
    try:
        client2 = FaissClient(
            dim=768,
            db_config=db_config2,
            db_case_config=db_case_config2,
            collection_name="test_collection_hnsw"
        )
        
        # 模拟插入一些向量
        test_vectors2 = np.random.random((50, 768)).tolist()
        test_metadata2 = list(range(50))
        
        print(f"\n🔄 尝试插入 {len(test_vectors2)} 个向量...")
        with client2.init():
            inserted_count2, error2 = client2.insert_embeddings(test_vectors2, test_metadata2)
            
        if error2 is None:
            print(f"✅ 操作完成，处理了 {inserted_count2} 个向量")
        else:
            print(f"❌ 插入失败: {error2}")
            
    except Exception as e:
        print(f"❌ 客户端初始化失败: {e}")
    
    # 配置3: 维度不匹配 (应该重新创建)
    print("\n📋 测试场景3: 维度不匹配 (1536 vs 768)")
    print("-" * 30)
    
    db_config3 = FaissConfig(
        host='***********',
        port=8000,
        index_type='Flat'
    )
    
    try:
        client3 = FaissClient(
            dim=1536,  # 与服务器的768不匹配
            db_config=db_config3,
            db_case_config=db_case_config,
            collection_name="test_collection_1536"
        )
        
        # 模拟插入一些向量
        test_vectors3 = np.random.random((30, 1536)).tolist()
        test_metadata3 = list(range(30))
        
        print(f"\n🔄 尝试插入 {len(test_vectors3)} 个向量...")
        with client3.init():
            inserted_count3, error3 = client3.insert_embeddings(test_vectors3, test_metadata3)
            
        if error3 is None:
            print(f"✅ 操作完成，处理了 {inserted_count3} 个向量")
        else:
            print(f"❌ 插入失败: {error3}")
            
    except Exception as e:
        print(f"❌ 客户端初始化失败: {e}")
    
    print("\n🎉 智能缓存演示完成!")
    print("\n💡 总结:")
    print("   ✅ 场景1: 配置匹配 → 使用缓存，跳过数据插入")
    print("   ⚠️  场景2: 索引不匹配 → 创建新索引，正常插入")
    print("   ⚠️  场景3: 维度不匹配 → 创建新索引，正常插入")

if __name__ == "__main__":
    demo_smart_cache()
