#!/usr/bin/env python3
"""
精确监控各个CPU核心的使用情况
"""

import subprocess
import time
import threading
import requests

def monitor_cpu_cores():
    """监控所有CPU核心使用情况"""
    while True:
        try:
            # 使用top命令查看各CPU核心使用率
            result = subprocess.run(['top', '-b', '-n1', '-p', '2144144'], 
                                  capture_output=True, text=True, timeout=5)
            
            # 使用mpstat查看详细CPU信息
            mpstat_result = subprocess.run(['mpstat', '-P', 'ALL', '1', '1'], 
                                         capture_output=True, text=True, timeout=5)
            
            print("=" * 80)
            print(f"⏰ {time.strftime('%H:%M:%S')} CPU核心使用情况:")
            print("Top输出(FAISS进程):")
            for line in result.stdout.split('\n'):
                if '2144144' in line or 'PID' in line or 'python' in line:
                    print(f"   {line}")
            
            print("\nmpstat输出(所有核心):")
            lines = mpstat_result.stdout.split('\n')
            for line in lines:
                if 'CPU' in line or '%usr' in line or any(str(i).zfill(2) in line for i in range(16)):
                    if any(c.isdigit() for c in line.split()) and '%' in line:
                        # 只显示有使用率的核心
                        parts = line.split()
                        try:
                            if len(parts) >= 4 and float(parts[2]) > 1.0:  # %usr > 1%
                                print(f"   💻 {line}")
                        except ValueError:
                            if 'CPU' in line:
                                print(f"   📊 {line}")
            
            time.sleep(3)
            
        except Exception as e:
            print(f"❌ 监控错误: {e}")
            time.sleep(5)

def stress_test_search():
    """压力测试搜索"""
    print("🚀 开始压力测试...")
    
    def concurrent_search():
        vector = [0.1] * 768
        for i in range(50):  # 每个线程50次请求
            try:
                requests.post('http://127.0.0.1:8001/search', 
                             json={'query': vector, 'topk': 100}, 
                             timeout=10)
            except:
                pass
    
    # 启动20个并发线程进行压力测试
    threads = []
    for i in range(20):
        t = threading.Thread(target=concurrent_search)
        threads.append(t)
        t.start()
    
    # 等待所有线程完成
    for t in threads:
        t.join()
    
    print("✅ 压力测试完成")

def main():
    print("🎯 精确CPU核心使用率监控")
    print("=" * 50)
    
    # 启动CPU监控线程
    monitor_thread = threading.Thread(target=monitor_cpu_cores, daemon=True)
    monitor_thread.start()
    
    # 等待3秒开始压力测试
    time.sleep(3)
    stress_test_search()
    
    # 继续监控10秒
    time.sleep(10)

if __name__ == "__main__":
    main()
