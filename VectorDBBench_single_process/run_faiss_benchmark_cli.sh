#!/bin/bash
"""
VectorDBBench FAISS 命令行基准测试示例
类似于 Milvus 的命令行使用方式
"""

echo "🎯 VectorDBBench FAISS 命令行基准测试示例"
echo "============================================="

# 设置数据集路径环境变量
export DATASET_LOCAL_DIR="/nas/yvan.chen/milvus/dataset"

echo "📁 数据集路径: $DATASET_LOCAL_DIR"
echo ""

# 1. FAISS HNSW 基准测试（类似你的 Milvus 命令）
echo "🚀 1. FAISS HNSW 基准测试"
echo "========================"
echo "类似命令: numactl -N 0 vectordbbench milvushnsw --uri 'http://10.1.180.13:19530' ..."
echo ""

# FAISS HNSW 测试命令
FAISS_HNSW_CMD="python -m vectordb_bench.cli.vectordbbench faisslocalhnsw \
    --case-type Performance1536D50K \
    --m 30 \
    --ef-construction 360 \
    --ef-search 100 \
    --concurrency-duration 300 \
    --num-concurrency 8,16,32,64,128 \
    --k 100 \
    --db-label faiss_hnsw_benchmark_$(date +%Y%m%d_%H%M%S) \
    --task-label FAISS_HNSW_Performance_Test"

echo "执行命令:"
echo "$FAISS_HNSW_CMD"
echo ""

# 执行 FAISS HNSW 测试
echo "正在执行 FAISS HNSW 测试..."
eval $FAISS_HNSW_CMD

echo ""
echo "================================="
echo ""

# 2. FAISS IVF Flat 基准测试
echo "🚀 2. FAISS IVF Flat 基准测试"
echo "============================"

FAISS_IVF_CMD="python -m vectordb_bench.cli.vectordbbench faisslocalivfflat \
    --case-type Performance1536D50K \
    --nlist 1024 \
    --nprobe 64 \
    --concurrency-duration 300 \
    --num-concurrency 8,16,32,64,128 \
    --k 100 \
    --db-label faiss_ivf_benchmark_$(date +%Y%m%d_%H%M%S) \
    --task-label FAISS_IVF_Performance_Test"

echo "执行命令:"
echo "$FAISS_IVF_CMD"
echo ""

# 执行 FAISS IVF 测试
echo "正在执行 FAISS IVF Flat 测试..."
eval $FAISS_IVF_CMD

echo ""
echo "================================="
echo ""

# 3. FAISS IVF PQ 基准测试
echo "🚀 3. FAISS IVF PQ 基准测试"
echo "========================="

FAISS_IVFPQ_CMD="python -m vectordb_bench.cli.vectordbbench faisslocalivfpq \
    --case-type Performance1536D50K \
    --nlist 1024 \
    --nprobe 64 \
    --m 64 \
    --nbits 8 \
    --concurrency-duration 300 \
    --num-concurrency 8,16,32,64,128 \
    --k 100 \
    --db-label faiss_ivfpq_benchmark_$(date +%Y%m%d_%H%M%S) \
    --task-label FAISS_IVFPQ_Performance_Test"

echo "执行命令:"
echo "$FAISS_IVFPQ_CMD"
echo ""

# 检查 FAISS IVF PQ 参数
echo "先检查 FAISS IVF PQ 参数..."
python -m vectordb_bench.cli.vectordbbench faisslocalivfpq --help | grep -E "(--m|--nbits|--nlist|--nprobe)"

echo ""
echo "🎉 所有 FAISS 基准测试完成!"
echo ""
echo "📊 查看结果:"
echo "结果文件通常保存在 results/ 目录下"
echo "可以使用以下命令查看最新结果:"
echo "ls -la results/ | head -10"
