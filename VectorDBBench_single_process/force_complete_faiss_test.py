#!/usr/bin/env python3
"""
强制执行完整的远程 FAISS 基准测试，确保生成结果文件
"""

import time
import argparse
import json
from pathlib import Path
from datetime import datetime
from vectordb_bench.cli.cli import run
from vectordb_bench.backend.clients import DB
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
from vectordb_bench.backend.task_runner import MetricType
from vectordb_bench import config

def force_complete_faiss_test():
    """强制执行完整的远程 FAISS 基准测试"""
    print("🎯 强制执行完整的远程 FAISS 基准测试")
    print("=" * 80)
    
    parser = argparse.ArgumentParser(description='强制完整远程 FAISS 基准测试')
    parser.add_argument('--host', default='127.0.0.1', help='FAISS 服务器主机')
    parser.add_argument('--port', type=int, default=8002, help='FAISS 服务器端口')
    parser.add_argument('--index-type', default='Flat', choices=['Flat'], 
                       help='索引类型 (仅推荐 Flat)')
    
    args = parser.parse_args()
    
    print(f"🔧 测试配置:")
    print(f"   服务器: {args.host}:{args.port}")
    print(f"   索引类型: {args.index_type}")
    
    # 确保结果目录存在
    results_dir = config.RESULTS_LOCAL_DIR
    faiss_results_dir = results_dir / "Faiss"
    faiss_results_dir.mkdir(exist_ok=True)
    print(f"   结果目录: {faiss_results_dir}")
    
    # 创建配置
    timestamp = int(time.time())
    db_config = FaissConfig(
        host=args.host,
        port=args.port,
        index_type=args.index_type,
        db_label=f"force_test_{timestamp}"
    )
    
    db_case_config = FaissDBCaseConfig(
        metric_type=MetricType.COSINE
    )
    
    print(f"\n🚀 开始完整基准测试...")
    print(f"   ⚠️  这次将执行所有阶段: 数据加载 + 串行搜索 + 并发搜索")
    
    try:
        start_time = time.time()
        
        # 执行完整的基准测试
        result = run(
            db=DB.Faiss,
            db_config=db_config,
            db_case_config=db_case_config,
            case_type="Performance1536D50K",  # 使用小数据集确保快速完成
            k=10,  # 减少返回数量以加快速度
            concurrency_duration=10,  # 每个并发测试10秒
            num_concurrency=[1, 2],  # 测试1和2个并发
            concurrency_timeout=300,  # 5分钟超时
            task_label=f"complete_faiss_test_{timestamp}",
            load=True,           # ✅ 启用数据加载
            search_serial=True,  # ✅ 启用串行搜索
            search_concurrent=True,  # ✅ 启用并发搜索
            drop_old=True,
            dry_run=False
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ 基准测试完成!")
        print(f"   总耗时: {duration:.2f}s")
        
        # 等待文件系统同步
        print(f"\n📊 等待结果文件生成...")
        time.sleep(3)
        
        # 检查结果文件
        result_files = list(faiss_results_dir.glob("*.json"))
        if result_files:
            result_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            print(f"✅ 发现 {len(result_files)} 个结果文件:")
            for i, file in enumerate(result_files[:3]):  # 显示最新的3个
                file_time = datetime.fromtimestamp(file.stat().st_mtime)
                file_size = file.stat().st_size
                print(f"   {i+1}. {file.name}")
                print(f"      大小: {file_size} bytes")
                print(f"      时间: {file_time}")
                
                # 读取最新文件的内容摘要
                if i == 0:
                    try:
                        with open(file, 'r') as f:
                            data = json.load(f)
                        
                        print(f"      📋 内容摘要:")
                        print(f"         任务ID: {data.get('run_id', 'N/A')}")
                        print(f"         任务标签: {data.get('task_label', 'N/A')}")
                        
                        if 'results' in data and data['results']:
                            metrics = data['results'][0].get('metrics', {})
                            print(f"         插入耗时: {metrics.get('insert_duration', 0):.2f}s")
                            print(f"         QPS: {metrics.get('qps', 0):.2f}")
                            print(f"         P99延迟: {metrics.get('serial_latency_p99', 0):.4f}s")
                            print(f"         召回率: {metrics.get('recall', 0):.4f}")
                            
                            conc_qps = metrics.get('conc_qps_list', [])
                            if conc_qps:
                                print(f"         并发QPS: {conc_qps}")
                                
                    except Exception as e:
                        print(f"      ❌ 读取文件内容失败: {e}")
                
                print()
        else:
            print(f"❌ 仍然没有生成结果文件")
            print(f"   检查任务是否真正执行...")
            
            # 检查最近的日志
            log_file = Path("logs/vectordb_bench.log")
            if log_file.exists():
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    recent_lines = [line for line in lines[-20:] if str(timestamp) in line or 'complete_faiss_test' in line]
                    
                if recent_lines:
                    print(f"   📝 相关日志条目:")
                    for line in recent_lines[-5:]:
                        print(f"      {line.strip()}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 完整基准测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_server_status(host, port):
    """检查服务器状态"""
    print(f"🔍 检查 FAISS 服务器状态...")
    
    try:
        import requests
        response = requests.get(f"http://{host}:{port}/docs", timeout=5)
        if response.status_code == 200:
            print(f"   ✅ FAISS 服务器运行正常: {host}:{port}")
            return True
        else:
            print(f"   ❌ FAISS 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 无法连接到 FAISS 服务器: {e}")
        return False

if __name__ == "__main__":
    # 解析参数以检查服务器
    parser = argparse.ArgumentParser(description='强制完整远程 FAISS 基准测试')
    parser.add_argument('--host', default='127.0.0.1', help='FAISS 服务器主机')
    parser.add_argument('--port', type=int, default=8002, help='FAISS 服务器端口')
    
    args, _ = parser.parse_known_args()
    
    # 检查服务器
    if check_server_status(args.host, args.port):
        # 执行完整测试
        success = force_complete_faiss_test()
        
        if success:
            print(f"\n🎉 测试完成! 现在应该可以在 vectordb_bench/results/Faiss/ 目录下看到结果文件了")
            print(f"查看命令: ls -la vectordb_bench/results/Faiss/")
        else:
            print(f"\n💥 测试失败，请检查错误信息")
            
        exit(0 if success else 1)
    else:
        print(f"\n💥 服务器检查失败，无法运行测试")
        exit(1)
