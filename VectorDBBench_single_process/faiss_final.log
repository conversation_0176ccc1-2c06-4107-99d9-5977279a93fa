nohup: ignoring input
INFO:__main__:🧵 使用同步模型 (无线程池) - 避免内存不足问题
INFO:resource_manager:Applying resource limits: 16 CPU cores, 64.0GB RAM
INFO:resource_manager:CPU affinity set to cores: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
INFO:resource_manager:Memory limit set to 64.0GB
INFO:__main__:✅ 资源限制已应用: 16核心, 64GB内存
INFO:__main__:🧵 FAISS OpenMP线程数设置为: 2
[2025-07-26 21:55:02 +0800] [396059] [INFO] Starting gunicorn 23.0.0
[2025-07-26 21:55:02 +0800] [396059] [INFO] Listening at: http://0.0.0.0:8005 (396059)
[2025-07-26 21:55:02 +0800] [396059] [INFO] Using worker: uvicorn.workers.UvicornWorker
[2025-07-26 21:55:02 +0800] [396297] [INFO] Booting worker with pid: 396297
[2025-07-26 21:55:02 +0800] [396297] [INFO] Started server process [396297]
[2025-07-26 21:55:02 +0800] [396297] [INFO] Waiting for application startup.
[2025-07-26 21:55:02 +0800] [396297] [INFO] Application startup complete.
[2025-07-26 21:55:02 +0800] [396310] [INFO] Booting worker with pid: 396310
[2025-07-26 21:55:02 +0800] [396310] [INFO] Started server process [396310]
[2025-07-26 21:55:02 +0800] [396310] [INFO] Waiting for application startup.
[2025-07-26 21:55:02 +0800] [396310] [INFO] Application startup complete.
[2025-07-26 21:55:02 +0800] [396405] [INFO] Booting worker with pid: 396405
[2025-07-26 21:55:02 +0800] [396405] [INFO] Started server process [396405]
[2025-07-26 21:55:02 +0800] [396405] [INFO] Waiting for application startup.
[2025-07-26 21:55:02 +0800] [396405] [INFO] Application startup complete.
[2025-07-26 21:55:02 +0800] [396482] [INFO] Booting worker with pid: 396482
[2025-07-26 21:55:02 +0800] [396483] [INFO] Booting worker with pid: 396483
[2025-07-26 21:55:02 +0800] [396482] [INFO] Started server process [396482]
[2025-07-26 21:55:02 +0800] [396482] [INFO] Waiting for application startup.
[2025-07-26 21:55:02 +0800] [396482] [INFO] Application startup complete.
[2025-07-26 21:55:02 +0800] [396483] [INFO] Started server process [396483]
[2025-07-26 21:55:02 +0800] [396483] [INFO] Waiting for application startup.
[2025-07-26 21:55:02 +0800] [396483] [INFO] Application startup complete.
[2025-07-26 21:55:02 +0800] [396546] [INFO] Booting worker with pid: 396546
[2025-07-26 21:55:02 +0800] [396546] [INFO] Started server process [396546]
[2025-07-26 21:55:02 +0800] [396546] [INFO] Waiting for application startup.
[2025-07-26 21:55:02 +0800] [396546] [INFO] Application startup complete.
[2025-07-26 21:55:02 +0800] [396597] [INFO] Booting worker with pid: 396597
[2025-07-26 21:55:02 +0800] [396597] [INFO] Started server process [396597]
[2025-07-26 21:55:02 +0800] [396597] [INFO] Waiting for application startup.
[2025-07-26 21:55:02 +0800] [396597] [INFO] Application startup complete.
[2025-07-26 21:55:02 +0800] [396622] [INFO] Booting worker with pid: 396622
[2025-07-26 21:55:02 +0800] [396622] [INFO] Started server process [396622]
[2025-07-26 21:55:02 +0800] [396622] [INFO] Waiting for application startup.
[2025-07-26 21:55:02 +0800] [396622] [INFO] Application startup complete.
INFO:__main__:创建索引: 维度=768, 类型=HNSW
INFO:__main__:HNSW参数: M=30, ef_construction=360
INFO:__main__:🚀 查找匹配的真实数据集...
INFO:__main__:✅ 找到匹配数据集: Performance768D1M (/nas/yvan.chen/milvus/dataset/cohere/cohere_medium_1m)
INFO:__main__:📁 加载数据集: cohere/cohere_medium_1m
INFO:__main__:📊 正在加载 1 个训练文件...
INFO:__main__:📄 读取文件: shuffle_train.parquet
INFO:__main__:📊 已加载 50,000 个向量
INFO:__main__:📊 已加载 100,000 个向量
INFO:__main__:📊 已加载 150,000 个向量
INFO:__main__:📊 已加载 200,000 个向量
INFO:__main__:📊 已加载 250,000 个向量
INFO:__main__:📊 已加载 300,000 个向量
INFO:__main__:📊 已加载 350,000 个向量
INFO:__main__:📊 已加载 400,000 个向量
INFO:__main__:📊 已加载 450,000 个向量
INFO:__main__:📊 已加载 500,000 个向量
INFO:__main__:📊 已加载 550,000 个向量
INFO:__main__:📊 已加载 600,000 个向量
INFO:__main__:📊 已加载 650,000 个向量
INFO:__main__:📊 已加载 700,000 个向量
INFO:__main__:📊 已加载 750,000 个向量
INFO:__main__:📊 已加载 800,000 个向量
INFO:__main__:📊 已加载 850,000 个向量
INFO:__main__:📊 已加载 900,000 个向量
INFO:__main__:📊 已加载 950,000 个向量
INFO:__main__:📊 已加载 1,000,000 个向量
INFO:__main__:✅ 数据集加载完成: 1,000,000 个向量
INFO:__main__:✅ 索引创建完成，包含 1,000,000 个向量
