# -*- coding: utf-8 -*-
"""
python print_required.py  # 默认打印 CapacityDim128
python print_required.py Performance768D1M
"""
import sys, inspect, importlib, re, pandas as pd
from typing import get_origin, get_args
from pydantic import BaseModel

# -------- 1. 解析目标 Case --------
case_name = sys.argv[1] if len(sys.argv) > 1 else "CapacityDim128"
cases_mod = importlib.import_module("vectordb_bench.backend.cases")
TargetCase: type[BaseModel] = getattr(cases_mod, case_name)

# -------- 2. 递归打印必填字段 --------
def is_df(field) -> bool:
    return (
        field.type_ is pd.DataFrame
        or field.outer_type_ is pd.DataFrame
        or get_origin(field.outer_type_) is pd.DataFrame
    )

def walk(model: type[BaseModel], prefix=""):
    for name, field in model.__fields__.items():
        if is_df(field):
            continue                         # 跳过 DataFrame
        path = f"{prefix}{name}"
        if field.required:
            print(path)
        # 嵌套模型
        if inspect.isclass(field.type_) and issubclass(field.type_, BaseModel):
            walk(field.type_, prefix=f"{path}.")
        # List[SubModel]
        if get_origin(field.outer_type_) is list:
            sub_type = get_args(field.outer_type_)[0]
            if inspect.isclass(sub_type) and issubclass(sub_type, BaseModel):
                walk(sub_type, prefix=f"{path}[].")

print(f"\n★ 必填字段列表 ({TargetCase.__name__}):")
walk(TargetCase)

