#!/usr/bin/env python3
"""
测试增强版FAISS remote - 服务端占位方案
"""

import os
import sys
import subprocess
import time

def test_enhanced_faiss_remote():
    """测试增强版的FAISS remote客户端"""
    
    print("🎯 测试增强版FAISS Remote客户端")
    print("=" * 50)
    
    # 检查命令是否注册成功
    print("🔍 检查命令注册...")
    try:
        result = subprocess.run([
            'python', '-m', 'vectordb_bench.cli.vectordbbench', 'faissremote', '--help'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 增强版faissremote命令注册成功")
            # 检查是否包含我们的增强功能描述
            if "零配置" in result.stdout or "占位" in result.stdout or "服务端" in result.stdout:
                print("✅ 确认为增强版本")
            else:
                print("⚠️ 可能仍为原版本")
        else:
            print(f"❌ 命令注册失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False
    
    # 测试dry-run模式
    print("\n🧪 测试dry-run模式...")
    try:
        cmd = [
            'python', '-m', 'vectordb_bench.cli.vectordbbench', 'faissremote',
            '--uri', 'localhost:8011',
            '--case-type', 'Performance1536D50K',
            '--dry-run'
        ]
        
        print(f"📋 命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Dry-run测试成功")
            if "占位" in result.stdout or "🎭" in result.stdout:
                print("✅ 确认包含占位逻辑")
            return True
        else:
            print(f"❌ Dry-run失败: {result.stderr}")
            print("输出:", result.stdout[-500:])
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Dry-run超时")
        return False
    except Exception as e:
        print(f"❌ Dry-run异常: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n🚀 增强版FAISS Remote使用方法")
    print("=" * 50)
    print("\n✨ 自动发现服务器（最简）:")
    print("python -m vectordb_bench.cli.vectordbbench faissremote --case-type Performance1536D50K")
    
    print("\n🔧 指定服务器:")
    print("python -m vectordb_bench.cli.vectordbbench faissremote \\")
    print("    --uri localhost:8011 \\")
    print("    --case-type Performance1536D50K")
    
    print("\n⚡ 完整性能测试:")
    print("python -m vectordb_bench.cli.vectordbbench faissremote \\")
    print("    --uri localhost:8011 \\")
    print("    --case-type Performance1536D50K \\")
    print("    --concurrency-duration 60 \\")
    print("    --num-concurrency 4,8,16")
    
    print("\n💡 核心优势:")
    print("   🎭 客户端数据集占位，避免下载卡死")
    print("   📊 服务端管理真实数据集和索引")
    print("   🚀 零配置启动，自动发现服务器")
    print("   🛡️ 完全兼容VectorDBBench框架")

if __name__ == "__main__":
    success = test_enhanced_faiss_remote()
    
    if success:
        print("\n🎊 增强版FAISS Remote配置成功！")
        show_usage_examples()
    else:
        print("\n❌ 配置验证失败，请检查修改")
