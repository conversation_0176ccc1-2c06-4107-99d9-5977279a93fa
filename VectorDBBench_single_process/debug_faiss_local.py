#!/usr/bin/env python3
"""
Debug script for FAISS local hanging issue
"""

import os
import sys

# Set environment
os.environ['DATASET_LOCAL_DIR'] = '/home/<USER>/VectorDBBench/dataset'

print(f"🔧 Environment setup:")
print(f"   DATASET_LOCAL_DIR: {os.environ.get('DATASET_LOCAL_DIR')}")
print(f"   Python path: {sys.executable}")
print(f"   Working directory: {os.getcwd()}")

# Test dataset access
dataset_dir = os.environ['DATASET_LOCAL_DIR']
print(f"\n📂 Dataset structure:")
if os.path.exists(dataset_dir):
    for root, dirs, files in os.walk(dataset_dir):
        level = root.replace(dataset_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
else:
    print(f"❌ Dataset directory not found: {dataset_dir}")

# Test basic imports
print(f"\n🔍 Testing imports:")
try:
    from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig
    print("✅ FaissLocalConfig import successful")
except Exception as e:
    print(f"❌ FaissLocalConfig import failed: {e}")

try:
    from vectordb_bench.backend.clients.faiss_local.cli import faisslocalhnsw
    print("✅ faisslocalhnsw CLI import successful")
except Exception as e:
    print(f"❌ faisslocalhnsw CLI import failed: {e}")

# Test command registration
print(f"\n🎯 Testing command registration:")
try:
    import subprocess
    result = subprocess.run([
        sys.executable, '-m', 'vectordb_bench.cli.vectordbbench', 'faisslocalhnsw', '--help'
    ], capture_output=True, text=True, timeout=10)
    
    if result.returncode == 0:
        print("✅ Command registration successful")
        print("Help output:")
        print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
    else:
        print(f"❌ Command failed with return code: {result.returncode}")
        print(f"Error: {result.stderr}")
except Exception as e:
    print(f"❌ Command test failed: {e}")

print(f"\n🚀 Ready to test FAISS local benchmark!")
