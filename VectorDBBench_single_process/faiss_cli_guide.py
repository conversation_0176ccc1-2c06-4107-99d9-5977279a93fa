#!/usr/bin/env python3
"""
VectorDBBench FAISS 命令行使用指南
对比 Milvus 和 FAISS 的命令行使用方式
"""

def show_command_comparison():
    """显示 Milvu<PERSON> vs FAISS 命令对比"""
    print("🎯 VectorDBBench 命令行对比: <PERSON>l<PERSON><PERSON> vs FAISS")
    print("=" * 60)
    
    print("📊 你的 Milvus 命令:")
    print("```bash")
    print("numactl -N 0 vectordbbench milvushnsw \\")
    print("    --uri 'http://10.1.180.13:19530' \\")
    print("    --case-type Performance1536D50K \\")
    print("    --m 30 \\")
    print("    --ef-construction 360 \\")
    print("    --ef-search 100 \\")
    print("    --concurrency-duration 300 \\")
    print("    --num-concurrency 8,16,32,64,128")
    print("```")
    
    print("\n🔄 对应的 FAISS 命令:")
    print("```bash")
    print("# 1. FAISS HNSW (最接近你的 Milvus HNSW)")
    print("python -m vectordb_bench.cli.vectordbbench faisslocalhnsw \\")
    print("    --case-type Performance1536D50K \\")
    print("    --m 30 \\")
    print("    --ef-construction 360 \\")
    print("    --ef-search 100 \\")
    print("    --concurrency-duration 300 \\")
    print("    --num-concurrency 8,16,32,64,128 \\")
    print("    --k 100 \\")
    print("    --db-label faiss_hnsw_benchmark \\")
    print("    --task-label FAISS_HNSW_Performance_Test")
    print("```")
    
    print("\n📋 主要区别:")
    print("• Milvus: 需要 --uri 指定远程服务器")
    print("• FAISS: 本地执行，无需 --uri")
    print("• FAISS: 需要额外的 --k 参数（返回邻居数量）")
    print("• FAISS: 可选的 --db-label 和 --task-label 用于标识测试")

def show_faiss_commands():
    """显示各种 FAISS 命令示例"""
    print("\n🚀 FAISS 各种索引类型的命令示例")
    print("=" * 50)
    
    # HNSW
    print("\n1️⃣ FAISS HNSW 索引:")
    print("```bash")
    print("python -m vectordb_bench.cli.vectordbbench faisslocalhnsw \\")
    print("    --case-type Performance1536D50K \\")
    print("    --m 30 \\                          # HNSW m 参数")
    print("    --ef-construction 360 \\           # 构建时的 ef 参数")
    print("    --ef-search 100 \\                 # 搜索时的 ef 参数")
    print("    --concurrency-duration 300 \\")
    print("    --num-concurrency 8,16,32,64,128")
    print("```")
    
    # IVF Flat
    print("\n2️⃣ FAISS IVF Flat 索引:")
    print("```bash")
    print("python -m vectordb_bench.cli.vectordbbench faisslocalivfflat \\")
    print("    --case-type Performance1536D50K \\")
    print("    --nlist 1024 \\                    # IVF 聚类中心数量")
    print("    --nprobe 64 \\                     # 搜索时探测的聚类数量")
    print("    --concurrency-duration 300 \\")
    print("    --num-concurrency 8,16,32,64,128")
    print("```")
    
    # IVF PQ
    print("\n3️⃣ FAISS IVF PQ 索引:")
    print("```bash")
    print("python -m vectordb_bench.cli.vectordbbench faisslocalivfpq \\")
    print("    --case-type Performance1536D50K \\")
    print("    --nlist 1024 \\                    # IVF 聚类中心数量")
    print("    --nprobe 64 \\                     # 搜索时探测的聚类数量")
    print("    --m 64 \\                          # PQ 子向量数量")
    print("    --nbits 8 \\                       # PQ 编码位数")
    print("    --concurrency-duration 300 \\")
    print("    --num-concurrency 8,16,32,64,128")
    print("```")

def show_numactl_usage():
    """显示如何在 FAISS 命令中使用 numactl"""
    print("\n🖥️ 使用 numactl 优化 FAISS 性能")
    print("=" * 40)
    
    print("如果你想像 Milvus 命令一样使用 numactl 来绑定 NUMA 节点:")
    print("```bash")
    print("# 绑定到 NUMA 节点 0")
    print("numactl -N 0 python -m vectordb_bench.cli.vectordbbench faisslocalhnsw \\")
    print("    --case-type Performance1536D50K \\")
    print("    --m 30 \\")
    print("    --ef-construction 360 \\")
    print("    --ef-search 100 \\")
    print("    --concurrency-duration 300 \\")
    print("    --num-concurrency 8,16,32,64,128")
    print("```")
    
    print("\n💡 NUMA 优化建议:")
    print("• 绑定到特定 NUMA 节点可以提高内存访问性能")
    print("• 对于大规模向量索引特别有效")
    print("• 可以减少跨 NUMA 节点的内存访问延迟")

def show_environment_setup():
    """显示环境配置"""
    print("\n⚙️ 环境配置")
    print("=" * 20)
    
    print("设置数据集路径:")
    print("```bash")
    print("export DATASET_LOCAL_DIR='/nas/yvan.chen/milvus/dataset'")
    print("```")
    
    print("\n检查可用的测试用例:")
    print("```bash")
    print("python -m vectordb_bench.cli.vectordbbench faisslocalhnsw --help | grep case-type")
    print("```")

def show_result_analysis():
    """显示结果分析方法"""
    print("\n📊 结果分析")
    print("=" * 15)
    
    print("测试结果通常保存在 results/ 目录:")
    print("```bash")
    print("# 查看最新结果")
    print("ls -la results/ | head -10")
    print("")
    print("# 查看 JSON 格式的详细结果")
    print("find results/ -name '*.json' -type f -exec ls -la {} \\; | tail -5")
    print("")
    print("# 分析特定测试的结果")
    print("cat results/latest_result.json | jq '.results[].metrics'")
    print("```")

def create_sample_script():
    """创建示例运行脚本"""
    print("\n📝 完整的示例脚本")
    print("=" * 25)
    
    script_content = '''#!/bin/bash
# FAISS 基准测试脚本 - 类似于你的 Milvus 命令

# 设置环境
export DATASET_LOCAL_DIR="/nas/yvan.chen/milvus/dataset"

# 获取时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "🎯 开始 FAISS 基准测试 - $TIMESTAMP"

# 使用 numactl 绑定 NUMA 节点 (类似你的 Milvus 命令)
numactl -N 0 python -m vectordb_bench.cli.vectordbbench faisslocalhnsw \\
    --case-type Performance1536D50K \\
    --m 30 \\
    --ef-construction 360 \\
    --ef-search 100 \\
    --concurrency-duration 300 \\
    --num-concurrency 8,16,32,64,128 \\
    --k 100 \\
    --db-label "faiss_hnsw_$TIMESTAMP" \\
    --task-label "FAISS_HNSW_Performance_$TIMESTAMP"

echo "✅ FAISS 基准测试完成"
'''
    
    print("保存为 `run_faiss_like_milvus.sh`:")
    print("```bash")
    print(script_content)
    print("```")
    
    # 实际创建文件
    with open('/home/<USER>/VectorDBBench/run_faiss_like_milvus.sh', 'w') as f:
        f.write(script_content)
    
    print("✅ 脚本已创建: /home/<USER>/VectorDBBench/run_faiss_like_milvus.sh")
    print("执行方法: chmod +x run_faiss_like_milvus.sh && ./run_faiss_like_milvus.sh")

if __name__ == "__main__":
    show_command_comparison()
    show_faiss_commands() 
    show_numactl_usage()
    show_environment_setup()
    show_result_analysis()
    create_sample_script()
    
    print("\n🎉 总结:")
    print("• FAISS 完全支持类似 Milvus 的命令行基准测试")
    print("• 使用 faisslocalhnsw/faisslocalivfflat/faisslocalivfpq 命令")
    print("• 可以配合 numactl 使用来优化性能")
    print("• 参数名称略有不同，但功能完全对应")
