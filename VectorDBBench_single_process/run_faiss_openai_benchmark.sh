#!/bin/bash

# 设置环境变量
export DATASET_LOCAL_DIR="/nas/yvan.chen/milvus/dataset"

echo "=== 开始 FAISS 基准测试 ==="
echo "数据集路径: $DATASET_LOCAL_DIR"

# 测试 OpenAI 小数据集 (50K)
echo ""
echo "1. 测试 OpenAI Small 50K 数据集..."
vectordbbench batchcli --batch-config-file faiss_openai_benchmark.yml

echo ""
echo "=== FAISS 基准测试完成 ==="

# 检查结果文件
echo ""
echo "结果文件位置:"
ls -la results/ 2>/dev/null || echo "暂无结果文件生成"

echo ""
echo "日志文件:"
ls -la logs/ 2>/dev/null || echo "暂无日志文件"
