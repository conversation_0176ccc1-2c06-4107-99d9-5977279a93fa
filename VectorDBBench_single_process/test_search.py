#!/usr/bin/env python3
"""
测试FAISS服务器搜索功能
"""

import requests
import json
import numpy as np
import time

def test_search():
    """测试搜索功能"""
    print("🔍 测试FAISS服务器搜索功能...")
    
    # 生成768维随机查询向量
    query = np.random.random(768).tolist()
    data = {
        'query': query, 
        'topk': 5
    }
    
    print(f"📊 查询向量维度: {len(query)}")
    print(f"📊 TopK: {data['topk']}")
    
    try:
        # 发送搜索请求
        print("🚀 发送搜索请求...")
        start_time = time.time()
        
        response = requests.post(
            'http://localhost:8005/search', 
            json=data, 
            timeout=30
        )
        
        search_time = time.time() - start_time
        
        print(f"⏱️  搜索耗时: {search_time:.3f} 秒")
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 搜索成功!")
            print(f"📊 返回结果数量: {len(result.get('ids', []))}")
            print(f"📊 最近邻ID: {result.get('ids', [])[:3]}...")
            print(f"📊 距离: {result.get('distances', [])[:3]}...")
        else:
            print(f"❌ 搜索失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_status():
    """测试状态接口"""
    print("\n📊 测试状态接口...")
    
    try:
        response = requests.get('http://localhost:8005/status', timeout=10)
        if response.status_code == 200:
            status = response.json()
            print("✅ 状态查询成功!")
            print(f"📊 向量数量: {status.get('total_vectors', 0):,}")
            print(f"📊 索引类型: {status.get('index_type', 'Unknown')}")
            print(f"📊 维度: {status.get('dimension', 0)}")
        else:
            print(f"❌ 状态查询失败: {response.text}")
    except Exception as e:
        print(f"❌ 状态查询异常: {e}")

if __name__ == "__main__":
    test_status()
    test_search()
