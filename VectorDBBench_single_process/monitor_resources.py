#!/usr/bin/env python3
"""
资源使用监控脚本
实时监控FAISS服务器资源使用情况
"""

import time
import psutil
import requests
from resource_manager import ResourceManager

def monitor_server_resources():
    """监控服务器资源使用"""
    try:
        # 检查服务器状态
        response = requests.get("http://localhost:8001/health", timeout=5)
        server_status = "🟢 在线" if response.status_code == 200 else "🔴 异常"
    except:
        server_status = "🔴 离线"
    
    # 系统资源监控
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    
    print(f"""
📊 资源监控报告 - {time.strftime('%Y-%m-%d %H:%M:%S')}
{'='*50}
🚀 服务器状态: {server_status}
💻 CPU使用率: {cpu_percent:.1f}%
🧠 内存使用率: {memory.percent:.1f}% ({memory.used/1024**3:.1f}GB/{memory.total/1024**3:.1f}GB)
⚡ 可用内存: {memory.available/1024**3:.1f}GB
🔧 CPU核心数: {psutil.cpu_count()}
""")

def continuous_monitor(interval=10):
    """持续监控"""
    print("🔍 开始监控服务器资源使用...")
    try:
        while True:
            monitor_server_resources()
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\n⏹️ 监控已停止")

if __name__ == "__main__":
    continuous_monitor()
