#!/usr/bin/env python3
"""
测试VectorDBBench客户端和FAISS服务器使用相同的768维向量
"""

import os
import sys
import requests
import json
import numpy as np
import time

sys.path.insert(0, '/home/<USER>/VectorDBBench')

def test_server_status():
    """检查FAISS服务器状态"""
    print("🔍 检查FAISS服务器状态...")
    
    try:
        response = requests.get('http://127.0.0.1:8001/status', timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 服务器运行中")
            print(f"   维度: {status.get('dimension', 'N/A')}")
            print(f"   索引类型: {status.get('index_type', 'N/A')}")
            print(f"   向量数量: {status.get('total_vectors', 'N/A'):,}")
            return status
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 连接服务器失败: {e}")
        return None

def test_768_dimension_vectors():
    """测试768维向量搜索"""
    print("\n🧪 测试768维向量搜索...")
    
    # 生成768维测试向量
    test_vectors = []
    for i in range(5):
        vector = np.random.random(768).astype(np.float32)
        vector = vector / np.linalg.norm(vector)  # 归一化
        test_vectors.append(vector.tolist())
    
    print(f"📊 生成了 {len(test_vectors)} 个768维测试向量")
    
    # 测试搜索
    search_url = 'http://127.0.0.1:8001/search'
    success_count = 0
    
    for i, vector in enumerate(test_vectors):
        try:
            search_request = {
                'query': vector,
                'topk': 10
            }
            
            start_time = time.time()
            response = requests.post(search_url, json=search_request, timeout=10)
            duration = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                result = response.json()
                distances = result.get('distances', [])
                indices = result.get('indices', [])
                
                print(f"  ✅ 搜索 {i+1}: {duration:.2f}ms, 返回{len(indices)}个结果")
                success_count += 1
            else:
                print(f"  ❌ 搜索 {i+1} 失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"  ❌ 搜索 {i+1} 异常: {e}")
    
    print(f"\n📈 成功搜索: {success_count}/{len(test_vectors)}")
    return success_count == len(test_vectors)

def test_vectordb_case_dimensions():
    """检查VectorDBBench case维度配置"""
    print("\n📋 检查VectorDBBench case维度配置...")
    
    try:
        from vectordb_bench.backend.cases import Performance768D1M, Performance1536D50K
        
        # Performance768D1M case
        case_768 = Performance768D1M()
        print(f"✅ Performance768D1M:")
        print(f"   数据集: {case_768.dataset.data.name}")
        print(f"   维度: {case_768.dataset.data.dim}")
        print(f"   大小: {case_768.dataset.data.size:,}")
        
        # Performance1536D50K case  
        case_1536 = Performance1536D50K()
        print(f"✅ Performance1536D50K:")
        print(f"   数据集: {case_1536.dataset.data.name}")
        print(f"   维度: {case_1536.dataset.data.dim}")
        print(f"   大小: {case_1536.dataset.data.size:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查case配置失败: {e}")
        return False

def run_vectordb_benchmark_768d():
    """运行VectorDBBench Performance768D1M测试"""
    print("\n🚀 运行VectorDBBench Performance768D1M测试...")
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
        from vectordb_bench.backend.cases import CaseType
        from vectordb_bench.backend.clients.api import MetricType
        
        # 配置远程FAISS
        db_config = FaissConfig(
            host='127.0.0.1',
            port=8001,
            index_type='HNSW',
            db_label='test_768d_match'
        )
        
        db_case_config = FaissDBCaseConfig(
            metric_type=MetricType.COSINE,
            m=16,
            ef_construction=200,
            ef_search=64
        )
        
        print(f"📡 连接服务器: {db_config.host}:{db_config.port}")
        print(f"🎯 使用case: Performance768D1M (768维)")
        print(f"⚙️ 索引配置: {db_case_config.index_type}")
        
        # 运行测试
        result = run(
            db=DB.Faiss,
            db_config=db_config,
            db_case_config=db_case_config,
            case_type=CaseType.Performance768D1M,  # 使用768维case
            run_name="test_768d_dimension_match",
            num_concurrency=[1, 2],  # 少量并发以便快速测试
            concurrency_duration=30,  # 短时间测试
            k=10
        )
        
        print(f"✅ VectorDBBench测试完成: {result}")
        return True
        
    except Exception as e:
        print(f"❌ VectorDBBench测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🎯 测试VectorDBBench客户端和FAISS服务器768维向量匹配")
    print("=" * 60)
    
    # 1. 检查服务器状态
    server_status = test_server_status()
    if not server_status:
        print("❌ 服务器未启动，请先启动FAISS服务器")
        return False
    
    # 2. 检查服务器维度配置
    server_dim = server_status.get('dimension', 0)
    if server_dim != 768:
        print(f"⚠️  服务器维度不匹配: 期望768，实际{server_dim}")
        print("   请确保服务器已加载768维数据集")
    
    # 3. 测试768维向量搜索
    search_ok = test_768_dimension_vectors()
    if not search_ok:
        print("❌ 768维向量搜索测试失败")
        return False
    
    # 4. 检查VectorDBBench case配置
    case_ok = test_vectordb_case_dimensions()
    if not case_ok:
        print("❌ VectorDBBench case配置检查失败")
        return False
    
    # 5. 运行VectorDBBench测试
    print("\n" + "="*60)
    print("🔬 开始VectorDBBench集成测试...")
    
    benchmark_ok = run_vectordb_benchmark_768d()
    
    if benchmark_ok:
        print("\n🎊 所有测试通过！VectorDBBench客户端和FAISS服务器已使用相同的768维向量配置")
        print("\n💡 建议:")
        print("   - 确保始终使用Performance768D1M case进行768维测试")
        print("   - 或确保FAISS服务器和VectorDBBench case使用相同维度")
    else:
        print("\n❌ 测试失败，请检查配置匹配问题")
    
    return benchmark_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
