#!/usr/bin/env python3
"""
详细调试FAISS本地测试卡顿问题
"""

import sys
import os
import time
import subprocess
import threading
from pathlib import Path

def monitor_process(pid):
    """监控进程状态"""
    try:
        while True:
            result = subprocess.run(['ps', '-p', str(pid), '-o', 'pid,ppid,pcpu,pmem,etime,cmd'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ 进程 {pid} 已退出")
                break
            
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:
                print(f"🔍 进程状态: {lines[1]}")
            
            time.sleep(5)
    except KeyboardInterrupt:
        print("⏹️ 监控停止")

def check_file_operations():
    """检查文件操作"""
    print("📁 检查数据集文件状态...")
    
    dataset_paths = [
        "/home/<USER>/VectorDBBench/dataset/openai",
        "/tmp/offline_vectordb_dataset/openai"
    ]
    
    for path in dataset_paths:
        p = Path(path)
        if p.exists():
            print(f"✅ 数据集目录存在: {path}")
            for file in p.glob("**/*.parquet"):
                size = file.stat().st_size
                print(f"   📄 {file.name}: {size:,} bytes")
        else:
            print(f"❌ 数据集目录不存在: {path}")

def check_temp_directories():
    """检查临时目录"""
    print("🗂️ 检查临时目录...")
    
    temp_dirs = list(Path("/tmp").glob("faiss_vectordb_*"))
    for temp_dir in temp_dirs:
        print(f"📁 临时目录: {temp_dir}")
        for file in temp_dir.glob("*"):
            size = file.stat().st_size if file.is_file() else "dir"
            print(f"   📄 {file.name}: {size}")

def check_environment():
    """检查环境变量"""
    print("🔧 检查环境变量...")
    
    env_vars = [
        "DATASET_LOCAL_DIR",
        "AWS_PROFILE", 
        "AWS_DEFAULT_REGION",
        "AWS_ACCESS_KEY_ID",
        "AWS_SECRET_ACCESS_KEY"
    ]
    
    for var in env_vars:
        value = os.environ.get(var, "未设置")
        print(f"   {var}={value}")

def run_diagnostic():
    """运行诊断测试"""
    print("🎯 FAISS诊断测试")
    print("=" * 50)
    
    check_environment()
    print()
    
    check_file_operations()
    print()
    
    check_temp_directories()
    print()
    
    print("🚀 启动最小化FAISS测试...")
    
    # 启动测试进程
    cmd = [
        "vectordbbench", "faisslocalhnsw",
        "--case-type", "Performance1536D50K",
        "--m", "16",
        "--ef-construction", "200", 
        "--ef-search", "100",
        "--concurrency-duration", "10",  # 缩短测试时间
        "--num-concurrency", "1"
    ]
    
    print(f"🔧 命令: {' '.join(cmd)}")
    
    try:
        # 使用Popen获取实时输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print(f"📊 进程PID: {process.pid}")
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_process, args=(process.pid,))
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # 读取输出
        line_count = 0
        start_time = time.time()
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                line_count += 1
                elapsed = time.time() - start_time
                print(f"[{elapsed:.1f}s] {output.strip()}")
                
                # 如果超过60秒没有新输出，认为卡住了
                if line_count > 0 and elapsed > 60:
                    print("⚠️ 超过60秒无输出，可能卡住了")
                    process.terminate()
                    break
        
        # 等待进程结束
        return_code = process.wait()
        print(f"🔚 进程结束，返回码: {return_code}")
        
    except KeyboardInterrupt:
        print("⏹️ 用户中断测试")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"❌ 执行错误: {e}")

if __name__ == "__main__":
    run_diagnostic()
