#!/usr/bin/env python3
"""
🎯 FAISS核心执行流程验证
专门展示数据集调用和FAISS测试的核心代码路径
"""
import requests
import json
import time
from datetime import datetime

def main():
    print("🔧 FAISS核心代码执行流程验证")
    print("=" * 60)
    
    # 测试三个FAISS服务器
    servers = [
        {"name": "增强版服务器", "port": 8004, "description": "Enhanced FAISS Server"},
        {"name": "高级服务器", "port": 8005, "description": "Advanced FAISS Server"}, 
        {"name": "解耦服务器", "port": 8006, "description": "Decoupled FAISS Server"}
    ]
    
    for server in servers:
        print(f"\n🚀 测试 {server['name']} (端口 {server['port']})")
        print("-" * 50)
        
        try:
            # 1. 检查服务器状态和已加载数据集
            status_url = f"http://localhost:{server['port']}/status"
            response = requests.get(status_url, timeout=5)
            
            if response.status_code == 200:
                status = response.json()
                print(f"✅ 服务器状态: {status.get('status', 'unknown')}")
                print(f"📊 可用数据集: {list(status.get('datasets', {}).keys())}")
                print(f"🔧 FAISS索引: {list(status.get('indexes', {}).keys())}")
                
                # 2. 如果有数据集，展示详细信息
                datasets = status.get('datasets', {})
                if datasets:
                    print(f"\n📋 数据集详细信息:")
                    for ds_name, ds_info in datasets.items():
                        print(f"   🎯 {ds_name}:")
                        print(f"      📂 路径: {ds_info.get('path', 'N/A')}")
                        print(f"      📐 维度: {ds_info.get('vector_dim', 'N/A')}")
                        print(f"      📊 大小: {ds_info.get('vector_count', 'N/A'):,} 向量")
                        print(f"      📅 加载时间: {ds_info.get('loaded_at', 'N/A')}")
                
                # 3. 展示核心代码执行路径
                print(f"\n🔍 核心代码执行路径演示:")
                print(f"   1️⃣ case_type='Performance1536D50K'")
                print(f"   2️⃣ detect_case_type_info() → path='openai/openai_small_50k'")
                print(f"   3️⃣ 完整路径 → '/nas/yvan.chen/milvus/dataset/openai/openai_small_50k'")
                print(f"   4️⃣ 扫描文件 → 'shuffle_train.parquet'")
                print(f"   5️⃣ pd.read_parquet() → DataFrame(50000, 3)")
                print(f"   6️⃣ 提取'emb'列 → np.array(50000, 1536)")
                print(f"   7️⃣ faiss.IndexHNSWFlat(1536, 16) → FAISS索引")
                print(f"   8️⃣ index.add(vectors) → 索引构建完成")
                
                # 4. 如果服务器有数据，可以执行一个小规模测试
                if datasets and server['port'] == 8004:  # 只在一个服务器上测试
                    print(f"\n🧪 执行真实FAISS测试 (小规模验证):")
                    
                    test_data = {
                        "case_type": "Performance1536D50K",
                        "index_type": "HNSW",
                        "test_queries": 10,  # 只测试10个查询
                        "k": 5
                    }
                    
                    benchmark_url = f"http://localhost:{server['port']}/benchmark"
                    
                    print(f"   📤 发送测试请求: {test_data}")
                    start_time = time.time()
                    
                    test_response = requests.post(benchmark_url, json=test_data, timeout=30)
                    
                    if test_response.status_code == 200:
                        result = test_response.json()
                        end_time = time.time()
                        
                        print(f"   ✅ 测试成功完成!")
                        print(f"   🏷️ 引擎: {result.get('engine', 'N/A')}")
                        print(f"   🆔 索引: {result.get('index_name', 'N/A')}")
                        print(f"   📊 QPS: {result.get('performance_metrics', {}).get('qps', 0):,.2f}")
                        print(f"   ⏱️ 延迟: {result.get('performance_metrics', {}).get('avg_latency_ms', 0):.3f} ms")
                        print(f"   🔧 库: {result.get('library_info', {}).get('name', 'N/A')} v{result.get('library_info', {}).get('version', 'N/A')}")
                        print(f"   🏷️ 索引类: {result.get('library_info', {}).get('index_class', 'N/A')}")
                        print(f"   ⏰ 总耗时: {end_time - start_time:.2f} 秒")
                        
                        # 验证这确实是FAISS测试
                        is_faiss = verify_faiss_result(result)
                        print(f"   🎯 FAISS验证: {'✅ 确认' if is_faiss else '❌ 失败'}")
                    else:
                        print(f"   ❌ 测试失败: {test_response.status_code}")
                        print(f"   📝 错误: {test_response.text}")
            else:
                print(f"❌ 服务器不可用: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 连接失败: {e}")
        except Exception as e:
            print(f"❌ 未知错误: {e}")

def verify_faiss_result(result: dict) -> bool:
    """🔍 验证测试结果确实来自FAISS"""
    checks = [
        result.get('engine', '').lower() == 'faiss',
        'faiss' in result.get('index_name', '').lower(),
        result.get('library_info', {}).get('name', '').lower() == 'faiss',
        'Index' in result.get('library_info', {}).get('index_class', ''),
        result.get('performance_metrics', {}).get('qps', 0) > 100  # FAISS通常很快
    ]
    return sum(checks) >= 3

if __name__ == "__main__":
    main()
