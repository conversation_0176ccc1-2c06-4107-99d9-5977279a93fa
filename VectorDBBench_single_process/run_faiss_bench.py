import subprocess
import threading
import time
import sys
import os

def start_faiss_server():
    print("[*] 启动 FAISS 服务端...")
    cmd = [
        sys.executable, "-m", "uvicorn",
        "vectordb_bench.backend.clients.faiss.server:app",
        "--host", "0.0.0.0",
        "--port", "8002"
    ]
    env = os.environ.copy()
    env["PYTHONPATH"] = "."
    subprocess.run(cmd, env=env)

def run_benchmark():
    print("[*] 运行 VectorDBBench benchmark (通过配置文件)...")
    config_path = "config-files/sample_config.yml"
    cmd = [
        sys.executable, "-m",
        "vectordb_bench.cli.vectordbbench",
        "--config", config_path
    ]
    subprocess.run(cmd)

if __name__ == "__main__":
    # 在后台线程启动服务
    server_thread = threading.Thread(target=start_faiss_server, daemon=True)
    server_thread.start()

    # 等待服务启动
    time.sleep(5)

    # 跑 benchmark
    run_benchmark()

