import requests
import json


base_url = "http://localhost:8002"

# 可配置参数
case_types = [
    "Performance1536D50K",
    "Performance1536D500K",
    "Performance768D1M"
]
index_types = ["Flat", "HNSW", "IVF1024"]
k = 100
test_queries = 1000
metric_type = "COSINE"

for case_type in case_types:
    print(f"\n=== 加载数据集: {case_type} ===")
    resp = requests.post(f"{base_url}/load_dataset", json={"case_type": case_type})
    print(json.dumps(resp.json(), indent=2, ensure_ascii=False))

    for index_type in index_types:
        print(f"\n=== 创建索引: {case_type} | {index_type} ===")
        resp = requests.post(f"{base_url}/create_index_with_dataset", json={
            "case_type": case_type,
            "index_type": index_type
        })
        print(json.dumps(resp.json(), indent=2, ensure_ascii=False))

        print(f"\n=== 运行基准测试: {case_type} | {index_type} ===")
        resp = requests.post(f"{base_url}/benchmark_test", json={
            "case_type": case_type,
            "index_type": index_type,
            "k": k,
            "test_queries": test_queries,
            "metric_type": metric_type
        })
        print(json.dumps(resp.json(), indent=2, ensure_ascii=False))

print("\n=== 服务器状态 ===")
resp = requests.get(f"{base_url}/server_status")
print(json.dumps(resp.json(), indent=2, ensure_ascii=False))
