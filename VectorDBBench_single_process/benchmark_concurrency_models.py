#!/usr/bin/env python3.11
"""
并发模型性能对比测试
测试不同的进程×线程配置组合
"""

import subprocess
import time
import requests
import json
import argparse
from pathlib import Path

def run_faiss_server(config_name, args_str, timeout=60):
    """启动FAISS服务器"""
    print(f"\n🚀 启动配置: {config_name}")
    print(f"   参数: {args_str}")
    
    # 停止现有服务
    subprocess.run(["pkill", "-f", "smart_faiss_server"], capture_output=True)
    time.sleep(3)
    
    # 启动新服务
    cmd = f"numactl -C 0-15 python3.11 smart_faiss_server.py --host 0.0.0.0 --port 8005 {args_str}"
    
    process = subprocess.Popen(
        cmd.split(),
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        cwd="/home/<USER>/VectorDBBench"
    )
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    for i in range(timeout):
        try:
            response = requests.get("http://localhost:8005/status", timeout=2)
            if response.status_code == 200:
                print(f"✅ 服务启动成功 (耗时: {i+1}秒)")
                return process
        except:
            time.sleep(1)
    
    print("❌ 服务启动失败")
    return None

def run_benchmark(config_name, concurrency="4,8"):
    """运行性能测试"""
    print(f"🏃 运行性能测试: {config_name}")
    
    cmd = [
        "python3.11", "-m", "vectordb_bench.cli.vectordbbench",
        "faissremote",
        "--uri", "http://***********:8005",
        "--case-type", "Performance768D1M",
        "--index-type", "HNSW",
        "--m", "30",
        "--ef-construction", "360",
        "--ef-search", "100",
        "--concurrency-duration", "15",  # 缩短测试时间
        "--num-concurrency", concurrency
    ]
    
    start_time = time.time()
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        cwd="/home/<USER>/VectorDBBench",
        timeout=300
    )
    test_time = time.time() - start_time
    
    if result.returncode == 0:
        # 解析QPS结果
        output_lines = result.stdout.split('\n')
        qps_values = []
        
        for line in output_lines:
            if 'qps' in line.lower() and any(c in line for c in concurrency.split(',')):
                try:
                    # 提取QPS数值
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if 'qps' in part.lower() and i > 0:
                            qps_values.append(float(parts[i-1]))
                except:
                    continue
        
        max_qps = max(qps_values) if qps_values else 0
        
        print(f"✅ 测试完成: 最大QPS = {max_qps:.1f}")
        return {
            "config": config_name,
            "max_qps": max_qps,
            "test_time": test_time,
            "success": True,
            "qps_values": qps_values
        }
    else:
        print(f"❌ 测试失败: {result.stderr[:200]}")
        return {
            "config": config_name,
            "success": False,
            "error": result.stderr[:200]
        }

def main():
    parser = argparse.ArgumentParser(description="并发模型性能对比测试")
    parser.add_argument("--concurrency", default="4,8", 
                       help="并发数配置 (默认: 4,8)")
    parser.add_argument("--quick", action="store_true",
                       help="快速测试模式 (减少配置数量)")
    args = parser.parse_args()
    
    print("🧪 FAISS并发模型性能对比测试")
    print("=" * 50)
    
    # 测试配置
    if args.quick:
        configs = [
            ("当前配置 8×2", "--use-gunicorn --workers 8 --omp-threads 2"),
            ("平衡配置 4×4", "--use-gunicorn --workers 4 --omp-threads 4"),
            ("单进程 1×16", "--omp-threads 16"),
        ]
    else:
        configs = [
            ("平衡模式", "--use-gunicorn --concurrency-model balanced"),
            ("HTTP优化", "--use-gunicorn --concurrency-model http-optimized"),
            ("计算优化", "--use-gunicorn --concurrency-model compute-optimized"),
            ("自动配置", "--use-gunicorn --auto-config"),
            ("8进程×2线程", "--use-gunicorn --workers 8 --omp-threads 2"),
            ("4进程×4线程", "--use-gunicorn --workers 4 --omp-threads 4"),
            ("16进程×1线程", "--use-gunicorn --workers 16 --omp-threads 1"),
            ("单进程×16线程", "--omp-threads 16"),
        ]
    
    results = []
    
    for config_name, config_args in configs:
        try:
            # 启动服务
            process = run_faiss_server(config_name, config_args)
            if not process:
                continue
            
            # 运行测试
            result = run_benchmark(config_name, args.concurrency)
            if result:
                results.append(result)
            
            # 停止服务
            subprocess.run(["pkill", "-f", "smart_faiss_server"], capture_output=True)
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ 配置 {config_name} 测试异常: {e}")
    
    # 输出对比结果
    print("\n" + "=" * 70)
    print("📊 性能对比结果")
    print("=" * 70)
    
    if results:
        # 按QPS排序
        results.sort(key=lambda x: x.get('max_qps', 0), reverse=True)
        
        print(f"{'排名':<4} {'配置':<20} {'最大QPS':<12} {'测试时间':<10} {'状态'}")
        print("-" * 70)
        
        for i, result in enumerate(results, 1):
            if result["success"]:
                status = "✅ 成功"
                qps = f"{result['max_qps']:.1f}"
                test_time = f"{result['test_time']:.1f}s"
            else:
                status = "❌ 失败"
                qps = "N/A"
                test_time = "N/A"
            
            print(f"{i:<4} {result['config']:<20} {qps:<12} {test_time:<10} {status}")
        
        # 推荐配置
        if results and results[0]["success"]:
            best_config = results[0]
            print(f"\n🏆 推荐配置: {best_config['config']}")
            print(f"   最大QPS: {best_config['max_qps']:.1f}")
            print(f"   性能提升: {best_config['max_qps']/results[-1]['max_qps']:.1f}x (相比最低)")
    
    # 保存结果
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    result_file = f"concurrency_benchmark_{timestamp}.json"
    
    with open(result_file, 'w') as f:
        json.dump({
            "timestamp": timestamp,
            "concurrency": args.concurrency,
            "results": results
        }, f, indent=2)
    
    print(f"\n💾 结果已保存到: {result_file}")
    
    # 恢复默认配置
    print("\n🔄 恢复默认配置...")
    subprocess.run(["pkill", "-f", "smart_faiss_server"], capture_output=True)
    time.sleep(2)
    
    restore_process = run_faiss_server("默认配置", "--use-gunicorn --workers 8 --omp-threads 2")
    if restore_process:
        print("✅ 默认配置已恢复")

if __name__ == "__main__":
    main()
