#!/usr/bin/env python3
"""
专门监控FAISS服务器CPU使用情况的脚本
"""

import time
import threading
import requests
import psutil
import random
from concurrent.futures import ThreadPoolExecutor

def get_server_process():
    """获取smart_faiss_server.py进程"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'smart_faiss_server.py' in ' '.join(proc.info['cmdline']):
                return psutil.Process(proc.info['pid'])
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def monitor_cpu_detailed(duration=30):
    """详细监控CPU使用情况"""
    server_process = get_server_process()
    if not server_process:
        print("❌ 找不到服务器进程")
        return
    
    print(f"📊 监控服务器进程:")
    print(f"   PID: {server_process.pid}")
    print(f"   CPU亲和性: {server_process.cpu_affinity()}")
    print(f"   线程数: {server_process.num_threads()}")
    print(f"   内存使用: {server_process.memory_info().rss / 1024 / 1024:.1f} MB")
    print(f"\n开始 {duration} 秒CPU监控...")
    print("-" * 120)
    
    start_time = time.time()
    sample_count = 0
    max_observed_cores = 0
    max_server_cpu = 0
    
    while time.time() - start_time < duration:
        try:
            # 服务器进程CPU使用率
            server_cpu = server_process.cpu_percent()
            max_server_cpu = max(max_server_cpu, server_cpu)
            
            # 系统每个CPU核心使用率
            per_cpu = psutil.cpu_percent(percpu=True, interval=0.3)
            
            # 分析前16个核心（我们设置的亲和性范围）
            cores_0_15 = per_cpu[:16]
            active_cores = [i for i, usage in enumerate(cores_0_15) if usage > 10.0]
            high_usage_cores = [i for i, usage in enumerate(cores_0_15) if usage > 30.0]
            
            max_observed_cores = max(max_observed_cores, len(active_cores))
            
            sample_count += 1
            
            # 显示监控结果
            print(f"[{sample_count:3d}] 服务器CPU: {server_cpu:6.1f}% | "
                  f"活跃核心(>10%): {len(active_cores):2d} | "
                  f"高负载核心(>30%): {len(high_usage_cores):2d}")
            
            # 如果有多个活跃核心，显示详细信息
            if len(active_cores) > 1:
                core_info = [f"核心{i}:{cores_0_15[i]:4.1f}%" for i in active_cores]
                print(f"     多核活跃: {' '.join(core_info)}")
            
            # 检查是否确实在使用多核
            if len(active_cores) >= 2:
                print(f"     ✅ 检测到多核心使用! 同时活跃: {len(active_cores)} 个核心")
            
        except psutil.NoSuchProcess:
            print("❌ 服务器进程已退出")
            break
        except Exception as e:
            print(f"❌ 监控错误: {e}")
            break
    
    print(f"\n📊 监控总结:")
    print(f"   最大服务器CPU使用率: {max_server_cpu:.1f}%")
    print(f"   最多同时活跃核心数: {max_observed_cores}")
    if max_observed_cores <= 1:
        print("   ⚠️  大部分时间只使用1个CPU核心")
    else:
        print(f"   ✅ 检测到多核心使用，最多 {max_observed_cores} 个核心同时活跃")

def create_test_index():
    """创建测试索引"""
    print("🔧 创建测试索引...")
    data = {
        "dimension": 768,
        "index_type": "Flat"  # 使用最简单的索引类型
    }
    
    try:
        response = requests.post("http://localhost:8001/create_index", json=data, timeout=10)
        if response.status_code == 200:
            print("✅ 索引创建成功")
            return True
        else:
            print(f"❌ 索引创建失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 索引创建出错: {e}")
        return False

def insert_bulk_data():
    """批量插入测试数据"""
    print("📝 批量插入测试数据...")
    
    # 生成测试向量
    vectors = []
    for i in range(1000):
        vector = [random.random() for _ in range(768)]
        vectors.append({
            "id": str(i),
            "vector": vector
        })
    
    data = {"vectors": vectors}
    
    try:
        response = requests.post("http://localhost:8001/insert_bulk", json=data, timeout=30)
        if response.status_code == 200:
            print(f"✅ 成功插入 {len(vectors)} 个向量")
            return True
        else:
            print(f"❌ 批量插入失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 批量插入出错: {e}")
        return False

def send_search_request():
    """发送搜索请求"""
    query_vector = [random.random() for _ in range(768)]
    data = {
        "query_vector": query_vector,
        "k": 10
    }
    
    try:
        response = requests.post("http://localhost:8001/search", json=data, timeout=5)
        return response.status_code == 200
    except:
        return False

def run_stress_test():
    """运行压力测试，同时监控CPU"""
    print("\n🚀 开始压力测试")
    print("="*60)
    
    # 启动CPU监控线程
    monitor_thread = threading.Thread(target=monitor_cpu_detailed, args=(25,))
    monitor_thread.daemon = True
    monitor_thread.start()
    
    time.sleep(2)  # 让监控先启动
    
    # 并发发送搜索请求
    success_count = 0
    total_count = 0
    
    def worker():
        nonlocal success_count, total_count
        for _ in range(50):  # 每个线程发送50个请求
            if send_search_request():
                success_count += 1
            total_count += 1
            time.sleep(0.05)  # 50ms间隔
    
    print("🎯 启动16个并发线程，每个线程发送50个搜索请求...")
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=16) as executor:
        futures = [executor.submit(worker) for _ in range(16)]
        
        # 等待所有任务完成
        for future in futures:
            future.result()
    
    duration = time.time() - start_time
    
    print(f"\n📊 压力测试结果:")
    print(f"   总请求数: {total_count}")
    print(f"   成功请求: {success_count}")
    print(f"   测试耗时: {duration:.2f}秒")
    print(f"   平均QPS: {success_count / duration:.2f}")
    
    # 等待监控线程完成
    time.sleep(3)

def main():
    print("🔧 FAISS服务器CPU多核使用情况分析")
    print("="*60)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行")
            return
        print("✅ 服务器运行正常")
        print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return
    
    # 创建索引
    if not create_test_index():
        print("❌ 索引创建失败，可能已存在索引，继续测试...")
    
    # 插入数据
    if not insert_bulk_data():
        print("❌ 数据插入失败，可能已有数据，继续测试...")
    
    # 运行压力测试并监控CPU
    run_stress_test()
    
    print("\n🎉 测试完成!")
    print("💡 查看上面的CPU监控输出：")
    print("   - 如果只看到1个活跃核心，说明确实存在单核限制")
    print("   - 如果看到多个活跃核心，说明多核优化生效了")

if __name__ == "__main__":
    main()
