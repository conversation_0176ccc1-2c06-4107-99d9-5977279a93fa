#!/bin/bash
# filepath: /home/<USER>/VectorDBBench/setup_cgroup_faiss.sh

echo "为FAISS服务设置Cgroup资源限制 (16C64G)"

# 1. 创建专用cgroup
sudo mkdir -p /sys/fs/cgroup/faiss-service

# 2. 设置CPU限制 (16核心)
echo "0-15" | sudo tee /sys/fs/cgroup/faiss-service/cpuset.cpus
echo "0" | sudo tee /sys/fs/cgroup/faiss-service/cpuset.mems  # NUMA节点

# 3. 设置内存限制 (64GB)
echo "68719476736" | sudo tee /sys/fs/cgroup/faiss-service/memory.max  # 64GB

# 4. 设置CPU权重 (高优先级)
echo "10000" | sudo tee /sys/fs/cgroup/faiss-service/cpu.weight  # 最高权重


echo "Cgroup配置完成"
