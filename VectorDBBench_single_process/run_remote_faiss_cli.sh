#!/bin/bash
"""
VectorDBBench 远程 FAISS 命令行基准测试示例
现在支持 --uri 参数，类似于 Milvus 的使用方式！
"""

echo "🎯 VectorDBBench 远程 FAISS 基准测试"
echo "支持 --uri 参数，类似 Milvus 命令！"
echo "========================================="

# 设置数据集路径环境变量
export DATASET_LOCAL_DIR="/nas/yvan.chen/milvus/dataset"

echo "📁 数据集路径: $DATASET_LOCAL_DIR"
echo ""

# 对比展示：Milvus vs 远程 FAISS
echo "📊 命令对比:"
echo ""

echo "🔵 你的 Milvus 命令:"
echo "numactl -N 0 vectordbbench milvushnsw \\"
echo "    --uri 'http://***********:19530' \\"
echo "    --case-type Performance1536D50K \\"
echo "    --m 30 \\"
echo "    --ef-construction 360 \\"
echo "    --ef-search 100 \\"
echo "    --concurrency-duration 300 \\"
echo "    --num-concurrency 8,16,32,64,128"
echo ""

echo "🟢 对应的远程 FAISS 命令:"
echo "numactl -N 0 python -m vectordb_bench.cli.vectordbbench faissremote \\"
echo "    --uri 'http://***********:8002' \\"
echo "    --case-type Performance1536D50K \\"
echo "    --index-type Flat \\"
echo "    --concurrency-duration 300 \\"
echo "    --num-concurrency 8,16,32,64,128 \\"
echo "    --k 100"
echo ""

echo "================================="
echo ""

# 1. 远程 FAISS Flat 索引基准测试
echo "🚀 1. 远程 FAISS Flat 索引测试"
echo "============================"

FAISS_REMOTE_FLAT_CMD="numactl -N 0 python -m vectordb_bench.cli.vectordbbench faissremote \
    --uri 'http://***********:8002' \
    --case-type Performance1536D50K \
    --index-type Flat \
    --concurrency-duration 300 \
    --num-concurrency 8,16,32,64,128 \
    --k 100 \
    --db-label remote_faiss_flat_$(date +%Y%m%d_%H%M%S) \
    --task-label Remote_FAISS_Flat_Performance_Test"

echo "执行命令:"
echo "$FAISS_REMOTE_FLAT_CMD"
echo ""

# 检查是否需要启动 FAISS 服务器
echo "📋 注意事项:"
echo "• 确保远程 FAISS 服务器正在运行在 ***********:8002"
echo "• 如果服务器未运行，请先启动:"
echo "  python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002"
echo ""

# 询问是否执行
read -p "是否执行远程 FAISS Flat 测试? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "正在执行远程 FAISS Flat 测试..."
    eval $FAISS_REMOTE_FLAT_CMD
else
    echo "跳过 Flat 索引测试"
fi

echo ""
echo "================================="
echo ""

# 2. 远程 FAISS IVF 索引基准测试
echo "🚀 2. 远程 FAISS IVF 索引测试"
echo "============================="

FAISS_REMOTE_IVF_CMD="numactl -N 0 python -m vectordb_bench.cli.vectordbbench faissremote \
    --uri 'http://***********:8002' \
    --case-type Performance1536D50K \
    --index-type IVF1024 \
    --concurrency-duration 300 \
    --num-concurrency 8,16,32,64,128 \
    --k 100 \
    --db-label remote_faiss_ivf_$(date +%Y%m%d_%H%M%S) \
    --task-label Remote_FAISS_IVF_Performance_Test"

echo "执行命令:"
echo "$FAISS_REMOTE_IVF_CMD"
echo ""

# 询问是否执行
read -p "是否执行远程 FAISS IVF 测试? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "正在执行远程 FAISS IVF 测试..."
    eval $FAISS_REMOTE_IVF_CMD
else
    echo "跳过 IVF 索引测试"
fi

echo ""
echo "================================="
echo ""

# 3. 显示可用的索引类型
echo "📊 可用的远程 FAISS 索引类型:"
echo "• Flat      - 暴力搜索，精确结果"
echo "• IVF1024   - IVF 索引，1024 个聚类中心"
echo "• IVF2048   - IVF 索引，2048 个聚类中心"
echo "• IVF4096   - IVF 索引，4096 个聚类中心"
echo ""

# 4. 显示服务器启动命令
echo "🌐 FAISS 服务器启动命令:"
echo "在目标服务器 (***********) 上运行:"
echo "```bash"
echo "cd /path/to/VectorDBBench"
echo "pip install fastapi uvicorn"
echo "python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002"
echo "```"
echo ""

# 5. 验证连接
echo "🔍 验证远程服务器连接:"
echo "curl http://***********:8002/docs"
echo ""

echo "🎉 远程 FAISS 基准测试脚本完成!"
echo ""
echo "📊 主要特点:"
echo "• ✅ 支持 --uri 参数，类似 Milvus"
echo "• ✅ 支持 numactl 性能优化"
echo "• ✅ 支持多种 FAISS 索引类型"
echo "• ✅ 支持自定义并发度和测试时长"
echo "• ✅ 完全兼容 VectorDBBench 框架"
