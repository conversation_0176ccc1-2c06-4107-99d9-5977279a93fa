#!/usr/bin/env python3
"""
验证VectorDBBench创建的大索引文件
"""

import sys
import os
sys.path.append('/home/<USER>/VectorDBBench')

def verify_large_index():
    """验证大的索引文件"""
    
    print("🔍 验证VectorDBBench创建的大索引文件")
    print("=" * 50)
    
    # 大索引文件路径
    large_index_dir = "/tmp/faiss_vectordb_8e00836292b16d00"
    index_file = os.path.join(large_index_dir, "faiss.index")
    metadata_file = os.path.join(large_index_dir, "metadata.pkl")
    
    if not os.path.exists(index_file):
        print(f"❌ 索引文件不存在: {index_file}")
        return
    
    print(f"✅ 索引文件存在")
    print(f"   大小: {os.path.getsize(index_file) / (1024*1024):.1f} MB")
    print(f"   元数据大小: {os.path.getsize(metadata_file) / 1024:.1f} KB")
    
    # 尝试加载索引
    try:
        import faiss
        import pickle
        
        print("📖 加载索引...")
        index = faiss.read_index(index_file)
        print(f"   索引类型: {type(index)}")
        print(f"   向量数量: {index.ntotal}")
        print(f"   向量维度: {index.d}")
        
        print("📖 加载元数据...")
        with open(metadata_file, 'rb') as f:
            metadata = pickle.load(f)
            print(f"   ID映射数量: {len(metadata.get('id_mapping', {}))}")
            print(f"   度量类型: {metadata.get('metric_type', 'N/A')}")
            print(f"   索引类型: {metadata.get('index_type', 'N/A')}")
        
        # 执行一个简单的搜索测试
        print("🔍 执行搜索测试...")
        import numpy as np
        
        # 生成随机查询向量
        query_vector = np.random.random((1, 1536)).astype(np.float32)
        
        # 归一化（COSINE距离）
        faiss.normalize_L2(query_vector)
        
        # 搜索
        D, I = index.search(query_vector, 10)
        
        print(f"   搜索结果ID: {I[0]}")
        print(f"   搜索距离: {D[0]}")
        print(f"   ✅ 搜索成功！")
        
    except Exception as e:
        print(f"❌ 加载/搜索失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_large_index()
