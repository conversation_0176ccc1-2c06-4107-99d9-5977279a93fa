#!/usr/bin/env python3
"""向服务器插入大量测试数据以创建缓存"""

import requests
import numpy as np
import json

def insert_bulk_data():
    """插入大量数据到服务器"""
    print("📝 向服务器插入大量测试数据...")
    
    # 生成2000个1536维向量
    vectors = np.random.random((2000, 1536)).tolist()
    
    # 分批插入，每批500个
    batch_size = 500
    total_inserted = 0
    
    for i in range(0, len(vectors), batch_size):
        batch = vectors[i:i + batch_size]
        
        try:
            resp = requests.post(
                "http://10.1.180.71:8000/insert_bulk",
                json={"vectors": batch},
                timeout=60
            )
            
            if resp.status_code == 200:
                total_inserted += len(batch)
                print(f"✅ 批次 {i//batch_size + 1}: 插入 {len(batch)} 个向量 (总计: {total_inserted})")
            else:
                print(f"❌ 批次 {i//batch_size + 1} 失败: {resp.status_code} - {resp.text}")
                
        except Exception as e:
            print(f"❌ 批次 {i//batch_size + 1} 异常: {e}")
            break
    
    print(f"🎉 完成插入 {total_inserted} 个向量")
    return total_inserted

def check_server_status():
    """检查服务器状态"""
    try:
        resp = requests.get("http://10.1.180.71:8000/status", timeout=5)
        status = resp.json()
        print(f"📊 服务器状态:")
        print(f"   - 总向量数: {status.get('total_vectors', 0)}")
        print(f"   - 索引类型: {status.get('index_type', 'Unknown')}")
        print(f"   - 维度: {status.get('dimension', 0)}")
        return status
    except Exception as e:
        print(f"❌ 无法获取服务器状态: {e}")
        return None

if __name__ == "__main__":
    print("🚀 创建缓存数据")
    print("=" * 50)
    
    # 检查初始状态
    print("🔍 检查初始服务器状态...")
    initial_status = check_server_status()
    
    if initial_status and initial_status.get('total_vectors', 0) >= 1000:
        print("✅ 服务器已有足够数据，无需插入")
    else:
        # 插入数据
        inserted = insert_bulk_data()
        
        if inserted > 0:
            print("\n🔍 检查插入后的状态...")
            check_server_status()
