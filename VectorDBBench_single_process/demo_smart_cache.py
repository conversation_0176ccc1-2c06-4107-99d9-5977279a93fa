#!/usr/bin/env python3
"""
直接测试FAISS客户端
"""

import json
import requests
import random
from typing import List

class SimpleFaissClient:
    """简化版FAISS客户端用于演示"""
    
    def __init__(self, host="localhost", port=8000, dim=768, index_type="Flat"):
        self.base_url = f"http://{host}:{port}"
        self.dim = dim
        self.index_type = index_type
        self._cache_validated = False
        self._server_has_data = False
        
        print(f"🔍 初始化FAISS客户端: {self.base_url}")
        self._validate_server_cache()
    
    def _validate_server_cache(self):
        """验证服务器缓存状态"""
        try:
            print(f"🔍 检查服务器缓存状态...")
            resp = requests.get(f"{self.base_url}/status", timeout=10)
            if resp.status_code == 200:
                status = resp.json()
                
                vectors_count = status.get('vectors_count', 0) or status.get('total_vectors', 0)
                server_index_type = status.get('index_type', '')
                server_dim = status.get('dimension', 0)
                
                print(f"📊 服务器状态: {vectors_count:,} 个向量, 索引类型: {server_index_type}, 维度: {server_dim}")
                
                # 检查缓存可用性
                if (vectors_count >= 1000 and 
                    server_index_type == self.index_type and 
                    server_dim == self.dim):
                    
                    self._server_has_data = True
                    self._cache_validated = True
                    print(f"✅ 缓存可用! 将跳过数据加载")
                    print(f"   - 向量数量: {vectors_count:,}")
                    print(f"   - 索引类型匹配: {server_index_type}")
                    print(f"   - 维度匹配: {server_dim}")
                else:
                    # 缓存不可用，记录原因
                    reasons = []
                    if vectors_count < 1000:
                        reasons.append(f"向量数量不足 ({vectors_count:,} < 1000)")
                    if server_index_type != self.index_type:
                        reasons.append(f"索引类型不匹配 ({server_index_type} != {self.index_type})")
                    if server_dim != self.dim:
                        reasons.append(f"维度不匹配 ({server_dim} != {self.dim})")
                    
                    print(f"⚠️ 缓存不可用，原因: {', '.join(reasons)}")
                    
        except Exception as e:
            print(f"⚠️ 无法连接到FAISS服务器: {e}")
    
    def insert_embeddings(self, embeddings: List[List[float]]) -> int:
        """插入向量数据，支持智能缓存跳过"""
        
        # 首先检查初始化时的缓存状态
        if self._server_has_data and self._cache_validated:
            print(f"🚀 智能缓存生效：跳过 {len(embeddings):,} 个向量的插入")
            print(f"   使用服务端现有的数据")
            return len(embeddings)
        
        # 正常插入流程
        print(f"📝 开始插入 {len(embeddings):,} 个向量到 {self.index_type} 索引...")
        
        try:
            resp = requests.post(
                f"{self.base_url}/insert_bulk",
                json={"vectors": embeddings},
                timeout=60
            )
            resp.raise_for_status()
            result = resp.json()
            
            print(f"✅ 成功插入 {len(embeddings)} 个向量")
            print(f"   服务器总向量数: {result.get('total_vectors', 'N/A')}")
            return len(embeddings)
            
        except Exception as e:
            print(f"❌ 插入失败: {e}")
            return 0
    
    def search_embedding(self, query: List[float], k: int = 10) -> List[int]:
        """搜索向量"""
        try:
            resp = requests.post(
                f"{self.base_url}/search",
                json={"query": query, "topk": k},
                timeout=30
            )
            resp.raise_for_status()
            result = resp.json()
            
            if "ids" in result and len(result["ids"]) > 0:
                return result["ids"][0]
            return []
            
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            return []

def demo_smart_cache():
    """演示智能缓存功能"""
    print("=" * 70)
    print("🎯 VectorDBBench FAISS智能缓存完整演示")
    print("=" * 70)
    
    # 情况1: 768维Flat索引 - 应该匹配缓存
    print("\n🎬 场景1: 768维Flat索引测试 (应该匹配缓存)")
    print("-" * 50)
    
    client1 = SimpleFaissClient(dim=768, index_type="Flat")
    
    # 生成测试数据
    test_embeddings = []
    for i in range(100):
        vector = [random.uniform(-1, 1) for _ in range(768)]
        test_embeddings.append(vector)
    
    # 插入测试
    inserted = client1.insert_embeddings(test_embeddings)
    print(f"处理结果: {inserted} 个向量")
    
    # 搜索测试
    query = [random.uniform(-1, 1) for _ in range(768)]
    results = client1.search_embedding(query, k=5)
    print(f"搜索结果: {len(results)} 个匹配项")
    
    # 情况2: 512维Flat索引 - 应该不匹配缓存
    print("\n🎬 场景2: 512维Flat索引测试 (应该不匹配缓存)")
    print("-" * 50)
    
    client2 = SimpleFaissClient(dim=512, index_type="Flat")
    
    # 生成512维测试数据
    test_embeddings_512 = []
    for i in range(50):
        vector = [random.uniform(-1, 1) for _ in range(512)]
        test_embeddings_512.append(vector)
    
    # 插入测试
    inserted = client2.insert_embeddings(test_embeddings_512)
    print(f"处理结果: {inserted} 个向量")
    
    # 情况3: 768维但不同索引类型 - 应该不匹配缓存
    print("\n🎬 场景3: 768维HNSW索引测试 (应该不匹配缓存)")
    print("-" * 50)
    
    client3 = SimpleFaissClient(dim=768, index_type="HNSW")
    
    # 插入测试
    inserted = client3.insert_embeddings(test_embeddings[:30])
    print(f"处理结果: {inserted} 个向量")
    
    print("\n" + "=" * 70)
    print("🎉 演示完成！")
    print("💡 智能缓存功能总结：")
    print("   ✅ 相同配置自动复用数据（768维+Flat）")
    print("   ❌ 不同维度需要重新插入（512维+Flat）") 
    print("   ❌ 不同索引类型需要重新插入（768维+HNSW）")
    print("🚀 这就是您问题的答案：不需要每次都重新embed数据！")
    print("=" * 70)

if __name__ == "__main__":
    demo_smart_cache()
