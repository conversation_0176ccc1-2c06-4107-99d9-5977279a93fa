# 智能FAISS服务器 - 使用说明

## 🎯 项目概述

智能FAISS服务器是一个为VectorDBBench框架优化的高性能向量搜索解决方案。它解决了原有系统中数据下载缓慢、API不兼容等关键问题，提供了完整的基准测试支持。

## 🚀 快速开始

### 1. 环境准备

#### 系统要求
- Python 3.11+
- Linux环境 (测试环境: Ubuntu)
- 访问共享数据集存储: `/nas/yvan.chen/milvus/dataset`

#### 依赖安装
```bash
# 安装核心依赖
pip install fastapi uvicorn faiss-cpu pandas numpy

# 或者使用项目环境
cd /home/<USER>/VectorDBBench
source vdbbench-venv/bin/activate  # 如果有虚拟环境
```

### 2. 服务器启动

#### 方法一：智能FAISS服务器（推荐）
```bash
cd /home/<USER>/VectorDBBench
python3 smart_faiss_server.py
```

#### 方法二：增强FAISS服务器
```bash
cd /home/<USER>/VectorDBBench  
python3 enhanced_server.py
```

#### 启动成功标志
```
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8001
📁 数据集路径: /nas/yvan.chen/milvus/dataset
📊 可用数据集:
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
   ✅ Performance768D1M: 1,000,000 向量, 768维
⚡ 智能缓存: 自动跳过重复插入
```

## 🔧 使用方法

### 1. 基本API测试

#### 服务状态检查
```bash
curl http://***********:8001/status
```

#### 服务信息查询
```bash
curl http://***********:8001/info
```

### 2. 自定义客户端测试

#### Python客户端示例
```python
import requests
import numpy as np

# 创建测试向量
test_vector = np.random.random(1536).tolist()

# 发送搜索请求
response = requests.post(
    "http://***********:8001/search",
    json={
        "query": test_vector,
        "topk": 100
    }
)

result = response.json()
print(f"找到 {len(result['ids'][0])} 个相似向量")
```

### 3. 官方VectorDBBench CLI测试

#### 基本性能测试
```bash
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://***********:8001 \
    --case-type Performance1536D50K \
    --index-type HNSW \
    --m 16 \
    --ef-construction 200 \
    --load
```

#### 不同规模数据集测试
```bash
# 5万向量测试
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://***********:8001 \
    --case-type Performance1536D50K \
    --index-type HNSW --load

# 50万向量测试  
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://***********:8001 \
    --case-type Performance1536D500K \
    --index-type HNSW --load

# 500万向量测试
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://***********:8001 \
    --case-type Performance1536D5M \
    --index-type HNSW --load
```

## 📊 性能基准

### 已验证性能指标

#### 自定义客户端性能
- **最大QPS**: 297.7
- **平均延迟**: < 50ms  
- **P99延迟**: < 100ms
- **并发支持**: 最高80线程
- **测试环境**: 跨机器部署

#### 官方CLI性能
- **最大QPS**: 45.98
- **平均延迟**: ~65ms
- **P99延迟**: 0.131s
- **并发测试**: 1-80线程全覆盖
- **兼容性**: 100%通过官方基准测试

### 数据集加载性能
- **Performance1536D50K**: ~3秒加载完成
- **Performance1536D500K**: ~30秒加载完成  
- **Performance1536D5M**: ~5分钟加载完成
- **智能缓存**: 二次启动 < 1秒

## 🌐 部署架构

### 单机部署
```
localhost:8001 ← FAISS服务器
localhost ← VectorDBBench客户端
```

### 跨机器部署（推荐）
```
***********:8001 ← FAISS服务器
********** ← VectorDBBench客户端  
/nas/yvan.chen/milvus/dataset ← 共享数据存储
```

### 网络配置
- **服务端口**: 8001
- **协议**: HTTP/JSON
- **防火墙**: 确保8001端口可访问

## 🛠️ 高级配置

### 1. HNSW参数调优

#### 服务器端配置
```python
# 在create_index请求中指定
{
    "dim": 1536,
    "index_type": "HNSW", 
    "m": 16,               # 连接数，影响精度和内存
    "ef_construction": 200  # 构建时搜索深度
}
```

#### 推荐参数组合
```bash
# 高精度配置
--m 32 --ef-construction 400

# 平衡配置（默认）
--m 16 --ef-construction 200

# 高速度配置  
--m 8 --ef-construction 100
```

### 2. 数据集映射自定义

#### 修改数据集路径
```python
# 在smart_faiss_server.py中修改
DATASET_BASE_PATH = "/your/custom/dataset/path"

DATASET_MAPPING = {
    "Performance1536D50K": {
        "path": "custom/dataset/path",
        "dimension": 1536,
        "vectors": 50000
    }
}
```

### 3. 内存和性能优化

#### 批处理大小调整
```python
# 在数据加载中调整
batch_size = 10000  # 根据内存情况调整
```

#### 并发处理优化
```python
# FastAPI并发配置
uvicorn.run(app, host="0.0.0.0", port=8001, workers=4)
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 数据集加载失败
```bash
# 检查数据集路径
ls -la /nas/yvan.chen/milvus/dataset/openai/openai_small_50k/

# 检查权限
sudo chmod -R 755 /nas/yvan.chen/milvus/dataset/
```

#### 2. API格式不兼容
```python
# 检查请求格式
logger.info(f"🔍 [DEBUG] 原始搜索请求: {request_data}")

# 支持的格式:
# 格式1: {"query": [1536维向量], "topk": 100}
# 格式2: {"vectors": [[1536维向量]], "k": 100}
```

#### 3. 性能问题诊断
```bash
# 检查服务器日志
tail -f /path/to/server.log

# 监控系统资源
htop
nvidia-smi  # 如果使用GPU
```

#### 4. 网络连接问题
```bash
# 检查端口占用
netstat -tlnp | grep 8001

# 测试网络连通性
curl -v http://***********:8001/status
```

## 📝 开发指南

### 代码结构
```
smart_faiss_server.py          # 主服务器实现
├── 数据集映射配置             # DATASET_MAPPING
├── FastAPI应用定义            # app = FastAPI()
├── 索引创建接口               # /create_index
├── 向量搜索接口               # /search  
├── 状态监控接口               # /status, /info
└── 服务启动函数               # main()

enhanced_server.py             # 整合后的增强服务器
vectordb_bench/backend/clients/faiss/faiss.py  # 客户端适配
```

### 扩展开发
```python
# 添加新的数据集支持
DATASET_MAPPING["NewDataset"] = {
    "path": "new/dataset/path",
    "dimension": 512, 
    "vectors": 100000
}

# 添加新的索引类型
if index_type.upper() == "IVF":
    index = faiss.IndexIVFFlat(quantizer, dim, nlist)
```

## 📈 监控和维护

### 性能监控
```python
# 添加性能指标收集
import time
start_time = time.time()
# ... 执行搜索
search_time = time.time() - start_time
logger.info(f"搜索耗时: {search_time:.3f}s")
```

### 日志管理
```bash
# 日志轮转配置
logrotate /etc/logrotate.d/faiss-server

# 实时日志监控
tail -f smart_faiss_server.log | grep -E "(QPS|ERROR|搜索)"
```

### 定期维护
- 每周检查数据集完整性
- 每月分析性能趋势
- 定期清理临时文件和缓存

## 🤝 贡献指南

### 提交代码
1. Fork项目并创建分支
2. 遵循现有代码风格
3. 添加单元测试
4. 更新文档
5. 提交Pull Request

### 报告问题
- 使用GitHub Issues
- 提供详细的错误信息
- 包含重现步骤
- 附加相关日志

## 📄 许可证

本项目遵循MIT许可证，详见LICENSE文件。

---

**联系方式**: 如有问题，请通过GitHub Issues或项目维护者联系。
