#!/bin/bash
# 修复后的FAISS环境设置脚本

export DATASET_LOCAL_DIR="/home/<USER>/VectorDBBench/dataset"
export AWS_PROFILE="default"
export AWS_DEFAULT_REGION="us-east-1"
export AWS_ACCESS_KEY_ID="dummy_access_key"
export AWS_SECRET_ACCESS_KEY="dummy_secret_key"

echo "✅ 修复后的FAISS环境已设置"
echo "   📂 数据集: $DATASET_LOCAL_DIR"
echo "   🔧 AWS: 完整配置模式"
echo ""
echo "🚀 现在可以运行FAISS测试:"
echo "   # FAISS Local HNSW (完整参数)"
echo "   vectordbbench faisslocalhnsw --case-type Performance1536D50K --m 16 --ef-construction 200 --ef-search 100"
echo ""
echo "   # FAISS Local IVF (完整参数)"  
echo "   vectordbbench faisslocalivfflat --case-type Performance1536D50K --nlist 100 --nprobe 10"
echo ""
echo "   # FAISS Remote (简化)"
echo "   python -m vectordb_bench.cli.vectordbbench faissremote --case-type Performance1536D50K"
