#!/usr/bin/env python3
"""
使用 VectorDBBench 测试远程 FAISS 服务器的示例脚本
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime

def run_remote_faiss_benchmark():
    """使用远程 FAISS 服务器运行 VectorDBBench 测试"""
    print("🌐 VectorDBBench 远程 FAISS 基准测试")
    print("=" * 60)
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
        
        print("✅ VectorDBBench 远程 FAISS 模块加载成功")
        
        # 远程 FAISS 服务器配置
        remote_configs = [
            {
                "name": "本地 FAISS 服务器测试",
                "host": "127.0.0.1",
                "port": 8002,
                "index_type": "Flat",
                "case_type": "Performance1536D50K",
                "description": "连接本地 FAISS 服务器进行测试"
            },
            {
                "name": "本地 IVF 索引测试", 
                "host": "127.0.0.1",
                "port": 8002,
                "index_type": "IVF1024",
                "case_type": "Performance1536D50K",
                "description": "使用 IVF 索引进行测试"
            }
        ]
        
        results = []
        
        for config in remote_configs:
            print(f"\\n🎯 执行远程测试: {config['name']}")
            print(f"   📋 描述: {config['description']}")
            print(f"   🔧 服务器: {config['host']}:{config['port']}")
            print(f"   📊 索引类型: {config['index_type']}")
            
            # 创建远程 FAISS 配置
            db_config = FaissConfig(
                host=config['host'],
                port=config['port'],
                index_type=config['index_type']
            )
            
            db_case_config = FaissDBCaseConfig()
            
            print(f"\\n   🔧 远程 FAISS 配置:")
            print(f"      服务器地址: {db_config.host}")
            print(f"      服务器端口: {db_config.port}")
            print(f"      索引类型: {db_config.index_type}")
            
            # 测试参数
            test_params = {
                'case_type': config['case_type'],
                'k': 100,
                'num_concurrency': [1],
                'concurrency_duration': 30,
                'concurrency_timeout': 3600,
                'db_label': f"remote_faiss_{config['index_type'].lower()}",
                'load': True,
                'search_serial': True,
                'search_concurrent': True,
                'drop_old': True,
                'dry_run': False,
                'task_label': f'Remote_FAISS_{config["index_type"]}_{config["case_type"]}',
                'custom_case': {}
            }
            
            print(f"\\n   🚀 开始远程基准测试...")
            print(f"      案例类型: {test_params['case_type']}")
            print(f"      k值: {test_params['k']}")
            print(f"      测试时长: {test_params['concurrency_duration']}s")
            print("   " + "="*50)
            
            start_time = time.time()
            
            try:
                # 运行远程测试
                run(
                    db=DB.Faiss,  # 使用远程 FAISS
                    db_config=db_config,
                    db_case_config=db_case_config,
                    **test_params
                )
                
                test_time = time.time() - start_time
                print(f"\\n   ✅ 远程测试完成，耗时: {test_time:.2f}s")
                
                # 记录结果
                results.append({
                    "config": config,
                    "test_time": test_time,
                    "status": "success"
                })
                
            except Exception as e:
                test_time = time.time() - start_time
                print(f"\\n   ❌ 远程测试失败: {e}")
                
                results.append({
                    "config": config,
                    "test_time": test_time,
                    "status": "failed",
                    "error": str(e)
                })
                
                # 打印更详细的错误信息
                import traceback
                print("   详细错误信息:")
                traceback.print_exc()
                
                # 继续下一个测试
                continue
        
        # 生成测试摘要
        print(f"\\n📊 远程测试摘要:")
        print("="*50)
        
        successful_tests = [r for r in results if r["status"] == "success"]
        failed_tests = [r for r in results if r["status"] == "failed"]
        
        print(f"✅ 成功测试: {len(successful_tests)}")
        print(f"❌ 失败测试: {len(failed_tests)}")
        
        if successful_tests:
            total_time = sum(r["test_time"] for r in successful_tests)
            print(f"⏱️  总测试时间: {total_time:.2f}s")
            
            for result in successful_tests:
                config = result["config"]
                print(f"   🎯 {config['name']}: {result['test_time']:.2f}s")
        
        if failed_tests:
            print(f"\\n❌ 失败的测试:")
            for result in failed_tests:
                config = result["config"]
                print(f"   💥 {config['name']}: {result.get('error', 'Unknown error')}")
        
        return len(successful_tests) > 0
        
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_faiss_server():
    """检查 FAISS 服务器是否正在运行"""
    print("\\n🔍 检查 FAISS 服务器状态...")
    
    try:
        import requests
        
        # 尝试连接本地 FAISS 服务器
        try:
            response = requests.get("http://127.0.0.1:8002/docs", timeout=5)
            if response.status_code == 200:
                print("✅ FAISS 服务器正在运行 (http://127.0.0.1:8002)")
                print("   📖 API 文档: http://127.0.0.1:8002/docs")
                return True
            else:
                print(f"⚠️  FAISS 服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException:
            print("❌ FAISS 服务器未运行或无法连接")
            print("\\n📖 启动 FAISS 服务器:")
            print("   ```bash")
            print("   cd /home/<USER>/VectorDBBench")
            print("   pip install fastapi uvicorn")
            print("   python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002")
            print("   ```")
            return False
            
    except ImportError:
        print("❌ 缺少 requests 依赖，无法检查服务器状态")
        print("   安装: pip install requests")
        return False

def show_usage_comparison():
    """显示本地 vs 远程 FAISS 的使用对比"""
    print("\\n📊 本地 vs 远程 FAISS 使用对比")
    print("=" * 50)
    
    print("🏠 本地 FAISS (你当前使用的):")
    print("   ```python")
    print("   from vectordb_bench.backend.clients import DB")
    print("   from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig")
    print("   ")
    print("   db_config = FaissLocalConfig(index_type='HNSW')")
    print("   db_case_config = HNSWConfig(m=16, ef_construction=200)")
    print("   ")
    print("   run(db=DB.FaissLocal, db_config=db_config, db_case_config=db_case_config, ...)")
    print("   ```")
    
    print("\\n🌐 远程 FAISS (新选项):")
    print("   ```python")
    print("   from vectordb_bench.backend.clients import DB")
    print("   from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig")
    print("   ")
    print("   db_config = FaissConfig(host='faiss-server.com', port=8002, index_type='Flat')")
    print("   db_case_config = FaissDBCaseConfig()")
    print("   ")
    print("   run(db=DB.Faiss, db_config=db_config, db_case_config=db_case_config, ...)")
    print("   ```")
    
    print("\\n🔄 主要区别:")
    print("   • 本地: 直接使用 FAISS 库，支持更多索引类型和参数")
    print("   • 远程: 通过 HTTP API 连接，支持分布式部署")
    print("   • 远程: 需要先启动 FAISS 服务器")
    print("   • 远程: 网络延迟可能影响性能测试结果")

if __name__ == "__main__":
    print("🎯 VectorDBBench 远程 FAISS 基准测试")
    print("=" * 55)
    
    # 检查服务器状态
    server_running = check_faiss_server()
    
    # 显示使用对比
    show_usage_comparison()
    
    if server_running:
        # 运行远程基准测试
        success = run_remote_faiss_benchmark()
        print(f"\\n{'🎉 远程测试完成!' if success else '💥 远程测试失败!'}")
    else:
        print("\\n⚠️  请先启动 FAISS 服务器后再运行远程测试")
        success = False
    
    sys.exit(0 if success else 1)
