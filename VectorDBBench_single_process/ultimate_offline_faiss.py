#!/usr/bin/env python3
"""
终极离线FAISS测试 - 完全绕过VectorDBBench的数据集下载逻辑
直接使用本地数据测试FAISS性能
"""

import os
import sys
import time
import numpy as np
import faiss
from pathlib import Path

def create_mock_dataset(size=50000, dim=1536):
    """创建模拟数据集进行测试"""
    print(f"📊 生成模拟数据集: {size:,} 向量, {dim} 维度")
    
    # 生成随机向量数据
    np.random.seed(42)  # 确保可重复
    vectors = np.random.random((size, dim)).astype('float32')
    
    # 归一化向量（用于余弦相似度）
    faiss.normalize_L2(vectors)
    
    # 生成查询向量
    query_vectors = np.random.random((100, dim)).astype('float32')
    faiss.normalize_L2(query_vectors)
    
    return vectors, query_vectors

def test_faiss_hnsw_performance(vectors, query_vectors, m=16, ef_construction=200, ef_search=100):
    """测试FAISS HNSW性能"""
    print(f"\n🔧 FAISS HNSW 配置:")
    print(f"   📐 M: {m}")
    print(f"   🏗️ ef_construction: {ef_construction}")
    print(f"   🔍 ef_search: {ef_search}")
    
    # 创建HNSW索引
    dim = vectors.shape[1]
    index = faiss.IndexHNSWFlat(dim, m)
    index.hnsw.efConstruction = ef_construction
    index.hnsw.efSearch = ef_search
    
    print(f"\n📈 开始构建索引...")
    start_time = time.time()
    
    # 添加向量到索引
    index.add(vectors)
    
    build_time = time.time() - start_time
    print(f"✅ 索引构建完成: {build_time:.2f}s")
    print(f"📊 索引大小: {index.ntotal:,} 向量")
    
    # 执行搜索测试
    print(f"\n🔍 开始搜索测试...")
    k = 100  # 返回前100个结果
    
    # 预热
    _, _ = index.search(query_vectors[:10], k)
    
    # 正式测试
    start_time = time.time()
    distances, indices = index.search(query_vectors, k)
    search_time = time.time() - start_time
    
    # 计算性能指标
    qps = len(query_vectors) / search_time
    avg_latency = (search_time / len(query_vectors)) * 1000  # ms
    
    print(f"\n📈 性能结果:")
    print(f"   🚀 QPS: {qps:.2f}")
    print(f"   ⏱️ 平均延迟: {avg_latency:.2f}ms")
    print(f"   📊 查询数量: {len(query_vectors)}")
    print(f"   🎯 返回结果: Top-{k}")
    
    return {
        "build_time": build_time,
        "qps": qps,
        "latency_ms": avg_latency,
        "index_size": index.ntotal
    }

def test_concurrent_performance(vectors, query_vectors, num_threads=4):
    """测试并发性能"""
    print(f"\n🔄 并发性能测试 (线程数: {num_threads})")
    
    import threading
    import queue
    
    # 创建索引
    dim = vectors.shape[1]
    index = faiss.IndexHNSWFlat(dim, 16)
    index.hnsw.efConstruction = 200
    index.hnsw.efSearch = 100
    index.add(vectors)
    
    # 准备查询任务
    query_queue = queue.Queue()
    result_queue = queue.Queue()
    
    for i, query in enumerate(query_vectors):
        query_queue.put((i, query.reshape(1, -1)))
    
    def worker():
        """工作线程函数"""
        while True:
            try:
                query_id, query = query_queue.get(timeout=1)
                start = time.time()
                distances, indices = index.search(query, 100)
                end = time.time()
                result_queue.put((query_id, end - start))
                query_queue.task_done()
            except queue.Empty:
                break
    
    # 启动并发测试
    start_time = time.time()
    
    threads = []
    for _ in range(num_threads):
        t = threading.Thread(target=worker)
        t.start()
        threads.append(t)
    
    # 等待所有任务完成
    query_queue.join()
    total_time = time.time() - start_time
    
    # 收集结果
    latencies = []
    while not result_queue.empty():
        _, latency = result_queue.get()
        latencies.append(latency)
    
    # 计算统计
    avg_latency = np.mean(latencies) * 1000  # ms
    p99_latency = np.percentile(latencies, 99) * 1000  # ms
    concurrent_qps = len(query_vectors) / total_time
    
    print(f"   🚀 并发QPS: {concurrent_qps:.2f}")
    print(f"   ⏱️ 平均延迟: {avg_latency:.2f}ms")
    print(f"   📊 P99延迟: {p99_latency:.2f}ms")
    
    return {
        "concurrent_qps": concurrent_qps,
        "avg_latency_ms": avg_latency,
        "p99_latency_ms": p99_latency
    }

def main():
    """主测试函数"""
    print("🎯 终极离线FAISS性能测试")
    print("=" * 50)
    
    # 参数配置
    dataset_size = 50000
    vector_dim = 1536
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='FAISS离线性能测试')
    parser.add_argument('--size', type=int, default=dataset_size, help='数据集大小')
    parser.add_argument('--dim', type=int, default=vector_dim, help='向量维度')
    parser.add_argument('--m', type=int, default=16, help='HNSW M参数')
    parser.add_argument('--ef-construction', type=int, default=200, help='HNSW ef_construction')
    parser.add_argument('--ef-search', type=int, default=100, help='HNSW ef_search')
    parser.add_argument('--threads', type=int, default=4, help='并发线程数')
    parser.add_argument('--concurrency-duration', type=int, default=30, help='并发测试持续时间(秒)')
    
    args = parser.parse_args()
    
    print(f"📋 测试配置:")
    print(f"   📊 数据集大小: {args.size:,}")
    print(f"   📐 向量维度: {args.dim}")
    print(f"   🔧 HNSW M: {args.m}")
    print(f"   🏗️ ef_construction: {args.ef_construction}")
    print(f"   🔍 ef_search: {args.ef_search}")
    print(f"   🔄 并发线程: {args.threads}")
    
    # 生成测试数据
    vectors, query_vectors = create_mock_dataset(args.size, args.dim)
    
    # 单线程性能测试
    print(f"\n" + "="*30 + " 单线程测试 " + "="*30)
    single_results = test_faiss_hnsw_performance(
        vectors, query_vectors, 
        args.m, args.ef_construction, args.ef_search
    )
    
    # 并发性能测试
    print(f"\n" + "="*30 + " 并发测试 " + "="*30)
    concurrent_results = test_concurrent_performance(vectors, query_vectors, args.threads)
    
    # 生成报告
    print(f"\n" + "="*30 + " 测试报告 " + "="*30)
    print(f"📊 数据集: {args.size:,} 向量 x {args.dim} 维")
    print(f"🔧 HNSW配置: M={args.m}, ef_construction={args.ef_construction}, ef_search={args.ef_search}")
    print(f"\n📈 单线程性能:")
    print(f"   🏗️ 构建时间: {single_results['build_time']:.2f}s")
    print(f"   🚀 QPS: {single_results['qps']:.2f}")
    print(f"   ⏱️ 延迟: {single_results['latency_ms']:.2f}ms")
    print(f"\n🔄 并发性能 ({args.threads} 线程):")
    print(f"   🚀 QPS: {concurrent_results['concurrent_qps']:.2f}")
    print(f"   ⏱️ 平均延迟: {concurrent_results['avg_latency_ms']:.2f}ms")
    print(f"   📊 P99延迟: {concurrent_results['p99_latency_ms']:.2f}ms")
    
    # 保存结果到文件
    import json
    report = {
        "config": {
            "dataset_size": args.size,
            "vector_dim": args.dim,
            "m": args.m,
            "ef_construction": args.ef_construction,
            "ef_search": args.ef_search,
            "threads": args.threads
        },
        "single_thread": single_results,
        "concurrent": concurrent_results,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    output_file = f"faiss_offline_benchmark_{int(time.time())}.json"
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n💾 结果已保存到: {output_file}")
    print(f"\n🎊 测试完成！这就是FAISS的真实性能表现！")

if __name__ == "__main__":
    main()
