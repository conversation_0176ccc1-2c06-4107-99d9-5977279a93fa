#!/usr/bin/env python3
"""
FAISS Benchmark 测试 - 获取 QPS、召回率等性能指标
"""

import os
import sys
import time
import logging
import numpy as np
import pandas as pd
from pathlib import Path

# 设置环境变量
os.environ['DATASET_LOCAL_DIR'] = '/home/<USER>/VectorDBBench/dataset'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
log = logging.getLogger(__name__)

def create_test_dataset():
    """创建测试数据集"""
    dataset_dir = Path('/home/<USER>/VectorDBBench/dataset/openai_test')
    dataset_dir.mkdir(parents=True, exist_ok=True)
    
    print("🔧 创建测试数据集...")
    
    # 参数
    num_train = 10000  # 训练数据量
    num_test = 100     # 测试查询数量
    dim = 1536         # OpenAI 向量维度
    
    # 生成训练数据
    print(f"  📊 生成 {num_train} 个训练向量...")
    train_vectors = np.random.random((num_train, dim)).astype(np.float32)
    train_vectors = train_vectors / np.linalg.norm(train_vectors, axis=1, keepdims=True)
    
    train_data = pd.DataFrame({
        'id': range(num_train),
        'vector': [v.tolist() for v in train_vectors]
    })
    
    train_file = dataset_dir / 'shuffle_train.parquet'
    train_data.to_parquet(train_file, index=False)
    
    # 生成测试数据
    print(f"  🔍 生成 {num_test} 个测试查询...")
    test_vectors = np.random.random((num_test, dim)).astype(np.float32)
    test_vectors = test_vectors / np.linalg.norm(test_vectors, axis=1, keepdims=True)
    
    test_data = pd.DataFrame({
        'vector': [v.tolist() for v in test_vectors]
    })
    
    test_file = dataset_dir / 'test.parquet'
    test_data.to_parquet(test_file, index=False)
    
    # 生成 ground truth（计算真实的最近邻）
    print("  ✅ 计算 ground truth...")
    similarities = np.dot(test_vectors, train_vectors.T)
    gt_neighbors = []
    for i in range(num_test):
        neighbors = np.argsort(similarities[i])[::-1][:100].tolist()
        gt_neighbors.append(neighbors)
    
    gt_data = pd.DataFrame({
        'neighbors': gt_neighbors
    })
    
    gt_file = dataset_dir / 'neighbors.parquet'
    gt_data.to_parquet(gt_file, index=False)
    
    print(f"  💾 数据集已保存到: {dataset_dir}")
    return dataset_dir

def run_faiss_benchmark():
    """运行 FAISS benchmark 测试"""
    print("🚀 开始 FAISS Benchmark 测试...")
    
    try:
        from vectordb_bench.backend.clients.faiss_local.faiss_local import FaissLocalClient
        from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
        from vectordb_bench.backend.clients.api import MetricType
        
        # 创建测试数据集
        dataset_dir = create_test_dataset()
        
        # 加载数据
        print("📂 加载数据集...")
        train_df = pd.read_parquet(dataset_dir / 'shuffle_train.parquet')
        test_df = pd.read_parquet(dataset_dir / 'test.parquet')
        gt_df = pd.read_parquet(dataset_dir / 'neighbors.parquet')
        
        print(f"  📈 训练数据: {len(train_df)} 个向量")
        print(f"  🔍 测试查询: {len(test_df)} 个查询")
        
        # 配置 FAISS 客户端
        db_config = FaissLocalConfig(index_type="HNSW")
        db_case_config = HNSWConfig(
            m=16,
            ef_construction=200,
            ef_search=64,
            metric_type=MetricType.COSINE
        )
        
        client = FaissLocalClient(
            dim=1536,
            db_config=db_config,
            db_case_config=db_case_config,
            collection_name="benchmark_test"
        )
        
        # 插入数据并构建索引
        print("🔨 构建 FAISS 索引...")
        start_time = time.time()
        
        vectors = train_df['vector'].tolist()
        ids = train_df['id'].tolist()
        
        result, error = client.insert_embeddings(vectors, ids)
        if error:
            print(f"❌ 插入数据失败: {error}")
            return False
        
        client.optimize()
        build_time = time.time() - start_time
        
        print(f"  ✅ 索引构建完成，耗时: {build_time:.2f}s")
        print(f"  📊 索引中向量数量: {client.index.ntotal}")
        
        # 性能测试
        print("\n🎯 开始性能测试...")
        
        # 测试参数
        k_values = [10, 50, 100]
        ef_search_values = [32, 64, 128]
        
        results = []
        
        for k in k_values:
            for ef_search in ef_search_values:
                # 设置搜索参数
                client.index.hnsw.efSearch = ef_search
                
                print(f"\n📈 测试参数: k={k}, efSearch={ef_search}")
                
                # 预热
                for i in range(5):
                    query = test_df['vector'].iloc[i]
                    client.search_embedding(query, k=k)
                
                # 性能测试
                search_times = []
                all_results = []
                
                start_perf = time.time()
                for i in range(len(test_df)):
                    query = test_df['vector'].iloc[i]
                    
                    query_start = time.time()
                    search_result = client.search_embedding(query, k=k)
                    query_time = time.time() - query_start
                    
                    search_times.append(query_time)
                    all_results.append(search_result[:k])
                
                total_time = time.time() - start_perf
                
                # 计算性能指标
                avg_latency = np.mean(search_times) * 1000  # 转换为毫秒
                p99_latency = np.percentile(search_times, 99) * 1000
                qps = len(test_df) / total_time
                
                # 计算召回率
                recalls = []
                for i, result in enumerate(all_results):
                    if len(result) > 0:
                        gt_neighbors = gt_df['neighbors'].iloc[i][:k]
                        intersection = len(set(result) & set(gt_neighbors))
                        recall = intersection / min(len(gt_neighbors), k)
                        recalls.append(recall)
                
                avg_recall = np.mean(recalls) if recalls else 0.0
                
                # 保存结果
                result_data = {
                    'k': k,
                    'efSearch': ef_search,
                    'QPS': qps,
                    'avg_latency_ms': avg_latency,
                    'p99_latency_ms': p99_latency,
                    'recall': avg_recall,
                    'total_queries': len(test_df)
                }
                results.append(result_data)
                
                # 输出结果
                print(f"  🎯 QPS: {qps:.2f}")
                print(f"  ⏱️  平均延迟: {avg_latency:.2f}ms")
                print(f"  📊 P99 延迟: {p99_latency:.2f}ms")
                print(f"  🔍 召回率: {avg_recall:.4f} ({avg_recall*100:.2f}%)")
        
        # 输出汇总结果
        print("\n" + "="*80)
        print("📋 FAISS Benchmark 测试结果汇总")
        print("="*80)
        print(f"{'k':<5} {'efSearch':<10} {'QPS':<10} {'延迟(ms)':<12} {'P99(ms)':<10} {'召回率':<10}")
        print("-" * 80)
        
        for r in results:
            print(f"{r['k']:<5} {r['efSearch']:<10} {r['QPS']:<10.2f} {r['avg_latency_ms']:<12.2f} {r['p99_latency_ms']:<10.2f} {r['recall']:<10.4f}")
        
        # 保存结果到文件
        results_df = pd.DataFrame(results)
        results_file = Path('/home/<USER>/VectorDBBench/faiss_benchmark_results.csv')
        results_df.to_csv(results_file, index=False)
        print(f"\n💾 详细结果已保存到: {results_file}")
        
        # 找出最佳配置
        best_qps = max(results, key=lambda x: x['QPS'])
        best_recall = max(results, key=lambda x: x['recall'])
        
        print(f"\n🏆 最佳性能配置:")
        print(f"  🚀 最高 QPS: {best_qps['QPS']:.2f} (k={best_qps['k']}, efSearch={best_qps['efSearch']})")
        print(f"  🎯 最高召回率: {best_recall['recall']:.4f} (k={best_recall['k']}, efSearch={best_recall['efSearch']})")
        
        print(f"\n✅ FAISS Benchmark 测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ Benchmark 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_faiss_benchmark()
    sys.exit(0 if success else 1)
