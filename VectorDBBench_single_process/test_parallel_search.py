#!/usr/bin/env python3
"""
测试FAISS服务器的并行搜索功能
"""
import requests
import numpy as np
import time
import json

def test_faiss_server():
    base_url = "http://10.1.180.71:8001"
    
    print("🧪 测试FAISS服务器并行搜索功能")
    print("=" * 50)
    
    # 1. 检查服务器状态
    print("1️⃣ 检查服务器状态...")
    response = requests.get(f"{base_url}/status")
    if response.status_code == 200:
        status = response.json()
        print(f"   ✅ 服务器状态: {status}")
        dim = status.get('dimension', 768)
    else:
        print(f"   ❌ 服务器未响应: {response.status_code}")
        return
    
    # 2. 测试单个搜索（应该被转换为批量搜索）
    print("\n2️⃣ 测试单个向量搜索（强制批量化）...")
    query_vector = np.random.random(dim).tolist()
    search_data = {
        "query": query_vector,
        "topk": 10
    }
    
    start_time = time.time()
    response = requests.post(f"{base_url}/search", json=search_data)
    end_time = time.time()
    
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ 单个搜索成功: {len(result['ids'][0])} 个结果")
        print(f"   ⏱️ 搜索时间: {(end_time - start_time)*1000:.2f}ms")
    else:
        print(f"   ❌ 搜索失败: {response.text}")
        return
    
    # 3. 测试真正的批量搜索
    print("\n3️⃣ 测试批量向量搜索...")
    batch_size = 32
    batch_vectors = [np.random.random(dim).tolist() for _ in range(batch_size)]
    batch_data = {
        "batch_queries": batch_vectors,
        "topk": 10
    }
    
    start_time = time.time()
    response = requests.post(f"{base_url}/search", json=batch_data)
    end_time = time.time()
    
    if response.status_code == 200:
        result = response.json()
        print(f"   ✅ 批量搜索成功: {len(result['ids'])} 个查询, 每个 {len(result['ids'][0])} 个结果")
        print(f"   ⏱️ 批量搜索时间: {(end_time - start_time)*1000:.2f}ms")
        print(f"   🚀 平均每个查询: {(end_time - start_time)*1000/batch_size:.2f}ms")
    else:
        print(f"   ❌ 批量搜索失败: {response.text}")
        return
    
    # 4. 性能压测
    print("\n4️⃣ 并发性能压测...")
    import concurrent.futures
    import threading
    
    def single_search():
        query_vector = np.random.random(dim).tolist()
        search_data = {"query": query_vector, "topk": 10}
        start = time.time()
        response = requests.post(f"{base_url}/search", json=search_data)
        end = time.time()
        return end - start, response.status_code == 200
    
    # 并发搜索测试
    concurrency = 16
    num_requests = 100
    
    print(f"   🔥 启动 {concurrency} 个并发线程，每个执行 {num_requests//concurrency} 次搜索...")
    
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = [executor.submit(single_search) for _ in range(num_requests)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    end_time = time.time()
    
    # 统计结果
    successful = sum(1 for _, success in results if success)
    avg_latency = sum(latency for latency, _ in results) / len(results) * 1000
    total_time = end_time - start_time
    qps = num_requests / total_time
    
    print(f"   ✅ 并发测试完成:")
    print(f"      总请求数: {num_requests}")
    print(f"      成功请求: {successful}")
    print(f"      总耗时: {total_time:.2f}秒")
    print(f"      QPS: {qps:.2f}")
    print(f"      平均延迟: {avg_latency:.2f}ms")
    
    print("\n🎯 提示: 在htop中观察CPU使用率")
    print("   - 如果看到多个核心被使用，说明OpenMP并行生效")
    print("   - 如果只有单核100%，说明仍需优化")

if __name__ == "__main__":
    test_faiss_server()
