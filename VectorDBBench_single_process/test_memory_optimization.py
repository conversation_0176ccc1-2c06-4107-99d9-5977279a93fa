#!/usr/bin/env python3
"""
测试内存带宽优化效果
"""

import requests
import numpy as np
import time
import threading
import concurrent.futures
from typing import List
import psutil
import os

def create_test_query(dimension: int = 768) -> List[float]:
    """创建测试查询向量"""
    return np.random.random(dimension).astype(np.float32).tolist()

def single_search_request(url: str, query: List[float], topk: int = 100, ef_search: int = None) -> dict:
    """发送单个搜索请求"""
    payload = {
        "query": query,
        "topk": topk
    }
    if ef_search is not None:
        payload["ef_search"] = ef_search
    
    try:
        response = requests.post(f"{url}/search", json=payload, timeout=10)
        response.raise_for_status()
        return {"success": True, "response_time": response.elapsed.total_seconds()}
    except Exception as e:
        return {"success": False, "error": str(e)}

def benchmark_concurrent_requests(url: str, num_requests: int = 100, num_threads: int = 10, ef_search: int = None):
    """并发请求基准测试"""
    print(f"🚀 开始并发测试: {num_requests}个请求, {num_threads}个线程, ef_search={ef_search}")
    
    # 创建测试查询
    queries = [create_test_query() for _ in range(num_requests)]
    
    # 记录开始时间
    start_time = time.time()
    
    # 并发执行
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [
            executor.submit(single_search_request, url, query, 100, ef_search)
            for query in queries
        ]
        
        for future in concurrent.futures.as_completed(futures):
            results.append(future.result())
    
    # 计算统计信息
    end_time = time.time()
    total_time = end_time - start_time
    
    successful_requests = [r for r in results if r["success"]]
    failed_requests = [r for r in results if not r["success"]]
    
    if successful_requests:
        response_times = [r["response_time"] for r in successful_requests]
        avg_response_time = sum(response_times) / len(response_times)
        qps = len(successful_requests) / total_time
    else:
        avg_response_time = 0
        qps = 0
    
    print(f"📊 测试结果:")
    print(f"   总时间: {total_time:.2f}s")
    print(f"   成功请求: {len(successful_requests)}/{num_requests}")
    print(f"   失败请求: {len(failed_requests)}")
    print(f"   平均响应时间: {avg_response_time*1000:.2f}ms")
    print(f"   QPS: {qps:.2f}")
    
    if failed_requests:
        print(f"❌ 失败原因示例: {failed_requests[0]['error']}")
    
    return {
        "total_time": total_time,
        "successful_requests": len(successful_requests),
        "failed_requests": len(failed_requests),
        "avg_response_time": avg_response_time,
        "qps": qps
    }

def monitor_system_resources(duration: int = 60):
    """监控系统资源使用"""
    print(f"📈 开始监控系统资源 ({duration}秒)...")
    
    # 找到FAISS服务器进程
    faiss_pid = None
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'smart_faiss_server.py' in ' '.join(proc.info['cmdline'] or []):
                faiss_pid = proc.info['pid']
                break
        except:
            continue
    
    if not faiss_pid:
        print("❌ 未找到FAISS服务器进程")
        return
    
    print(f"🔍 监控进程 PID: {faiss_pid}")
    faiss_process = psutil.Process(faiss_pid)
    
    start_time = time.time()
    cpu_samples = []
    memory_samples = []
    
    while time.time() - start_time < duration:
        try:
            cpu_percent = faiss_process.cpu_percent(interval=1)
            memory_info = faiss_process.memory_info()
            memory_gb = memory_info.rss / 1024 / 1024 / 1024
            
            cpu_samples.append(cpu_percent)
            memory_samples.append(memory_gb)
            
            print(f"   CPU: {cpu_percent:.1f}%, 内存: {memory_gb:.1f}GB")
        except psutil.NoSuchProcess:
            print("❌ 进程已退出")
            break
        except Exception as e:
            print(f"⚠️ 监控错误: {e}")
    
    if cpu_samples:
        avg_cpu = sum(cpu_samples) / len(cpu_samples)
        max_cpu = max(cpu_samples)
        avg_memory = sum(memory_samples) / len(memory_samples)
        
        print(f"📊 资源使用统计:")
        print(f"   平均CPU: {avg_cpu:.1f}%")
        print(f"   峰值CPU: {max_cpu:.1f}%")
        print(f"   平均内存: {avg_memory:.1f}GB")

def main():
    """主测试函数"""
    server_url = "http://localhost:8005"
    
    # 检查服务器状态
    try:
        response = requests.get(f"{server_url}/health", timeout=5)
        response.raise_for_status()
        print("✅ FAISS服务器运行正常")
    except Exception as e:
        print(f"❌ FAISS服务器连接失败: {e}")
        return
    
    print("\n" + "="*60)
    print("🧪 内存带宽优化效果测试")
    print("="*60)
    
    # 测试不同的ef_search值
    test_configs = [
        {"ef_search": 32, "name": "低内存访问 (ef_search=32)"},
        {"ef_search": 64, "name": "优化配置 (ef_search=64)"},
        {"ef_search": 100, "name": "默认配置 (ef_search=100)"},
        {"ef_search": 128, "name": "高精度 (ef_search=128)"},
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n🔬 测试配置: {config['name']}")
        print("-" * 40)
        
        # 启动资源监控
        monitor_thread = threading.Thread(
            target=monitor_system_resources, 
            args=(30,), 
            daemon=True
        )
        monitor_thread.start()
        
        # 执行基准测试
        result = benchmark_concurrent_requests(
            server_url, 
            num_requests=50, 
            num_threads=10, 
            ef_search=config["ef_search"]
        )
        
        results[config["name"]] = result
        
        # 等待监控完成
        time.sleep(2)
    
    # 输出对比结果
    print("\n" + "="*60)
    print("📊 性能对比总结")
    print("="*60)
    
    for name, result in results.items():
        print(f"{name}:")
        print(f"  QPS: {result['qps']:.2f}")
        print(f"  平均响应时间: {result['avg_response_time']*1000:.2f}ms")
        print(f"  成功率: {result['successful_requests']/(result['successful_requests']+result['failed_requests'])*100:.1f}%")
        print()

if __name__ == "__main__":
    main()
