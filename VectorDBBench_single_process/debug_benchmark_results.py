#!/usr/bin/env python3
"""
调查为什么基准测试没有生成详细结果（QPS、时延、召回率等）
"""

import os
import json
from pathlib import Path
import time

def check_results_directories():
    """检查结果目录结构"""
    print("🔍 检查结果目录结构")
    print("=" * 40)
    
    # 检查主要结果目录
    results_dirs = [
        "results",
        "vectordb_bench/results", 
        "logs",
        "metrics"
    ]
    
    for dir_path in results_dirs:
        path = Path(dir_path)
        if path.exists():
            print(f"📁 {dir_path}:")
            files = list(path.rglob("*"))
            if files:
                for file in sorted(files)[:10]:  # 只显示前10个
                    if file.is_file():
                        size = file.stat().st_size
                        mod_time = time.ctime(file.stat().st_mtime)
                        print(f"   📄 {file.relative_to(path)}: {size} bytes ({mod_time})")
                if len(files) > 10:
                    print(f"   ... 还有 {len(files) - 10} 个文件")
            else:
                print(f"   ❌ 空目录")
        else:
            print(f"❌ {dir_path}: 不存在")

def analyze_recent_logs():
    """分析最近的日志文件"""
    print(f"\n📋 分析最近的日志文件")
    print("=" * 40)
    
    log_paths = [
        "logs",
        "vectordb_bench/logs",
        "."
    ]
    
    log_files = []
    for log_dir in log_paths:
        log_path = Path(log_dir)
        if log_path.exists():
            # 查找日志文件
            for pattern in ["*.log", "*benchmark*.txt", "*result*.json", "*result*.csv"]:
                log_files.extend(log_path.glob(pattern))
    
    # 按修改时间排序
    if log_files:
        log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        print("📄 最近的日志文件:")
        for log_file in log_files[:5]:
            mod_time = time.ctime(log_file.stat().st_mtime)
            size = log_file.stat().st_size
            print(f"   📄 {log_file}: {size} bytes ({mod_time})")
    else:
        print("❌ 未找到日志文件")

def check_vectordb_bench_results():
    """检查 VectorDBBench 框架的结果生成机制"""
    print(f"\n🔧 检查 VectorDBBench 结果生成机制")
    print("=" * 40)
    
    try:
        # 检查结果相关的模块
        print("🔍 检查结果生成相关模块:")
        
        # 检查 results 相关代码
        result_modules = [
            "vectordb_bench/interface.py",
            "vectordb_bench/cli/cli.py",
            "vectordb_bench/backend/task_runner.py"
        ]
        
        for module_path in result_modules:
            path = Path(module_path)
            if path.exists():
                print(f"   ✅ {module_path}")
            else:
                print(f"   ❌ {module_path}")
        
        # 尝试找到结果保存逻辑
        print(f"\n🔍 查找结果保存相关代码:")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def analyze_benchmark_execution():
    """分析基准测试执行过程"""
    print(f"\n⚡ 分析基准测试执行过程")
    print("=" * 40)
    
    print("从你的测试输出分析:")
    print("✅ 任务创建成功:")
    print("   • 生成了任务 UUID: fa20baac100b48db8f04985a7dcb6e2c")
    print("   • 生成了任务 UUID: 87fc9ef425ab434897737b887c6975e6")
    print("   • 任务配置正确: host='127.0.0.1', port=8002")
    print("")
    
    print("⚠️  问题分析:")
    print("   • 每个测试只用了 5 秒（太短了）")
    print("   • 可能没有达到生成详细结果的阈值")
    print("   • 可能是连接或数据问题导致快速结束")
    print("")
    
    print("🎯 可能的原因:")
    print("   1. 远程服务器连接问题（虽然连接成功但操作失败）")
    print("   2. 数据集加载问题")
    print("   3. 测试配置问题（时间太短）")
    print("   4. 结果保存路径问题")
    print("   5. 权限问题")

def suggest_debugging_steps():
    """建议调试步骤"""
    print(f"\n🔧 建议的调试步骤")
    print("=" * 40)
    
    print("1️⃣ 检查详细日志:")
    print("   ```bash")
    print("   # 查找最近的日志文件")
    print("   find . -name '*.log' -mtime -1 | head -5")
    print("   find . -name '*result*' -mtime -1 | head -5")
    print("   ```")
    print("")
    
    print("2️⃣ 运行更详细的测试:")
    print("   ```bash")
    print("   # 增加详细输出和更长测试时间")
    print("   python run_faiss_benchmark_enhanced.py \\")
    print("       --mode remote \\")
    print("       --host 127.0.0.1 \\")
    print("       --port 8002 \\")
    print("       --index-type Flat \\")
    print("       --verbose")
    print("   ```")
    print("")
    
    print("3️⃣ 检查 FAISS 服务器日志:")
    print("   • 查看服务器端是否有错误日志")
    print("   • 确认数据实际传输和处理情况")
    print("")
    
    print("4️⃣ 使用单步调试:")
    print("   ```python")
    print("   # 直接调用 CLI run 函数进行调试")
    print("   from vectordb_bench.cli.cli import run")
    print("   from vectordb_bench.backend.clients import DB")
    print("   # ... 配置详细的参数")
    print("   ```")

def check_faiss_server_response():
    """检查 FAISS 服务器实际响应"""
    print(f"\n🌐 检查 FAISS 服务器实际响应")
    print("=" * 40)
    
    print("建议手动测试服务器 API:")
    print("")
    print("```bash")
    print("# 测试服务器状态")
    print("curl http://127.0.0.1:8002/docs")
    print("")
    print("# 测试创建索引")
    print("curl -X POST http://127.0.0.1:8002/create_index \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -d '{\"index_type\": \"Flat\", \"dim\": 768}'")
    print("")
    print("# 检查服务器日志输出")
    print("```")

if __name__ == "__main__":
    print("🔍 基准测试结果调试分析")
    print("=" * 50)
    
    check_results_directories()
    analyze_recent_logs() 
    check_vectordb_bench_results()
    analyze_benchmark_execution()
    suggest_debugging_steps()
    check_faiss_server_response()
    
    print(f"\n💡 总结:")
    print("基准测试任务已创建并提交，但可能由于以下原因没有生成详细结果:")
    print("• 测试时间太短（5秒）")
    print("• 远程连接或数据处理问题") 
    print("• 结果保存配置问题")
    print("需要进一步调试来确定具体原因。")
