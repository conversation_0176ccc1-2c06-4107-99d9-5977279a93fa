#!/usr/bin/env python3
"""
自动化批次向量加载脚本
用于分批加载10M向量到FAISS索引，避免超时问题
"""

import requests
import json
import time
import sys
import logging
from datetime import datetime

# 配置
FAISS_SERVER_URL = "http://localhost:8005"
BATCH_SIZE = 500000   # 每批500K向量
TOTAL_BATCHES = 20    # 总共20批 = 10M向量
BATCH_TIMEOUT = 3600  # 每批最大等待时间（60分钟）
CHECK_INTERVAL = 30   # 状态检查间隔（秒）

# 配置日志 - 同时输出到控制台和文件
log_file = "/home/<USER>/VectorDBBench/batch_loader.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, mode='a', encoding='utf-8'),
        logging.StreamHandler()  # 同时输出到控制台
    ]
)
logger = logging.getLogger(__name__)

def log_message(message):
    """打印带时间戳的日志消息，同时输出到控制台和日志文件"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_msg = f"[{timestamp}] {message}"
    print(formatted_msg)  # 控制台输出
    logger.info(message)  # 日志文件输出

def check_server_health():
    """检查FAISS服务器健康状态"""
    try:
        response = requests.get(f"{FAISS_SERVER_URL}/health", timeout=10)
        if response.status_code == 200:
            return True
        else:
            log_message(f"❌ 服务器健康检查失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        log_message(f"❌ 服务器连接失败: {e}")
        return False

def get_server_status():
    """获取服务器状态"""
    try:
        response = requests.get(f"{FAISS_SERVER_URL}/status", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            log_message(f"❌ 获取状态失败: HTTP {response.status_code}")
            return None
    except Exception as e:
        log_message(f"❌ 获取状态异常: {e}")
        return None

def create_batch_index(batch_num, is_incremental=False):
    """创建批次索引"""
    log_message(f"🚀 开始第{batch_num}批向量加载...")

    payload = {
        "dim": 768,
        "index_type": "HNSW",
        "m": 30,
        "ef_construction": 360,
        "batch_limit": BATCH_SIZE,
        "incremental": is_incremental
    }

    log_message(f"📋 请求参数: batch_limit={BATCH_SIZE:,}, incremental={is_incremental}")

    try:
        log_message(f"📡 发送请求到 {FAISS_SERVER_URL}/create_index")
        response = requests.post(
            f"{FAISS_SERVER_URL}/create_index",
            json=payload,
            timeout=BATCH_TIMEOUT
        )

        log_message(f"📨 收到响应: HTTP {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            message = result.get('message', '成功')
            vectors_loaded = result.get('vectors_loaded', 'N/A')
            log_message(f"✅ 第{batch_num}批完成: {message}")
            log_message(f"📊 服务器报告加载向量: {vectors_loaded}")
            return True
        else:
            log_message(f"❌ 第{batch_num}批失败: HTTP {response.status_code}")
            log_message(f"   响应内容: {response.text}")
            return False

    except requests.exceptions.Timeout:
        log_message(f"⏰ 第{batch_num}批超时（{BATCH_TIMEOUT}秒）")
        return False
    except requests.exceptions.ConnectionError as e:
        log_message(f"🔌 第{batch_num}批连接错误: {e}")
        return False
    except Exception as e:
        log_message(f"❌ 第{batch_num}批异常: {e}")
        return False

def wait_for_server_ready():
    """等待服务器准备就绪"""
    log_message("⏳ 等待服务器准备就绪...")
    
    for i in range(60):  # 最多等待30分钟
        if check_server_health():
            status = get_server_status()
            if status and status.get("status") == "ready":
                log_message("✅ 服务器已准备就绪")
                return True
        
        time.sleep(30)
        log_message(f"   等待中... ({i+1}/60)")
    
    log_message("❌ 服务器准备超时")
    return False

def main():
    """主函数"""
    log_message("🎯 开始自动化批次向量加载")
    log_message(f"   目标: {TOTAL_BATCHES}批 × {BATCH_SIZE:,}向量 = {TOTAL_BATCHES * BATCH_SIZE:,}向量")
    log_message(f"   服务器: {FAISS_SERVER_URL}")

    # 检查服务器状态
    if not check_server_health():
        log_message("❌ 服务器不可用，退出")
        sys.exit(1)

    # 获取初始状态
    initial_status = get_server_status()
    current_vectors = 0
    if initial_status:
        current_vectors = initial_status.get("vectors_loaded", 0)
        log_message(f"📊 当前已加载向量: {current_vectors:,}")

    # 计算起始批次
    start_batch = (current_vectors // BATCH_SIZE) + 1
    if start_batch > TOTAL_BATCHES:
        log_message("✅ 所有批次已完成！")
        return

    # 🔧 智能恢复：如果发现已有向量，说明之前的批次可能已完成
    if current_vectors > 0:
        completed_batches = current_vectors // BATCH_SIZE
        log_message(f"🔍 检测到已完成 {completed_batches} 批次，共 {current_vectors:,} 向量")
        log_message(f"🔄 从第{start_batch}批继续")
    else:
        log_message(f"🔄 从第{start_batch}批开始")

    # 执行批次加载
    success_count = 0

    for batch_num in range(start_batch, TOTAL_BATCHES + 1):
        log_message(f"\n{'='*50}")
        log_message(f"📦 第{batch_num}/{TOTAL_BATCHES}批")

        # 🔑 关键修正：只有第一批是非增量的，所有后续批次都是增量的
        # 但如果从中间批次开始（比如第5批），那么第5批也应该是增量的
        is_incremental = (batch_num > 1) or (current_vectors > 0)

        if is_incremental:
            log_message(f"🔄 增量模式：向现有索引添加第{batch_num}批数据")
        else:
            log_message(f"🏗️ 创建新索引：第{batch_num}批数据")

        # 执行批次
        if create_batch_index(batch_num, is_incremental):
            success_count += 1
            log_message(f"✅ 第{batch_num}批成功完成")

            # 检查状态
            status = get_server_status()
            if status:
                vectors_loaded = status.get("vectors_loaded", 0)
                log_message(f"📊 累计向量: {vectors_loaded:,}")

                # 验证向量数量是否符合预期
                expected_vectors = batch_num * BATCH_SIZE
                if vectors_loaded >= expected_vectors:
                    log_message(f"✅ 向量数量验证通过: {vectors_loaded:,} >= {expected_vectors:,}")
                else:
                    log_message(f"⚠️  向量数量不符合预期: {vectors_loaded:,} < {expected_vectors:,}")

            # 批次间短暂休息
            if batch_num < TOTAL_BATCHES:
                log_message("⏸️  批次间休息5秒...")
                time.sleep(5)
        else:
            log_message(f"❌ 第{batch_num}批失败")

            # 自动重试一次
            log_message("🔄 自动重试一次...")
            time.sleep(10)
            if create_batch_index(batch_num, is_incremental):
                success_count += 1
                log_message(f"✅ 第{batch_num}批重试成功")
                continue

            # 询问是否继续
            log_message("❌ 重试也失败了")
            break  # 自动停止，不再询问用户
    
    # 最终报告
    log_message(f"\n{'='*50}")
    log_message("📋 最终报告")
    log_message(f"   成功批次: {success_count}/{TOTAL_BATCHES}")
    log_message(f"   预期向量: {success_count * BATCH_SIZE:,}")
    
    # 最终状态检查
    final_status = get_server_status()
    if final_status:
        final_vectors = final_status.get("vectors_loaded", 0)
        log_message(f"   实际向量: {final_vectors:,}")
        
        if final_vectors >= TOTAL_BATCHES * BATCH_SIZE:
            log_message("🎉 所有10M向量加载完成！")
        else:
            log_message(f"⚠️  还需加载: {(TOTAL_BATCHES * BATCH_SIZE) - final_vectors:,}向量")
    
    log_message("🏁 自动化批次加载结束")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_message("\n🛑 用户中断脚本")
        sys.exit(1)
    except Exception as e:
        log_message(f"\n❌ 脚本异常: {e}")
        sys.exit(1)
