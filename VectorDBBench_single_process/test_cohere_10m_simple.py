#!/usr/bin/env python3
"""
使用内置库测试 Cohere 10M 数据集
"""

import json
import time
import random
import sys

# 兼容Python 2/3的urllib
try:
    # Python 3
    from urllib.request import urlopen, Request
    from urllib.error import URLError, HTTPError
    from urllib.parse import urlencode
except ImportError:
    # Python 2
    from urllib2 import urlopen, Request, URLError, HTTPError
    from urllib import urlencode

def http_request(url, data=None, timeout=30):
    """发送HTTP请求"""
    try:
        if data:
            # POST请求
            data_json = json.dumps(data).encode('utf-8')
            req = Request(url, data=data_json)
            req.add_header('Content-Type', 'application/json')
        else:
            # GET请求
            req = Request(url)
        
        response = urlopen(req, timeout=timeout)
        response_data = response.read().decode('utf-8')
        return json.loads(response_data), response.getcode()
    except HTTPError as e:
        return None, e.code
    except Exception as e:
        print(f"请求错误: {e}")
        return None, 0

def test_cohere_10m():
    """测试Cohere 10M数据集"""
    base_url = "http://10.1.180.71:8001"
    
    print("🧪 Cohere 768维 10M 数据集测试")
    print("=" * 60)
    
    # 1. 测试连接
    print("🔌 测试服务器连接...")
    data, code = http_request(f"{base_url}/")
    
    if code == 200:
        print("✅ 服务器连接成功")
        print(f"📊 服务器响应: {data}")
    else:
        print(f"❌ 连接失败，状态码: {code}")
        return False
    
    # 2. 获取服务器信息
    print("\n📋 获取服务器信息...")
    data, code = http_request(f"{base_url}/info")
    
    if code == 200:
        print("📊 服务器信息:")
        for key, value in data.items():
            print(f"   {key}: {value}")
    else:
        print(f"⚠️ 无法获取服务器信息，状态码: {code}")
    
    # 3. 创建768维HNSW索引
    print("\n🏗️ 创建768维HNSW索引 (匹配Cohere数据集)...")
    
    create_data = {
        "dim": 768,
        "index_type": "HNSW",
        "m": 16,
        "ef_construction": 200
    }
    
    print(f"📋 索引参数: {create_data}")
    print("⏳ 正在创建索引并加载数据集...")
    
    start_time = time.time()
    data, code = http_request(f"{base_url}/create_index", create_data, timeout=1800)  # 30分钟超时
    end_time = time.time()
    
    if code == 200:
        duration = end_time - start_time
        print(f"✅ 索引创建成功!")
        print(f"⏱️ 创建耗时: {duration:.2f} 秒 ({duration/60:.1f} 分钟)")
        print(f"📊 创建结果: {data}")
    else:
        print(f"❌ 索引创建失败，状态码: {code}")
        if data:
            print(f"错误信息: {data}")
        return False
    
    # 4. 检查索引状态
    print("\n📊 检查索引状态...")
    data, code = http_request(f"{base_url}/status")
    
    if code == 200:
        print("当前索引状态:")
        for key, value in data.items():
            if key in ['total_vectors', 'vectors_count', 'vectors_loaded']:
                print(f"   {key}: {value:,}")
            else:
                print(f"   {key}: {value}")
        
        total_vectors = data.get('total_vectors', 0)
        if total_vectors >= 1000000:
            print(f"✅ 成功加载大规模数据集: {total_vectors:,} 个向量")
        else:
            print(f"⚠️ 向量数量: {total_vectors:,}")
    else:
        print(f"⚠️ 无法获取状态，状态码: {code}")
    
    # 5. 生成随机查询向量并测试搜索
    print("\n🔍 测试搜索性能...")
    
    # 生成768维随机向量
    query_vector = [random.random() for _ in range(768)]
    
    search_times = []
    num_tests = 5
    
    for i in range(num_tests):
        search_data = {
            "query": query_vector,
            "topk": 100
        }
        
        start_time = time.time()
        data, code = http_request(f"{base_url}/search", search_data, timeout=30)
        end_time = time.time()
        
        if code == 200:
            search_time = end_time - start_time
            search_times.append(search_time)
            
            ids = data.get('ids', [[]])[0] if data.get('ids') else []
            distances = data.get('distances', [[]])[0] if data.get('distances') else []
            
            print(f"🎯 搜索 {i+1}/{num_tests}: {search_time*1000:.2f}ms, 返回 {len(ids)} 个结果")
            
            if i == 0:  # 显示第一次搜索的详细结果
                print(f"   前5个结果ID: {ids[:5]}")
                print(f"   前5个距离: {[round(d, 4) for d in distances[:5]]}")
        else:
            print(f"❌ 搜索失败，状态码: {code}")
    
    # 6. 计算性能指标
    if search_times:
        avg_latency = sum(search_times) / len(search_times) * 1000  # ms
        min_latency = min(search_times) * 1000
        max_latency = max(search_times) * 1000
        qps = 1.0 / (sum(search_times) / len(search_times))
        
        print(f"\n📈 性能统计 (基于 {len(search_times)} 次搜索):")
        print(f"   平均延迟: {avg_latency:.2f} ms")
        print(f"   最小延迟: {min_latency:.2f} ms") 
        print(f"   最大延迟: {max_latency:.2f} ms")
        print(f"   估算QPS: {qps:.2f}")
        
        # 评估性能等级
        if qps > 100:
            print("🚀 性能等级: 优秀")
        elif qps > 50:
            print("⚡ 性能等级: 良好")
        elif qps > 20:
            print("✅ 性能等级: 一般")
        else:
            print("⚠️ 性能等级: 需要优化")
    
    return True

def main():
    success = test_cohere_10m()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Cohere 10M 数据集测试完成")
        print("🎯 数据集特征:")
        print("   - 维度: 768")
        print("   - 向量数量: 10,000,000")
        print("   - 数据类型: Cohere文本嵌入")
        print("   - 距离度量: COSINE")
        print("   - 索引类型: HNSW")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
