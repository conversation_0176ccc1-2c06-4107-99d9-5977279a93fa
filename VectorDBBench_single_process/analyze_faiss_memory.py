#!/usr/bin/env python3
"""
分析FAISS内存使用和Copy-on-Write破坏的具体原因
"""

import os
import sys
import time
import numpy as np
import faiss
import psutil
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_memory_usage():
    """获取当前进程内存使用"""
    process = psutil.Process()
    memory_info = process.memory_info()
    return {
        "rss_gb": memory_info.rss / (1024**3),  # 实际物理内存
        "vms_gb": memory_info.vms / (1024**3),  # 虚拟内存
        "shared_gb": getattr(memory_info, 'shared', 0) / (1024**3),  # 共享内存
        "percent": process.memory_percent()
    }

def analyze_faiss_index_structure(index_path):
    """分析FAISS索引的内存结构"""
    logger.info(f"🔍 分析FAISS索引结构: {index_path}")
    
    file_size_gb = os.path.getsize(index_path) / (1024**3)
    logger.info(f"📁 文件大小: {file_size_gb:.2f} GB")
    
    # 记录加载前内存
    memory_before = get_memory_usage()
    logger.info(f"📊 加载前内存: {memory_before['rss_gb']:.2f} GB")
    
    try:
        # 加载索引
        logger.info("🔄 加载索引...")
        start_time = time.time()
        index = faiss.read_index(index_path)
        load_time = time.time() - start_time
        
        # 记录加载后内存
        memory_after = get_memory_usage()
        memory_increase = memory_after['rss_gb'] - memory_before['rss_gb']
        
        logger.info(f"✅ 索引加载完成:")
        logger.info(f"   加载时间: {load_time:.2f}s")
        logger.info(f"   向量数量: {index.ntotal:,}")
        logger.info(f"   维度: {index.d}")
        logger.info(f"   索引类型: {type(index).__name__}")
        
        logger.info(f"📊 内存使用分析:")
        logger.info(f"   文件大小: {file_size_gb:.2f} GB")
        logger.info(f"   内存增加: {memory_increase:.2f} GB")
        logger.info(f"   内存/文件比: {memory_increase/file_size_gb:.2f}x")
        
        # 分析HNSW特定属性
        if hasattr(index, 'hnsw'):
            logger.info(f"🔗 HNSW参数:")
            logger.info(f"   M: {index.hnsw.M}")
            logger.info(f"   efConstruction: {index.hnsw.efConstruction}")
            logger.info(f"   max_level: {index.hnsw.max_level}")
            logger.info(f"   entry_point: {index.hnsw.entry_point}")
            
            # 计算理论内存需求
            theoretical_vector_memory = index.ntotal * index.d * 4 / (1024**3)
            theoretical_graph_memory = estimate_hnsw_graph_memory(index)
            
            logger.info(f"🧮 理论内存需求:")
            logger.info(f"   向量数据: {theoretical_vector_memory:.2f} GB")
            logger.info(f"   HNSW图: {theoretical_graph_memory:.2f} GB")
            logger.info(f"   总计: {theoretical_vector_memory + theoretical_graph_memory:.2f} GB")
        
        return index, memory_increase
        
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        return None, 0

def estimate_hnsw_graph_memory(index):
    """估算HNSW图结构的内存使用"""
    if not hasattr(index, 'hnsw'):
        return 0
    
    M = index.hnsw.M
    ntotal = index.ntotal
    max_level = index.hnsw.max_level
    
    # HNSW图结构内存估算
    # 每个节点在每层的连接数约为M，每个连接4字节(int32)
    # 层数分布：level 0有所有节点，level 1有1/2，level 2有1/4...
    
    total_connections = 0
    for level in range(max_level + 1):
        nodes_at_level = ntotal // (2 ** level) if level > 0 else ntotal
        connections_per_node = M * 2 if level == 0 else M  # level 0有更多连接
        total_connections += nodes_at_level * connections_per_node
    
    # 每个连接4字节，再加上其他元数据
    graph_memory_bytes = total_connections * 4 * 1.5  # 1.5倍考虑元数据
    graph_memory_gb = graph_memory_bytes / (1024**3)
    
    return graph_memory_gb

def test_search_memory_impact(index):
    """测试搜索操作对内存的影响"""
    logger.info("🔍 测试搜索操作的内存影响...")
    
    if index is None:
        logger.error("❌ 索引为空，无法测试")
        return
    
    # 创建测试查询
    query = np.random.random((1, index.d)).astype(np.float32)
    
    # 测试不同的搜索参数
    test_cases = [
        {"k": 10, "ef": None},
        {"k": 100, "ef": None},
        {"k": 10, "ef": 200},
        {"k": 100, "ef": 500},
    ]
    
    for i, case in enumerate(test_cases):
        logger.info(f"📊 测试用例 {i+1}: k={case['k']}, ef={case['ef']}")
        
        memory_before = get_memory_usage()
        
        try:
            # 设置搜索参数
            if hasattr(index, 'hnsw') and case['ef']:
                original_ef = index.hnsw.efSearch
                index.hnsw.efSearch = case['ef']  # 🚨 这里会触发写操作！
            
            # 执行搜索
            start_time = time.time()
            distances, indices = index.search(query, case['k'])
            search_time = time.time() - start_time
            
            memory_after = get_memory_usage()
            memory_change = memory_after['rss_gb'] - memory_before['rss_gb']
            
            logger.info(f"   搜索时间: {search_time:.4f}s")
            logger.info(f"   内存变化: {memory_change:.4f} GB")
            
            # 恢复原始参数
            if hasattr(index, 'hnsw') and case['ef']:
                index.hnsw.efSearch = original_ef
                
        except Exception as e:
            logger.error(f"   搜索失败: {e}")

def analyze_copy_on_write_breaking():
    """分析Copy-on-Write破坏的具体原因"""
    logger.info("🔍 分析Copy-on-Write破坏的原因...")
    
    logger.info("📋 FAISS搜索过程中的写操作:")
    logger.info("1. 🚨 index.hnsw.efSearch = value  # 修改搜索参数")
    logger.info("2. 🚨 搜索过程中的内部缓存更新")
    logger.info("3. 🚨 访问计数器更新")
    logger.info("4. 🚨 临时数据结构分配")
    
    logger.info("📋 Copy-on-Write触发条件:")
    logger.info("- 任何对共享内存页的写操作")
    logger.info("- 即使是1字节的修改也会复制整个4KB页")
    logger.info("- FAISS索引数据分布在数万个内存页中")
    logger.info("- 一次搜索可能触发数千个页面的复制")
    
    logger.info("📋 内存爆炸计算:")
    logger.info("- 原始索引: 31GB")
    logger.info("- 实际内存需求: ~80GB (包括HNSW图)")
    logger.info("- 4个worker进程: 4 × 80GB = 320GB")
    logger.info("- 加上系统开销: ~400GB")
    logger.info("- 但系统只有1TB，其他进程也需要内存")

def calculate_optimal_memory_requirements():
    """计算最优内存需求"""
    logger.info("🧮 计算最优内存需求...")
    
    # 10M向量，768维，float32
    vector_data_gb = 10_000_000 * 768 * 4 / (1024**3)
    
    # HNSW图结构 (M=30, 估算)
    # 保守估算：每个向量平均60个连接，每个连接4字节
    graph_data_gb = 10_000_000 * 60 * 4 / (1024**3)
    
    # FAISS内部开销 (缓存、临时数据等)
    overhead_gb = (vector_data_gb + graph_data_gb) * 0.2
    
    total_per_process = vector_data_gb + graph_data_gb + overhead_gb
    
    logger.info(f"📊 单进程内存需求分析:")
    logger.info(f"   向量数据: {vector_data_gb:.2f} GB")
    logger.info(f"   HNSW图: {graph_data_gb:.2f} GB") 
    logger.info(f"   内部开销: {overhead_gb:.2f} GB")
    logger.info(f"   单进程总计: {total_per_process:.2f} GB")
    
    logger.info(f"📊 多进程场景:")
    for workers in [1, 2, 4, 8]:
        total_memory = workers * total_per_process
        logger.info(f"   {workers} workers: {total_memory:.2f} GB")
        if total_memory > 1000:  # 1TB
            logger.warning(f"     ⚠️ 超过1TB限制!")
    
    logger.info(f"📊 理想情况 (真正的共享内存):")
    logger.info(f"   共享数据: {vector_data_gb + graph_data_gb:.2f} GB")
    logger.info(f"   每进程开销: {overhead_gb:.2f} GB")
    for workers in [1, 2, 4, 8]:
        shared_memory = vector_data_gb + graph_data_gb + workers * overhead_gb
        logger.info(f"   {workers} workers: {shared_memory:.2f} GB")

def main():
    """主函数"""
    logger.info("🎯 FAISS内存使用深度分析")
    logger.info("=" * 60)
    
    # 分析理论内存需求
    calculate_optimal_memory_requirements()
    
    # 分析Copy-on-Write问题
    analyze_copy_on_write_breaking()
    
    # 如果索引文件存在，进行实际分析
    index_path = "/home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index"
    if os.path.exists(index_path):
        logger.info("🔍 分析实际索引文件...")
        index, memory_increase = analyze_faiss_index_structure(index_path)
        
        if index:
            test_search_memory_impact(index)
    else:
        logger.warning(f"⚠️ 索引文件不存在: {index_path}")
    
    logger.info("🎉 分析完成!")

if __name__ == "__main__":
    main()
