#!/usr/bin/env python3
"""
简化版多核CPU测试脚本 - 通过手动插入数据进行测试
"""

import time
import threading
import requests
import psutil
import random
from concurrent.futures import ThreadPoolExecutor, as_completed

def get_server_pid():
    """获取smart_faiss_server.py的进程ID"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'smart_faiss_server.py' in ' '.join(proc.info['cmdline']):
                return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def monitor_cpu_realtime(duration=20):
    """实时监控CPU使用情况"""
    server_pid = get_server_pid()
    if not server_pid:
        print("❌ 找不到smart_faiss_server.py进程")
        return
    
    try:
        server_process = psutil.Process(server_pid)
        print(f"📊 监控服务器进程 PID: {server_pid}")
        print(f"📊 CPU亲和性: {server_process.cpu_affinity()}")
        print(f"📊 线程数: {server_process.num_threads()}")
        print(f"📊 开始 {duration} 秒CPU监控...")
        print("-" * 100)
        
        start_time = time.time()
        sample_count = 0
        
        while time.time() - start_time < duration:
            # 获取服务器进程CPU使用率
            server_cpu = server_process.cpu_percent()
            
            # 获取每个CPU核心使用率
            per_cpu = psutil.cpu_percent(percpu=True, interval=0.2)
            
            # 统计活跃核心
            active_cores = [i for i, usage in enumerate(per_cpu) if usage > 5.0]
            high_usage_cores = [i for i, usage in enumerate(per_cpu) if usage > 20.0]
            
            # 只显示前16个核心（我们设置的CPU亲和性范围）
            cores_0_15 = per_cpu[:16]
            max_core_usage = max(cores_0_15) if cores_0_15 else 0
            avg_core_usage = sum(cores_0_15) / len(cores_0_15) if cores_0_15 else 0
            
            sample_count += 1
            print(f"[{sample_count:3d}] 服务器CPU: {server_cpu:6.1f}% | "
                  f"活跃核心: {len(active_cores):2d} | "
                  f"高负载核心: {len(high_usage_cores):2d} | "
                  f"最大核心: {max_core_usage:5.1f}% | "
                  f"前16核平均: {avg_core_usage:5.1f}%")
            
            # 显示前16个核心的详细使用情况（如果有高使用率的）
            if max_core_usage > 15:
                core_details = [f"{i}:{cores_0_15[i]:4.1f}%" for i in range(len(cores_0_15)) if cores_0_15[i] > 5]
                if core_details:
                    print(f"     高使用率核心: {' '.join(core_details)}")
            
    except psutil.NoSuchProcess:
        print(f"❌ 进程 {server_pid} 不存在")
    except Exception as e:
        print(f"❌ 监控出错: {e}")

def insert_test_data():
    """插入测试数据"""
    print("📝 插入测试数据...")
    
    # 插入一些向量数据
    for i in range(100):
        vector = [random.random() for _ in range(768)]
        data = {
            "id": i,
            "vector": vector
        }
        
        try:
            response = requests.post("http://localhost:8001/insert", json=data, timeout=5)
            if response.status_code == 200:
                if i % 20 == 0:
                    print(f"✅ 已插入 {i+1} 个向量")
            else:
                print(f"❌ 插入向量 {i} 失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 插入向量 {i} 出错: {e}")
            return False
    
    print("✅ 测试数据插入完成")
    return True

def send_search_request():
    """发送搜索请求"""
    query_vector = [random.random() for _ in range(768)]
    data = {
        "query_vector": query_vector,
        "top_k": 10
    }
    
    try:
        response = requests.post("http://localhost:8001/search", json=data, timeout=10)
        if response.status_code == 200:
            return True, response.elapsed.total_seconds()
        else:
            return False, f"HTTP {response.status_code}"
    except Exception as e:
        return False, str(e)

def run_concurrent_search_test(num_threads=16, duration=15):
    """运行并发搜索测试"""
    print(f"\n🚀 并发搜索测试")
    print("=" * 60)
    print(f"🎯 并发线程数: {num_threads}")
    print(f"🎯 测试时长: {duration} 秒")
    
    # 启动CPU监控
    monitor_thread = threading.Thread(target=monitor_cpu_realtime, args=(duration + 5,))
    monitor_thread.daemon = True
    monitor_thread.start()
    
    time.sleep(1)  # 让监控先启动
    
    # 并发测试
    success_count = 0
    total_requests = 0
    
    def worker():
        nonlocal success_count, total_requests
        end_time = time.time() + duration
        
        while time.time() < end_time:
            success, result = send_search_request()
            total_requests += 1
            if success:
                success_count += 1
            time.sleep(0.1)  # 100ms间隔
    
    # 启动并发线程
    threads = []
    start_time = time.time()
    
    for i in range(num_threads):
        t = threading.Thread(target=worker)
        t.start()
        threads.append(t)
    
    # 等待所有线程完成
    for t in threads:
        t.join()
    
    actual_duration = time.time() - start_time
    
    print(f"\n📊 测试结果:")
    print(f"   实际耗时: {actual_duration:.2f}秒")
    print(f"   总请求数: {total_requests}")
    print(f"   成功请求: {success_count}")
    print(f"   成功率: {(success_count/total_requests*100) if total_requests > 0 else 0:.1f}%")
    print(f"   平均QPS: {success_count / actual_duration:.2f}")

def main():
    print("🔧 FAISS服务器多核CPU使用情况测试 (简化版)")
    print("="*60)
    
    # 检查服务器
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行")
            return
        print("✅ 服务器运行正常")
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return
    
    # 插入测试数据
    if not insert_test_data():
        print("❌ 测试数据插入失败")
        return
    
    # 运行不同并发级别的测试
    test_configs = [
        {"threads": 4, "name": "低并发测试 (4线程)"},
        {"threads": 8, "name": "中等并发测试 (8线程)"},
        {"threads": 16, "name": "高并发测试 (16线程)"},
    ]
    
    for config in test_configs:
        print(f"\n{'='*20} {config['name']} {'='*20}")
        run_concurrent_search_test(config["threads"], 15)
        
        print("\n⏱️ 等待3秒...")
        time.sleep(3)
    
    print("\n🎉 所有测试完成!")
    print("💡 请检查上面的CPU监控数据，观察是否使用了多个CPU核心")

if __name__ == "__main__":
    main()
