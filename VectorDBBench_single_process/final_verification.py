#!/usr/bin/env python3
"""
最终验证测试：使用真实数据测试recall
"""

import sys
import os
import numpy as np
import pandas as pd
sys.path.append('/home/<USER>/VectorDBBench')

def final_verification():
    """最终验证recall"""
    
    print("🏁 FAISS 最终验证测试")
    print("=" * 50)
    
    # 加载真实的测试数据
    test_file = "/nas/yvan.chen/milvus/dataset/openai/openai_small_50k/test.parquet"
    neighbors_file = "/nas/yvan.chen/milvus/dataset/openai/openai_small_50k/neighbors.parquet"
    
    print("📊 加载真实测试数据...")
    test_df = pd.read_parquet(test_file)
    neighbors_df = pd.read_parquet(neighbors_file)
    
    print(f"   测试向量数: {len(test_df)}")
    print(f"   邻居数据: {len(neighbors_df)}")
    
    # 加载我们的索引
    from vectordb_bench.backend.clients.faiss_local.faiss_local import FaissLocalClient
    from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
    from vectordb_bench.backend.clients.api import MetricType
    
    db_config = FaissLocalConfig(
        db_label="faiss_real_data_performance1536d50k",
        index_type="HNSW"
    )
    
    db_case_config = HNSWConfig(
        m=16,
        ef_construction=200,
        ef_search=64,
        metric_type=MetricType.COSINE
    )
    
    print("🔍 创建客户端并加载索引...")
    client = FaissLocalClient(
        dim=1536,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="FaissLocalCollection"
    )
    
    # 执行搜索测试
    print("🎯 执行搜索测试...")
    
    # 取前10个测试向量
    test_samples = 10
    correct_predictions = 0
    
    for i in range(min(test_samples, len(test_df))):
        query_vector = test_df.iloc[i]['emb']
        true_neighbors = neighbors_df.iloc[i]['neighbors'][:10]  # 真实的前10个邻居
        
        with client.init():
            predicted_neighbors = client.search_embedding(query_vector, k=10)
        
        # 计算交集
        if len(predicted_neighbors) > 0:
            # 转换为集合进行比较
            true_set = set(true_neighbors)
            pred_set = set(int(x) for x in predicted_neighbors)
            
            intersection = len(true_set.intersection(pred_set))
            recall = intersection / len(true_set)
            
            correct_predictions += recall
            
            if i < 3:  # 显示前3个详细结果
                print(f"   查询 {i+1}:")
                print(f"     真实邻居: {true_neighbors[:5]}...")
                print(f"     预测邻居: {predicted_neighbors[:5]}")
                print(f"     交集: {intersection}")
                print(f"     Recall: {recall:.3f}")
        else:
            print(f"   查询 {i+1}: 搜索失败")
    
    avg_recall = correct_predictions / test_samples
    print(f"\n📊 最终结果:")
    print(f"   测试样本数: {test_samples}")
    print(f"   平均 Recall@10: {avg_recall:.3f}")
    
    if avg_recall > 0:
        print("   ✅ Recall 计算成功!")
    else:
        print("   ⚠️  Recall 为0，可能存在ID映射问题")

if __name__ == "__main__":
    final_verification()
