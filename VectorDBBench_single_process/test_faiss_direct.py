#!/usr/bin/env python3
"""
Direct FAISS Local Test - Bypass VectorDBBench framework hanging issue
"""

import os
import sys
import numpy as np
import time

# Set environment
os.environ['DATASET_LOCAL_DIR'] = '/home/<USER>/VectorDBBench/dataset'
sys.path.insert(0, '/home/<USER>/VectorDBBench')

def test_faiss_local_direct():
    """Test FAISS local client directly without the full framework"""
    
    print("🧪 Testing FAISS Local Client Directly")
    
    try:
        from vectordb_bench.backend.clients.faiss_local.faiss_local import FaissLocalClient
        from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
        from vectordb_bench.backend.clients.api import MetricType
        
        print("✅ Successfully imported FAISS local components")
        
        # Create config
        config = {
            'db_label': 'test_2025_07_20',
            'index_type': 'HNSW',
            'version': '',
            'note': ''
        }
        
        case_config = HNSWConfig(m=16, ef_construction=200, ef_search=100)
        
        print("🔧 Creating FAISS local client...")
        
        # Create client
        client = FaissLocalClient(
            dim=1536,
            db_config=config,
            db_case_config=case_config,
            collection_name="TestCollection"
        )
        
        print("✅ FAISS local client created successfully")
        
        # Test with small sample data
        print("📊 Testing with sample data...")
        
        # Generate sample vectors
        num_vectors = 100
        vectors = np.random.rand(num_vectors, 1536).astype(np.float32)
        ids = list(range(num_vectors))
        
        print(f"📥 Inserting {num_vectors} test vectors...")
        
        with client.init():
            # Insert data
            start_time = time.time()
            result, error = client.insert_embeddings(
                embeddings=vectors.tolist(),
                metadata=ids
            )
            insert_time = time.time() - start_time
            
            if error:
                print(f"❌ Insert failed: {error}")
                return False
            else:
                print(f"✅ Inserted {result} vectors in {insert_time:.2f}s")
            
            # Test search
            print("🔍 Testing search...")
            query_vector = vectors[0].tolist()  # Use first vector as query
            
            start_time = time.time()
            search_result = client.search_embedding(
                query=query_vector,
                k=10
            )
            search_time = time.time() - start_time
            
            if search_result:
                print(f"✅ Search completed in {search_time:.3f}s")
                print(f"📋 Found {len(search_result)} results")
                print(f"🎯 Top result ID: {search_result[0][0]}, Score: {search_result[0][1]:.6f}")
            else:
                print("❌ Search failed")
                return False
        
        print("🎊 FAISS local client test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dataset_loading():
    """Test dataset loading separately"""
    print("\n📂 Testing dataset loading...")
    
    try:
        import polars as pl
        
        dataset_dir = os.environ['DATASET_LOCAL_DIR']
        openai_dir = os.path.join(dataset_dir, 'openai')
        
        # Try to read train data
        train_file = os.path.join(openai_dir, 'shuffle_train.parquet')
        if os.path.exists(train_file):
            print(f"📄 Reading: {train_file}")
            df = pl.read_parquet(train_file)
            print(f"✅ Loaded {len(df)} rows, columns: {df.columns}")
            return True
        else:
            print(f"❌ Train file not found: {train_file}")
            return False
            
    except Exception as e:
        print(f"❌ Dataset loading failed: {e}")
        return False

if __name__ == "__main__":
    print("🎯 FAISS Local Direct Test")
    print("=" * 50)
    
    # Test dataset loading first
    dataset_ok = test_dataset_loading()
    
    if dataset_ok:
        # Test FAISS client
        client_ok = test_faiss_local_direct()
        
        if client_ok:
            print("\n🎊 All tests passed! FAISS local is working correctly.")
            print("\n💡 The hang issue is likely in the VectorDBBench framework's")
            print("   dataset download logic, not in FAISS local itself.")
            print("\n🔧 Try running with --dry-run first to see configuration:")
            print("   vectordbbench faisslocalhnsw --case-type Performance1536D50K --dry-run")
        else:
            print("\n❌ FAISS local client test failed")
    else:
        print("\n❌ Dataset loading test failed")
