2025-07-30 07:27:33,741 - INFO - 🎯 开始自动化批次向量加载
2025-07-30 07:27:33,741 - INFO -    目标: 20批 × 500,000向量 = 10,000,000向量
2025-07-30 07:27:33,741 - INFO -    服务器: http://localhost:8005
2025-07-30 07:27:33,749 - INFO - 📊 当前已加载向量: 0
2025-07-30 07:27:33,749 - INFO - 🔄 从第1批开始
2025-07-30 07:27:33,749 - INFO - 
==================================================
2025-07-30 07:27:33,749 - INFO - 📦 第1/20批
2025-07-30 07:27:33,749 - INFO - 🚀 开始第1批向量加载...
2025-07-30 07:37:33,851 - INFO - ⏰ 第1批超时（600秒）
2025-07-30 07:37:33,853 - INFO - ❌ 第1批失败
2025-07-30 08:10:04,448 - INFO - 🎯 开始自动化批次向量加载
2025-07-30 08:10:04,449 - INFO -    目标: 20批 × 500,000向量 = 10,000,000向量
2025-07-30 08:10:04,449 - INFO -    服务器: http://localhost:8005
2025-07-30 08:10:04,457 - INFO - 📊 当前已加载向量: 0
2025-07-30 08:10:04,458 - INFO - 🔄 从第1批开始
2025-07-30 08:10:04,458 - INFO - 
==================================================
2025-07-30 08:10:04,458 - INFO - 📦 第1/20批
2025-07-30 08:10:04,458 - INFO - 🏗️ 创建新索引：第1批数据
2025-07-30 08:10:04,458 - INFO - 🚀 开始第1批向量加载...
2025-07-30 08:10:04,458 - INFO - 📋 请求参数: batch_limit=500,000, incremental=False
2025-07-30 08:10:04,458 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 08:20:04,561 - INFO - ⏰ 第1批超时（600秒）
2025-07-30 08:20:04,561 - INFO - ❌ 第1批失败
2025-07-30 08:20:04,561 - INFO - 🔄 自动重试一次...
2025-07-30 08:20:14,572 - INFO - 🚀 开始第1批向量加载...
2025-07-30 08:20:14,572 - INFO - 📋 请求参数: batch_limit=500,000, incremental=False
2025-07-30 08:20:14,572 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 08:30:14,623 - INFO - ⏰ 第1批超时（600秒）
2025-07-30 08:30:14,623 - INFO - ❌ 重试也失败了
2025-07-30 08:30:14,623 - INFO - 
==================================================
2025-07-30 08:30:14,623 - INFO - 📋 最终报告
2025-07-30 08:30:14,623 - INFO -    成功批次: 0/20
2025-07-30 08:30:14,623 - INFO -    预期向量: 0
2025-07-30 08:30:24,636 - INFO - ❌ 获取状态异常: HTTPConnectionPool(host='localhost', port=8005): Read timed out. (read timeout=10)
2025-07-30 08:30:24,636 - INFO - 🏁 自动化批次加载结束
2025-07-30 08:48:59,442 - INFO - 🎯 开始自动化批次向量加载
2025-07-30 08:48:59,442 - INFO -    目标: 20批 × 500,000向量 = 10,000,000向量
2025-07-30 08:48:59,442 - INFO -    服务器: http://localhost:8005
2025-07-30 08:48:59,449 - INFO - 📊 当前已加载向量: 500,000
2025-07-30 08:48:59,450 - INFO - 🔍 检测到已完成 1 批次，共 500,000 向量
2025-07-30 08:48:59,450 - INFO - 🔄 从第2批继续
2025-07-30 08:48:59,450 - INFO - 
==================================================
2025-07-30 08:48:59,450 - INFO - 📦 第2/20批
2025-07-30 08:48:59,450 - INFO - 🔄 增量模式：向现有索引添加第2批数据
2025-07-30 08:48:59,450 - INFO - 🚀 开始第2批向量加载...
2025-07-30 08:48:59,450 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 08:48:59,450 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 09:08:07,601 - INFO - 📨 收到响应: HTTP 200
2025-07-30 09:08:07,601 - INFO - ✅ 第2批完成: 索引创建成功: HNSW
2025-07-30 09:08:07,601 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 09:08:07,601 - INFO - ✅ 第2批成功完成
2025-07-30 09:08:07,608 - INFO - 📊 累计向量: 1,000,000
2025-07-30 09:08:07,608 - INFO - ✅ 向量数量验证通过: 1,000,000 >= 1,000,000
2025-07-30 09:08:07,608 - INFO - ⏸️  批次间休息5秒...
2025-07-30 09:08:12,613 - INFO - 
==================================================
2025-07-30 09:08:12,613 - INFO - 📦 第3/20批
2025-07-30 09:08:12,613 - INFO - 🔄 增量模式：向现有索引添加第3批数据
2025-07-30 09:08:12,613 - INFO - 🚀 开始第3批向量加载...
2025-07-30 09:08:12,614 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 09:08:12,614 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 09:28:21,649 - INFO - 📨 收到响应: HTTP 200
2025-07-30 09:28:21,649 - INFO - ✅ 第3批完成: 索引创建成功: HNSW
2025-07-30 09:28:21,649 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 09:28:21,649 - INFO - ✅ 第3批成功完成
2025-07-30 09:28:21,653 - INFO - 📊 累计向量: 1,500,000
2025-07-30 09:28:21,653 - INFO - ✅ 向量数量验证通过: 1,500,000 >= 1,500,000
2025-07-30 09:28:21,653 - INFO - ⏸️  批次间休息5秒...
2025-07-30 09:28:26,659 - INFO - 
==================================================
2025-07-30 09:28:26,659 - INFO - 📦 第4/20批
2025-07-30 09:28:26,659 - INFO - 🔄 增量模式：向现有索引添加第4批数据
2025-07-30 09:28:26,659 - INFO - 🚀 开始第4批向量加载...
2025-07-30 09:28:26,659 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 09:28:26,659 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 09:49:04,921 - INFO - 📨 收到响应: HTTP 200
2025-07-30 09:49:04,921 - INFO - ✅ 第4批完成: 索引创建成功: HNSW
2025-07-30 09:49:04,921 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 09:49:04,921 - INFO - ✅ 第4批成功完成
2025-07-30 09:49:04,925 - INFO - 📊 累计向量: 2,000,000
2025-07-30 09:49:04,926 - INFO - ✅ 向量数量验证通过: 2,000,000 >= 2,000,000
2025-07-30 09:49:04,926 - INFO - ⏸️  批次间休息5秒...
2025-07-30 09:49:09,931 - INFO - 
==================================================
2025-07-30 09:49:09,931 - INFO - 📦 第5/20批
2025-07-30 09:49:09,931 - INFO - 🔄 增量模式：向现有索引添加第5批数据
2025-07-30 09:49:09,931 - INFO - 🚀 开始第5批向量加载...
2025-07-30 09:49:09,931 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 09:49:09,931 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 10:10:21,529 - INFO - 📨 收到响应: HTTP 200
2025-07-30 10:10:21,529 - INFO - ✅ 第5批完成: 索引创建成功: HNSW
2025-07-30 10:10:21,529 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 10:10:21,529 - INFO - ✅ 第5批成功完成
2025-07-30 10:10:21,533 - INFO - 📊 累计向量: 2,500,000
2025-07-30 10:10:21,533 - INFO - ✅ 向量数量验证通过: 2,500,000 >= 2,500,000
2025-07-30 10:10:21,533 - INFO - ⏸️  批次间休息5秒...
2025-07-30 10:10:26,539 - INFO - 
==================================================
2025-07-30 10:10:26,539 - INFO - 📦 第6/20批
2025-07-30 10:10:26,539 - INFO - 🔄 增量模式：向现有索引添加第6批数据
2025-07-30 10:10:26,539 - INFO - 🚀 开始第6批向量加载...
2025-07-30 10:10:26,539 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 10:10:26,539 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 10:31:55,657 - INFO - 📨 收到响应: HTTP 200
2025-07-30 10:31:55,657 - INFO - ✅ 第6批完成: 索引创建成功: HNSW
2025-07-30 10:31:55,657 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 10:31:55,657 - INFO - ✅ 第6批成功完成
2025-07-30 10:31:55,661 - INFO - 📊 累计向量: 3,000,000
2025-07-30 10:31:55,661 - INFO - ✅ 向量数量验证通过: 3,000,000 >= 3,000,000
2025-07-30 10:31:55,661 - INFO - ⏸️  批次间休息5秒...
2025-07-30 10:32:00,667 - INFO - 
==================================================
2025-07-30 10:32:00,667 - INFO - 📦 第7/20批
2025-07-30 10:32:00,667 - INFO - 🔄 增量模式：向现有索引添加第7批数据
2025-07-30 10:32:00,667 - INFO - 🚀 开始第7批向量加载...
2025-07-30 10:32:00,667 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 10:32:00,667 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 10:55:20,647 - INFO - 📨 收到响应: HTTP 200
2025-07-30 10:55:20,647 - INFO - ✅ 第7批完成: 索引创建成功: HNSW
2025-07-30 10:55:20,647 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 10:55:20,647 - INFO - ✅ 第7批成功完成
2025-07-30 10:55:20,652 - INFO - 📊 累计向量: 3,500,000
2025-07-30 10:55:20,653 - INFO - ✅ 向量数量验证通过: 3,500,000 >= 3,500,000
2025-07-30 10:55:20,653 - INFO - ⏸️  批次间休息5秒...
2025-07-30 10:55:25,658 - INFO - 
==================================================
2025-07-30 10:55:25,658 - INFO - 📦 第8/20批
2025-07-30 10:55:25,658 - INFO - 🔄 增量模式：向现有索引添加第8批数据
2025-07-30 10:55:25,658 - INFO - 🚀 开始第8批向量加载...
2025-07-30 10:55:25,658 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 10:55:25,658 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 11:18:08,283 - INFO - 📨 收到响应: HTTP 200
2025-07-30 11:18:08,284 - INFO - ✅ 第8批完成: 索引创建成功: HNSW
2025-07-30 11:18:08,284 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 11:18:08,284 - INFO - ✅ 第8批成功完成
2025-07-30 11:18:08,288 - INFO - 📊 累计向量: 4,000,000
2025-07-30 11:18:08,288 - INFO - ✅ 向量数量验证通过: 4,000,000 >= 4,000,000
2025-07-30 11:18:08,288 - INFO - ⏸️  批次间休息5秒...
2025-07-30 11:18:13,293 - INFO - 
==================================================
2025-07-30 11:18:13,293 - INFO - 📦 第9/20批
2025-07-30 11:18:13,293 - INFO - 🔄 增量模式：向现有索引添加第9批数据
2025-07-30 11:18:13,293 - INFO - 🚀 开始第9批向量加载...
2025-07-30 11:18:13,293 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 11:18:13,294 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 11:42:57,023 - INFO - 📨 收到响应: HTTP 200
2025-07-30 11:42:57,023 - INFO - ✅ 第9批完成: 索引创建成功: HNSW
2025-07-30 11:42:57,023 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 11:42:57,023 - INFO - ✅ 第9批成功完成
2025-07-30 11:42:57,028 - INFO - 📊 累计向量: 4,500,000
2025-07-30 11:42:57,028 - INFO - ✅ 向量数量验证通过: 4,500,000 >= 4,500,000
2025-07-30 11:42:57,028 - INFO - ⏸️  批次间休息5秒...
2025-07-30 11:43:02,033 - INFO - 
==================================================
2025-07-30 11:43:02,033 - INFO - 📦 第10/20批
2025-07-30 11:43:02,033 - INFO - 🔄 增量模式：向现有索引添加第10批数据
2025-07-30 11:43:02,033 - INFO - 🚀 开始第10批向量加载...
2025-07-30 11:43:02,033 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 11:43:02,034 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 12:34:58,335 - INFO - 🎯 开始自动化批次向量加载
2025-07-30 12:34:58,335 - INFO -    目标: 20批 × 500,000向量 = 10,000,000向量
2025-07-30 12:34:58,335 - INFO -    服务器: http://localhost:8005
2025-07-30 12:34:58,342 - INFO - 📊 当前已加载向量: 0
2025-07-30 12:34:58,342 - INFO - 🔄 从第1批开始
2025-07-30 12:34:58,342 - INFO - 
==================================================
2025-07-30 12:34:58,342 - INFO - 📦 第1/20批
2025-07-30 12:34:58,342 - INFO - 🏗️ 创建新索引：第1批数据
2025-07-30 12:34:58,342 - INFO - 🚀 开始第1批向量加载...
2025-07-30 12:34:58,342 - INFO - 📋 请求参数: batch_limit=500,000, incremental=False
2025-07-30 12:34:58,342 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 12:35:10,395 - INFO - 
🛑 用户中断脚本
2025-07-30 12:35:16,795 - INFO - 🎯 开始自动化批次向量加载
2025-07-30 12:35:16,795 - INFO -    目标: 20批 × 500,000向量 = 10,000,000向量
2025-07-30 12:35:16,795 - INFO -    服务器: http://localhost:8005
2025-07-30 12:35:26,808 - INFO - ❌ 服务器连接失败: HTTPConnectionPool(host='localhost', port=8005): Read timed out. (read timeout=10)
2025-07-30 12:35:26,808 - INFO - ❌ 服务器不可用，退出
2025-07-30 12:43:55,390 - INFO - 🎯 开始自动化批次向量加载
2025-07-30 12:43:55,391 - INFO -    目标: 20批 × 500,000向量 = 10,000,000向量
2025-07-30 12:43:55,391 - INFO -    服务器: http://localhost:8005
2025-07-30 12:43:55,400 - INFO - 📊 当前已加载向量: 0
2025-07-30 12:43:55,400 - INFO - 🔄 从第1批开始
2025-07-30 12:43:55,400 - INFO - 
==================================================
2025-07-30 12:43:55,400 - INFO - 📦 第1/20批
2025-07-30 12:43:55,400 - INFO - 🏗️ 创建新索引：第1批数据
2025-07-30 12:43:55,400 - INFO - 🚀 开始第1批向量加载...
2025-07-30 12:43:55,400 - INFO - 📋 请求参数: batch_limit=500,000, incremental=False
2025-07-30 12:43:55,400 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 12:44:47,084 - INFO - 🎯 开始自动化批次向量加载
2025-07-30 12:44:47,084 - INFO -    目标: 20批 × 500,000向量 = 10,000,000向量
2025-07-30 12:44:47,084 - INFO -    服务器: http://localhost:8005
2025-07-30 12:44:47,093 - INFO - 📊 当前已加载向量: 0
2025-07-30 12:44:47,093 - INFO - 🔄 从第1批开始
2025-07-30 12:44:47,093 - INFO - 
==================================================
2025-07-30 12:44:47,093 - INFO - 📦 第1/20批
2025-07-30 12:44:47,093 - INFO - 🏗️ 创建新索引：第1批数据
2025-07-30 12:44:47,093 - INFO - 🚀 开始第1批向量加载...
2025-07-30 12:44:47,093 - INFO - 📋 请求参数: batch_limit=500,000, incremental=False
2025-07-30 12:44:47,093 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 13:04:17,167 - INFO - 📨 收到响应: HTTP 200
2025-07-30 13:04:17,168 - INFO - ✅ 第1批完成: 索引创建成功: HNSW
2025-07-30 13:04:17,168 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 13:04:17,168 - INFO - ✅ 第1批成功完成
2025-07-30 13:04:17,172 - INFO - 📊 累计向量: 500,000
2025-07-30 13:04:17,172 - INFO - ✅ 向量数量验证通过: 500,000 >= 500,000
2025-07-30 13:04:17,172 - INFO - ⏸️  批次间休息5秒...
2025-07-30 13:04:22,177 - INFO - 
==================================================
2025-07-30 13:04:22,177 - INFO - 📦 第2/20批
2025-07-30 13:04:22,177 - INFO - 🔄 增量模式：向现有索引添加第2批数据
2025-07-30 13:04:22,178 - INFO - 🚀 开始第2批向量加载...
2025-07-30 13:04:22,178 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 13:04:22,178 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 13:28:00,738 - INFO - 📨 收到响应: HTTP 200
2025-07-30 13:28:00,738 - INFO - ✅ 第2批完成: 索引创建成功: HNSW
2025-07-30 13:28:00,738 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 13:28:00,738 - INFO - ✅ 第2批成功完成
2025-07-30 13:28:00,742 - INFO - 📊 累计向量: 1,000,000
2025-07-30 13:28:00,742 - INFO - ✅ 向量数量验证通过: 1,000,000 >= 1,000,000
2025-07-30 13:28:00,742 - INFO - ⏸️  批次间休息5秒...
2025-07-30 13:28:05,747 - INFO - 
==================================================
2025-07-30 13:28:05,747 - INFO - 📦 第3/20批
2025-07-30 13:28:05,747 - INFO - 🔄 增量模式：向现有索引添加第3批数据
2025-07-30 13:28:05,747 - INFO - 🚀 开始第3批向量加载...
2025-07-30 13:28:05,747 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 13:28:05,747 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 13:54:01,309 - INFO - 📨 收到响应: HTTP 200
2025-07-30 13:54:01,310 - INFO - ✅ 第3批完成: 索引创建成功: HNSW
2025-07-30 13:54:01,310 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 13:54:01,310 - INFO - ✅ 第3批成功完成
2025-07-30 13:54:01,313 - INFO - 📊 累计向量: 1,500,000
2025-07-30 13:54:01,314 - INFO - ✅ 向量数量验证通过: 1,500,000 >= 1,500,000
2025-07-30 13:54:01,314 - INFO - ⏸️  批次间休息5秒...
2025-07-30 13:54:06,319 - INFO - 
==================================================
2025-07-30 13:54:06,319 - INFO - 📦 第4/20批
2025-07-30 13:54:06,319 - INFO - 🔄 增量模式：向现有索引添加第4批数据
2025-07-30 13:54:06,319 - INFO - 🚀 开始第4批向量加载...
2025-07-30 13:54:06,319 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 13:54:06,319 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 14:23:08,646 - INFO - 📨 收到响应: HTTP 200
2025-07-30 14:23:08,646 - INFO - ✅ 第4批完成: 索引创建成功: HNSW
2025-07-30 14:23:08,646 - INFO - 📊 服务器报告加载向量: N/A
2025-07-30 14:23:08,646 - INFO - ✅ 第4批成功完成
2025-07-30 14:23:08,648 - INFO - ❌ 获取状态异常: HTTPConnectionPool(host='localhost', port=8005): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff99575b80>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-07-30 14:23:08,648 - INFO - ⏸️  批次间休息5秒...
2025-07-30 14:23:13,653 - INFO - 
==================================================
2025-07-30 14:23:13,653 - INFO - 📦 第5/20批
2025-07-30 14:23:13,653 - INFO - 🔄 增量模式：向现有索引添加第5批数据
2025-07-30 14:23:13,653 - INFO - 🚀 开始第5批向量加载...
2025-07-30 14:23:13,653 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 14:23:13,653 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 14:23:13,655 - INFO - 🔌 第5批连接错误: HTTPConnectionPool(host='localhost', port=8005): Max retries exceeded with url: /create_index (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff995670d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-07-30 14:23:13,655 - INFO - ❌ 第5批失败
2025-07-30 14:23:13,655 - INFO - 🔄 自动重试一次...
2025-07-30 14:23:23,665 - INFO - 🚀 开始第5批向量加载...
2025-07-30 14:23:23,665 - INFO - 📋 请求参数: batch_limit=500,000, incremental=True
2025-07-30 14:23:23,665 - INFO - 📡 发送请求到 http://localhost:8005/create_index
2025-07-30 14:23:23,667 - INFO - 🔌 第5批连接错误: HTTPConnectionPool(host='localhost', port=8005): Max retries exceeded with url: /create_index (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff995675b0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-07-30 14:23:23,667 - INFO - ❌ 重试也失败了
2025-07-30 14:23:23,667 - INFO - 
==================================================
2025-07-30 14:23:23,667 - INFO - 📋 最终报告
2025-07-30 14:23:23,667 - INFO -    成功批次: 4/20
2025-07-30 14:23:23,667 - INFO -    预期向量: 2,000,000
2025-07-30 14:23:23,668 - INFO - ❌ 获取状态异常: HTTPConnectionPool(host='localhost', port=8005): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0xffff99582d90>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-07-30 14:23:23,668 - INFO - 🏁 自动化批次加载结束
