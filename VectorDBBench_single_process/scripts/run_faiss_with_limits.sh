#!/usr/bin/env bash

# 创建目录
mkdir -p metrics results

# 设置环境变量
export OMP_NUM_THREADS=$(nproc)  # 控制 FAISS 并行
ulimit -n 1048576                # 高并发时别忘了调 fd

# 启动资源监控
pidstat -ruh -p ALL 1 > metrics/pidstat.log &
PIDSTAT_PID=$!

# 运行基准测试
echo "开始运行 FAISS 基准测试..."
timeout 2h vectordbbench batchcli \
  --batch-config-file faiss_768d1m.yml \
  --dry-run=false

# 测试结束，停止监控
kill $PIDSTAT_PID

# 清理历史
history -c 