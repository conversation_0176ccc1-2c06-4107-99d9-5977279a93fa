#!/usr/bin/env python3
"""
直接测试 FAISS benchmark 完整流程
"""

import os
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

# 设置环境变量
os.environ['DATASET_LOCAL_DIR'] = '/home/<USER>/VectorDBBench/dataset'

def test_faiss_benchmark():
    """测试完整的 FAISS benchmark 流程"""
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
        from vectordb_bench.backend.cases import CaseType
        from vectordb_bench.backend.clients.api import MetricType
        
        # 获取数据集的 metric_type
        case_type = CaseType.Performance1536D50K
        case = case_type.case_cls()
        dataset_metric_type = case.dataset.data.metric_type
        
        print(f"Dataset metric type: {dataset_metric_type}")
        print(f"Dataset path: {case.dataset.data.full_name}")
        
        # 创建配置
        db_config = FaissLocalConfig(
            db_label="test_faiss_direct",
            index_type="HNSW"
        )
        
        db_case_config = HNSWConfig(
            m=16,
            ef_construction=200,
            ef_search=64,
            metric_type=dataset_metric_type,
        )
        
        print("开始运行 FAISS benchmark...")
        
        # 运行测试
        parameters = {
            'case_type': 'Performance1536D50K',
            'k': 10,
            'num_concurrency': [1],
            'concurrency_duration': 10,
            'concurrency_timeout': 3600,
            'db_label': 'test_faiss_direct',
            'load': True,
            'search_serial': True,
            'search_concurrent': True,
            'drop_old': True,
            'dry_run': False,
            'task_label': None,
            'custom_case': {}
        }
        
        run(
            db=DB.FaissLocal,
            db_config=db_config,
            db_case_config=db_case_config,
            **parameters
        )
        
        print("✅ FAISS benchmark 完成!")
        return True
        
    except Exception as e:
        print(f"❌ FAISS benchmark 失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_faiss_benchmark()
    sys.exit(0 if success else 1)
