#!/bin/bash
# FAISS Gunicorn 多进程启动脚本

echo "🚀 启动 FAISS Gunicorn 多进程服务"
echo "================================="

# 设置环境变量
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset
export PYTHONPATH=/home/<USER>/VectorDBBench:$PYTHONPATH

# 获取时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "🔧 环境配置:"
echo "   数据集路径: $DATASET_LOCAL_DIR"
echo "   Python路径: $PYTHONPATH"
echo "   启动时间: $TIMESTAMP"

# 检查依赖
echo ""
echo "🔍 检查依赖..."
if ! python3.11 -c "import gunicorn" 2>/dev/null; then
    echo "❌ Gunicorn未安装，正在安装..."
    pip install gunicorn
    if [ $? -eq 0 ]; then
        echo "✅ Gunicorn安装完成"
    else
        echo "❌ Gunicorn安装失败"
        exit 1
    fi
else
    echo "✅ Gunicorn已安装"
fi

# 停止现有服务
echo ""
echo "🛑 停止现有服务..."
pkill -f "smart_faiss_server" 2>/dev/null || true
sleep 2

# 启动服务
echo ""
echo "🚀 启动 FAISS 服务 (Gunicorn 多进程模式)..."
echo "   配置: 8 workers, preload_app=True"
echo "   地址: http://0.0.0.0:8005"
echo "   日志: faiss_gunicorn_${TIMESTAMP}.log"

nohup python3.11 smart_faiss_server.py \
    --host 0.0.0.0 \
    --port 8005 \
    --use-gunicorn \
    --workers 8 \
    --preload \
    > faiss_gunicorn_${TIMESTAMP}.log 2>&1 &

FAISS_PID=$!
echo "✅ FAISS服务启动完成"
echo "   主进程PID: $FAISS_PID"

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
sleep 5

# 验证服务状态
echo "🔍 验证服务状态..."
if curl -s http://localhost:8005/status > /dev/null 2>&1; then
    echo "✅ FAISS服务运行正常"
else
    echo "⚠️  服务可能还在启动中，请稍后检查"
fi

# 显示进程信息
echo ""
echo "📊 进程信息:"
ps aux | grep smart_faiss_server | grep -v grep || echo "   进程信息获取失败"

# 显示管理信息
echo ""
echo "📋 服务信息:"
echo "   服务地址: http://0.0.0.0:8005"
echo "   主进程PID: $FAISS_PID"
echo "   日志文件: faiss_gunicorn_${TIMESTAMP}.log"
echo ""
echo "🔧 管理命令:"
echo "   查看日志: tail -f faiss_gunicorn_${TIMESTAMP}.log"
echo "   查看进程: ps aux | grep smart_faiss_server"
echo "   停止服务: kill $FAISS_PID"
echo "   API测试: curl http://localhost:8005/status"
echo ""
echo "📊 性能测试:"
echo "   基础测试: ./test_faiss_gunicorn_performance.sh"
echo "   验证服务: ./verify_faiss_gunicorn.sh"
