#!/usr/bin/env python3
'''
测试512并发修复效果
'''

import sys
import os
sys.path.append('/home/<USER>/VectorDBBench')

from vectordb_bench.backend.runner.mp_runner import MultiProcessingSearchRunner
import multiprocessing as mp
import time

class MockDB:
    def init(self):
        return self
    
    def __enter__(self):
        return self
    
    def __exit__(self, *args):
        pass
    
    def prepare_filter(self, filters):
        pass
    
    def search_embedding(self, data, k):
        time.sleep(0.001)  # 模拟搜索
        return []

def test_512_concurrency():
    print("🧪 测试512并发修复效果")
    print("=" * 50)
    
    # 创建模拟数据
    test_data = [[0.1] * 768 for _ in range(100)]
    mock_db = MockDB()
    
    # 创建runner，只测试512并发
    runner = MultiProcessingSearchRunner(
        db=mock_db,
        test_data=test_data,
        concurrencies=[512],  # 只测试512
        duration=5,  # 短时间测试
        concurrency_timeout=300  # 5分钟超时
    )
    
    try:
        print("🚀 开始512并发测试...")
        start_time = time.time()
        
        result = runner.run()
        
        end_time = time.time()
        print(f"✅ 512并发测试成功!")
        print(f"   耗时: {end_time - start_time:.2f} 秒")
        print(f"   结果: {result}")
        
    except Exception as e:
        print(f"❌ 512并发测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_512_concurrency()
