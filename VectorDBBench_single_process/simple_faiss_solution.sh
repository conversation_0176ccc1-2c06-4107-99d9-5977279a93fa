#!/bin/bash
"""
超级简单的FAISS解决方案 - 直接使用本地数据
"""

echo "🎯 超级简单FAISS解决方案"
echo "======================================"

# 设置正确的数据集路径
export DATASET_LOCAL_DIR="/home/<USER>/VectorDBBench/dataset"

# 禁用所有AWS/网络下载
export AWS_ACCESS_KEY_ID=""
export AWS_SECRET_ACCESS_KEY=""
export AWS_DEFAULT_REGION=""
export AWS_PROFILE=""

echo "✅ 环境设置:"
echo "   📂 数据集: $DATASET_LOCAL_DIR"
echo "   🚫 AWS访问: 已禁用"

echo ""
echo "🚀 选择FAISS模式:"
echo ""
echo "1️⃣ FAISS Local (本地模式，无需服务器):"
echo "   vectordbbench faisslocalhnsw --case-type Performance1536D50K --m 16 --ef-construction 200 --ef-search 100"
echo ""
echo "2️⃣ FAISS Remote (需要服务器):"
echo "   python -m vectordb_bench.cli.vectordbbench faissremote --uri localhost:8011 --case-type Performance1536D50K"
echo ""

# 提供快速测试选项
echo "🧪 快速测试FAISS Local (10秒):"
echo "======================================"

# 使用timeout避免挂起
timeout 60 vectordbbench faisslocalhnsw \
    --case-type Performance1536D50K \
    --m 16 --ef-construction 200 --ef-search 100 \
    --concurrency-duration 10 \
    --num-concurrency 1 2>&1 | head -20

echo ""
echo "💡 如果上面的测试成功启动（显示进度条），说明环境正确！"
echo "   你可以去掉timeout和head限制，运行完整测试"
echo ""
echo "📋 完整FAISS Local命令:"
echo "vectordbbench faisslocalhnsw \\"
echo "    --case-type Performance1536D50K \\"
echo "    --m 16 --ef-construction 200 --ef-search 100 \\"
echo "    --concurrency-duration 300 \\"
echo "    --num-concurrency 8,16,32"
