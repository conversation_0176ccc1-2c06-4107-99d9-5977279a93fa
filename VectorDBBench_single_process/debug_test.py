#!/usr/bin/env python3

import os
import sys
import logging
import pathlib

# 设置环境变量
os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'

# 添加项目路径
sys.path.insert(0, '/home/<USER>/VectorDBBench')

# 设置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s | %(levelname)s: %(message)s (%(filename)s:%(lineno)d)',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/home/<USER>/VectorDBBench/logs/debug_test.log')
    ]
)

print("=== VectorDBBench FAISS 测试开始 ===")
print(f"数据集路径: {os.environ['DATASET_LOCAL_DIR']}")

try:
    from vectordb_bench.backend.dataset import Dataset
    from vectordb_bench.backend.cases import Performance1536D50K
    
    print("正在检查数据集配置...")
    
    # 检查数据集管理器
    manager = Dataset.OPENAI.manager(50_000)
    print(f"数据集路径: {manager.data_dir}")
    print(f"数据集名称: {manager.data.name}")
    print(f"数据集维度: {manager.data.dim}")
    print(f"所有属性: {[attr for attr in dir(manager) if not attr.startswith('_')]}")
    
    # 检查路径是否存在
    if manager.data_dir.exists():
        print("✓ 数据集路径存在")
        files = list(manager.data_dir.iterdir())
        print(f"数据集文件: {[f.name for f in files]}")
    else:
        print("✗ 数据集路径不存在")
        
    print("正在初始化案例...")
    case = Performance1536D50K()
    print(f"案例名称: {case.name}")
    print(f"案例描述: {case.description}")
    
    print("=== 配置检查完成 ===")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
