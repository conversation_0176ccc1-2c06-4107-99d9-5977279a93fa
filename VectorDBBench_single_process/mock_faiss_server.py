#!/usr/bin/env python3
"""
模拟FAISS服务器 - 用于演示智能缓存功能
"""

import json
import time
import random
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

class MockFAISSServer(BaseHTTPRequestHandler):
    # 模拟数据存储
    vectors_count = 2000  # 模拟已有2000个向量
    dimension = 768
    index_type = "Flat"
    
    def _set_headers(self, status=200):
        self.send_response(status)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_OPTIONS(self):
        self._set_headers()

    def do_GET(self):
        path = urlparse(self.path).path
        
        if path == "/status":
            self._set_headers()
            response = {
                "status": "ready",
                "index_type": self.index_type,
                "total_vectors": self.vectors_count,
                "vectors_count": self.vectors_count,
                "dimension": self.dimension,
                "cache_info": {
                    "initialized": True,
                    "last_insert_count": 0
                }
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif path == "/health":
            self._set_headers()
            response = {"status": "healthy", "timestamp": time.time()}
            self.wfile.write(json.dumps(response).encode())
            
        else:
            self._set_headers(404)
            self.wfile.write(json.dumps({"error": "Not found"}).encode())

    def do_POST(self):
        path = urlparse(self.path).path
        content_length = int(self.headers.get('Content-Length', 0))
        
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data.decode())
            except:
                data = {}
        else:
            data = {}

        if path == "/create_index":
            self._set_headers()
            dim = data.get("dim", 768)
            index_type = data.get("index_type", "Flat")
            
            # 更新服务器状态
            MockFAISSServer.dimension = dim
            MockFAISSServer.index_type = index_type
            
            response = {
                "message": f"Successfully created {index_type} index",
                "dimension": dim,
                "index_type": index_type
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif path == "/insert_bulk":
            self._set_headers()
            vectors = data.get("vectors", [])
            
            # 模拟插入延迟
            time.sleep(0.1)
            
            # 更新向量计数
            MockFAISSServer.vectors_count += len(vectors)
            
            response = {
                "message": f"Successfully inserted {len(vectors)} vectors",
                "total_vectors": MockFAISSServer.vectors_count
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif path == "/search":
            self._set_headers()
            query = data.get("query", [])
            topk = data.get("topk", 10)
            
            # 模拟搜索延迟
            time.sleep(0.05)
            
            # 生成模拟结果
            ids = [random.randint(0, MockFAISSServer.vectors_count-1) for _ in range(min(topk, MockFAISSServer.vectors_count))]
            distances = [random.uniform(0.1, 1.0) for _ in range(len(ids))]
            
            response = {
                "ids": [ids],
                "distances": [distances]
            }
            self.wfile.write(json.dumps(response).encode())
            
        elif path == "/reset":
            self._set_headers()
            MockFAISSServer.vectors_count = 0
            MockFAISSServer.dimension = 0
            MockFAISSServer.index_type = "unknown"
            
            response = {"message": "Index reset successfully"}
            self.wfile.write(json.dumps(response).encode())
            
        else:
            self._set_headers(404)
            self.wfile.write(json.dumps({"error": "Not found"}).encode())

    def log_message(self, format, *args):
        # 自定义日志格式
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def run_server(port=8000):
    server_address = ('', port)
    httpd = HTTPServer(server_address, MockFAISSServer)
    print(f"🚀 模拟FAISS服务器启动成功!")
    print(f"📍 服务地址: http://0.0.0.0:{port}")
    print(f"📊 模拟数据: {MockFAISSServer.vectors_count:,} 个向量, 维度: {MockFAISSServer.dimension}")
    print(f"💡 这是一个演示服务器，用于测试智能缓存功能")
    print("-" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        httpd.shutdown()

if __name__ == "__main__":
    run_server()
