#!/bin/bash

set -e  

# 设置环境变量和日志
export DATASET_LOCAL_DIR="/nas/yvan.chen/milvus/dataset"
export PYTHONUNBUFFERED=1

echo "=== FAISS 基准测试开始 ==="
echo "时间: $(date)"
echo ""

# 运行简单测试
echo "运行 FAISS 基准测试..."
vectordbbench faisslocalhnsw \
    --case-type Performance1536D50K \
    --m 16 \
    --ef-construction 200 \
    --ef-search 64 \
    --k 10 \
    --num-concurrency "1" \
    --concurrency-duration 10 \
    --db-label "faiss_benchmark_$(date +%H%M%S)" 2>&1 | while IFS= read -r line; do
        echo "[$(date '+%H:%M:%S')] $line"
    done

echo ""
echo "=== 检查结果 ==="

# 检查日志
if [ -f "logs/vectordb_bench.log" ]; then
    echo "最新日志内容:"
    tail -30 logs/vectordb_bench.log
fi

# 检查结果目录
if [ -d "results" ] && [ "$(ls -A results 2>/dev/null)" ]; then
    echo ""
    echo "结果文件:"
    ls -la results/
    echo ""
    echo "结果内容预览:"
    for file in results/*.json; do
        if [ -f "$file" ]; then
            echo "文件: $file"
            head -20 "$file" || echo "无法读取文件内容"
        fi
    done
else
    echo "没有找到结果文件"
fi

echo ""
echo "=== 测试完成 ==="
