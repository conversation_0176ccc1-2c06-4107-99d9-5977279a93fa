#!/usr/bin/env python3
"""
测试FAISS智能缓存客户端
"""

import sys
import os
import random

# 添加项目路径
sys.path.append('/home/<USER>/VectorDBBench')

from vectordb_bench.backend.clients.faiss.faiss import FaissClient
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig

def test_smart_cache():
    """测试智能缓存功能"""
    print("=" * 60)
    print("🧪 FAISS智能缓存测试")
    print("=" * 60)
    
    # 配置客户端
    db_config = {
        'host': 'localhost',
        'port': 8000,
        'index_type': 'Flat'
    }
    
    db_case_config = FaissDBCaseConfig()
    
    print("\n1️⃣ 创建FAISS客户端...")
    client = FaissClient(
        dim=768,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="test_collection"
    )
    
    # 测试智能缓存
    print("\n2️⃣ 测试智能缓存 - 插入100个向量...")
    test_embeddings = []
    for i in range(100):
        # 生成随机768维向量
        vector = [random.uniform(-1, 1) for _ in range(768)]
        test_embeddings.append(vector)
    
    metadata = list(range(100))
    
    # 这里应该会触发智能缓存
    with client.init():
        inserted_count, error = client.insert_embeddings(test_embeddings, metadata)
        
        if error:
            print(f"❌ 插入失败: {error}")
        else:
            print(f"✅ 处理了 {inserted_count} 个向量")
    
    # 测试搜索功能
    print("\n3️⃣ 测试搜索功能...")
    query_vector = [random.uniform(-1, 1) for _ in range(768)]
    
    try:
        with client.init():
            search_results = client.search_embedding(query_vector, k=10)
            print(f"✅ 搜索返回 {len(search_results)} 个结果")
            print(f"   结果ID: {search_results[:5]}...")  # 显示前5个结果
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
    
    print("\n4️⃣ 再次测试插入（应该再次触发缓存）...")
    more_embeddings = []
    for i in range(50):
        vector = [random.uniform(-1, 1) for _ in range(768)]
        more_embeddings.append(vector)
    
    with client.init():
        inserted_count, error = client.insert_embeddings(more_embeddings, list(range(50)))
        
        if error:
            print(f"❌ 插入失败: {error}")
        else:
            print(f"✅ 处理了 {inserted_count} 个向量")

def test_different_config():
    """测试不同配置（应该不会触发缓存）"""
    print("\n" + "=" * 60)
    print("🔧 测试不同配置（维度不匹配）")
    print("=" * 60)
    
    # 使用不同的维度，应该不会匹配缓存
    db_config = {
        'host': 'localhost',
        'port': 8000,
        'index_type': 'Flat'
    }
    
    print("\n1️⃣ 创建维度=512的客户端（应该不匹配缓存）...")
    client = FaissClient(
        dim=512,  # 不同的维度
        db_config=db_config,
        db_case_config=FaissDBCaseConfig(),
        collection_name="test_collection_512"
    )
    
    # 这应该不会触发缓存
    test_embeddings = []
    for i in range(50):
        vector = [random.uniform(-1, 1) for _ in range(512)]
        test_embeddings.append(vector)
    
    print("\n2️⃣ 插入50个512维向量...")
    with client.init():
        inserted_count, error = client.insert_embeddings(test_embeddings, list(range(50)))
        
        if error:
            print(f"❌ 插入失败: {error}")
        else:
            print(f"✅ 处理了 {inserted_count} 个向量")

if __name__ == "__main__":
    try:
        # 测试智能缓存
        test_smart_cache()
        
        # 测试不同配置
        test_different_config()
        
        print("\n" + "=" * 60)
        print("🎉 测试完成！")
        print("💡 查看上面的日志，确认智能缓存是否按预期工作")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
