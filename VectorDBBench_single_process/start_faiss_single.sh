#!/bin/bash
# FAISS 单进程启动脚本 (对比测试用)

echo "🔄 启动 FAISS 单进程服务 (对比测试)"
echo "================================="

# 设置环境变量
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset
export PYTHONPATH=/home/<USER>/VectorDBBench:$PYTHONPATH

# 获取时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "🔧 环境配置:"
echo "   数据集路径: $DATASET_LOCAL_DIR"
echo "   Python路径: $PYTHONPATH"
echo "   启动时间: $TIMESTAMP"

# 停止现有服务
echo ""
echo "🛑 停止现有服务..."
pkill -f "smart_faiss_server" 2>/dev/null || true
sleep 2

# 启动服务
echo ""
echo "🔄 启动 FAISS 服务 (Uvicorn 单进程模式)..."
echo "   配置: 1 worker, 200 并发限制"
echo "   地址: http://0.0.0.0:8005"
echo "   日志: faiss_single_${TIMESTAMP}.log"

nohup python3.11 smart_faiss_server.py \
    --host 0.0.0.0 \
    --port 8005 \
    > faiss_single_${TIMESTAMP}.log 2>&1 &

FAISS_PID=$!
echo "✅ FAISS服务启动完成"
echo "   进程PID: $FAISS_PID"

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
sleep 3

# 验证服务状态
echo "🔍 验证服务状态..."
if curl -s http://localhost:8005/status > /dev/null 2>&1; then
    echo "✅ FAISS服务运行正常"
else
    echo "⚠️  服务可能还在启动中，请稍后检查"
fi

# 显示进程信息
echo ""
echo "📊 进程信息:"
ps aux | grep smart_faiss_server | grep -v grep || echo "   进程信息获取失败"

# 显示管理信息
echo ""
echo "📋 服务信息:"
echo "   服务地址: http://0.0.0.0:8005"
echo "   进程PID: $FAISS_PID"
echo "   日志文件: faiss_single_${TIMESTAMP}.log"
echo ""
echo "🔧 管理命令:"
echo "   查看日志: tail -f faiss_single_${TIMESTAMP}.log"
echo "   查看进程: ps aux | grep smart_faiss_server"
echo "   停止服务: kill $FAISS_PID"
echo "   API测试: curl http://localhost:8005/status"
echo ""
echo "⚠️  注意: 这是单进程模式，性能有限"
echo "   推荐使用: ./start_faiss_gunicorn.sh"
