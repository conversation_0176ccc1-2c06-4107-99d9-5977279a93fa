#!/usr/bin/env python3
"""
简化的真实数据集测试，使用更可控的方式
"""

import os
import sys
import time

# 添加 VectorDBBench 到路径
sys.path.append('/home/<USER>/VectorDBBench')

def test_real_data_simple():
    """简化的真实数据测试"""
    
    print("🧪 FAISS 真实数据集简化测试")
    print("=" * 50)
    
    # 检查数据集
    dataset_path = "/nas/yvan.chen/milvus/dataset/openai/openai_small_50k"
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return
    
    print(f"✅ 数据集路径: {dataset_path}")
    
    # 导入必要模块
    from vectordb_bench.backend.clients.faiss_local.faiss_local import FaissLocalClient
    from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
    from vectordb_bench.backend.clients.api import MetricType
    
    # 创建配置
    db_config = FaissLocalConfig(
        db_label="faiss_real_test_simple",
        index_type="HNSW"
    )
    
    db_case_config = HNSWConfig(
        m=16,
        ef_construction=200,
        ef_search=64,
        metric_type=MetricType.COSINE
    )
    
    # 创建客户端
    print("1️⃣ 创建 FAISS 客户端")
    client = FaissLocalClient(
        dim=1536,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="real_test_collection"
    )
    
    # 读取一小部分真实数据
    print("2️⃣ 读取真实数据")
    import pandas as pd
    
    train_file = os.path.join(dataset_path, "shuffle_train.parquet")
    test_file = os.path.join(dataset_path, "test.parquet") 
    
    if not os.path.exists(train_file):
        print(f"❌ 训练文件不存在: {train_file}")
        return
    
    # 读取少量数据进行测试
    train_df = pd.read_parquet(train_file)
    test_df = pd.read_parquet(test_file)
    
    print(f"   训练数据形状: {train_df.shape}")
    print(f"   测试数据形状: {test_df.shape}")
    
    # 只取前1000条数据进行测试
    sample_size = 1000
    train_sample = train_df.head(sample_size)
    
    # 准备向量和ID
    vectors = train_sample['emb'].tolist()
    ids = list(range(len(vectors)))
    
    print(f"   使用 {len(vectors)} 个向量进行测试")
    
    # 插入数据
    print("3️⃣ 插入向量数据")
    start_time = time.time()
    
    with client.init():
        result = client.insert_embeddings(vectors, ids)
        print(f"   插入结果: {result}")
    
    insert_time = time.time() - start_time
    print(f"   插入耗时: {insert_time:.2f}s")
    
    # 验证文件保存
    print("4️⃣ 验证文件保存")
    if os.path.exists(client.index_file):
        size = os.path.getsize(client.index_file)
        print(f"   ✅ 索引文件: {size} bytes")
    else:
        print(f"   ❌ 索引文件不存在")
        return
    
    # 创建新客户端进行搜索（模拟搜索进程）
    print("5️⃣ 创建新客户端并搜索")
    client2 = FaissLocalClient(
        dim=1536,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="real_test_collection"
    )
    
    # 使用测试数据中的向量进行搜索
    test_vector = test_df.iloc[0]['emb']
    
    start_time = time.time()
    with client2.init():
        results = client2.search_embedding(test_vector, k=10)
    search_time = time.time() - start_time
    
    print(f"   搜索结果: {results}")
    print(f"   搜索耗时: {search_time:.4f}s")
    print(f"   结果数量: {len(results)}")
    
    # 计算简单的性能指标
    if len(results) > 0:
        print("   ✅ 搜索成功")
    else:
        print("   ❌ 搜索失败")
    
    print("6️⃣ 测试完成")

if __name__ == "__main__":
    test_real_data_simple()
