#!/usr/bin/env python3
"""
FAISS服务器多核使用情况诊断工具
检查为什么看起来只使用1个核心
"""

import psutil
import os
import time
import subprocess
import requests
from datetime import datetime

def check_system_info():
    """检查系统基本信息"""
    print("🖥️ 系统信息:")
    print(f"   物理CPU核心: {psutil.cpu_count(logical=False)}")
    print(f"   逻辑CPU核心: {psutil.cpu_count(logical=True)}")
    print(f"   系统负载 (1/5/15分钟): {os.getloadavg()}")
    print()

def check_faiss_processes():
    """检查FAISS进程状态"""
    print("🔍 FAISS进程检查:")
    faiss_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'faiss' in ' '.join(proc.info['cmdline']).lower():
                faiss_processes.append(proc)
                print(f"   PID: {proc.info['pid']}")
                print(f"   命令: {' '.join(proc.info['cmdline'])}")
                print()
        except:
            continue
    
    return faiss_processes

def analyze_process_details(processes):
    """分析进程详细信息"""
    for proc in processes:
        try:
            p = psutil.Process(proc.info['pid'])
            print(f"📊 进程 {proc.info['pid']} 详细信息:")
            
            # CPU亲和性
            try:
                affinity = p.cpu_affinity()
                print(f"   CPU亲和性: {affinity} (共{len(affinity)}个核心)")
            except:
                print("   CPU亲和性: 无法获取")
            
            # 线程数
            try:
                threads = p.num_threads()
                print(f"   线程数: {threads}")
            except:
                print("   线程数: 无法获取")
            
            # CPU使用率
            try:
                cpu_percent = p.cpu_percent(interval=1)
                print(f"   CPU使用率: {cpu_percent:.1f}%")
            except:
                print("   CPU使用率: 无法获取")
            
            # 内存使用
            try:
                memory = p.memory_info()
                print(f"   内存使用: {memory.rss / 1024**2:.1f} MB")
            except:
                print("   内存使用: 无法获取")
            
            # 进程状态
            try:
                status = p.status()
                print(f"   进程状态: {status}")
            except:
                print("   进程状态: 无法获取")
            
            print()
            
        except Exception as e:
            print(f"   ❌ 无法获取进程信息: {e}")
            print()

def check_python_threading():
    """检查Python多线程配置"""
    print("🐍 Python多线程检查:")
    
    # 检查GIL状态
    import sys
    print(f"   Python版本: {sys.version}")
    
    # 检查FAISS多线程设置
    try:
        import faiss
        print(f"   FAISS版本: {faiss.__version__}")
        print(f"   FAISS OpenMP线程数: {faiss.omp_get_max_threads()}")
    except Exception as e:
        print(f"   FAISS检查失败: {e}")
    
    # 检查NumPy多线程
    try:
        import numpy as np
        print(f"   NumPy版本: {np.__version__}")
        # 尝试获取BLAS线程数
        try:
            import numpy.core._multiarray_umath
            print("   NumPy多线程: 可用")
        except:
            print("   NumPy多线程: 检查失败")
    except Exception as e:
        print(f"   NumPy检查失败: {e}")
    
    print()

def test_server_load():
    """测试服务器负载情况"""
    print("⚡ 服务器负载测试:")
    
    servers = [
        ("http://localhost:8001", "smart_faiss_server"),
        ("http://localhost:8002", "legacy_faiss_server")
    ]
    
    for url, name in servers:
        try:
            print(f"   测试 {name} ({url}):")
            
            # 健康检查
            resp = requests.get(f"{url}/health", timeout=5)
            if resp.status_code == 200:
                print(f"   ✅ 服务器响应正常")
                
                # 状态检查
                try:
                    status_resp = requests.get(f"{url}/status", timeout=5)
                    if status_resp.status_code == 200:
                        status = status_resp.json()
                        vectors = status.get('vectors_count', 0) or status.get('total_vectors', 0)
                        print(f"   📊 向量数量: {vectors:,}")
                    else:
                        print(f"   ⚠️ 状态检查失败: {status_resp.status_code}")
                except:
                    print(f"   ⚠️ 无法获取状态信息")
            else:
                print(f"   ❌ 服务器无响应: {resp.status_code}")
        except Exception as e:
            print(f"   ❌ 连接失败: {e}")
        print()

def monitor_real_time_cpu():
    """实时监控CPU使用情况"""
    print("📈 实时CPU监控 (5秒):")
    print("   按Ctrl+C停止...")
    
    try:
        for i in range(5):
            # 获取各核心使用率
            cpu_percent = psutil.cpu_percent(interval=1, percpu=True)
            
            # 只显示有负载的核心
            active_cores = [(i, usage) for i, usage in enumerate(cpu_percent) if usage > 1.0]
            
            print(f"   时刻{i+1}: 活跃核心数: {len(active_cores)}")
            if active_cores:
                for core, usage in active_cores[:10]:  # 只显示前10个活跃核心
                    print(f"     核心{core}: {usage:.1f}%")
            else:
                print("     没有核心使用率超过1%")
            print()
            
    except KeyboardInterrupt:
        print("   监控已停止")

def main():
    """主函数"""
    print("🔧 FAISS服务器多核使用诊断")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 系统信息
    check_system_info()
    
    # 2. FAISS进程检查
    processes = check_faiss_processes()
    
    # 3. 进程详细分析
    if processes:
        analyze_process_details(processes)
    
    # 4. Python多线程检查
    check_python_threading()
    
    # 5. 服务器负载测试
    test_server_load()
    
    # 6. 实时CPU监控
    monitor_real_time_cpu()
    
    print("🎯 诊断建议:")
    print("1. 检查FAISS是否编译了OpenMP支持")
    print("2. 设置环境变量: export OMP_NUM_THREADS=16")
    print("3. 检查是否有CPU密集型操作在单线程中执行")
    print("4. 考虑使用异步处理提高并发")

if __name__ == "__main__":
    main()
