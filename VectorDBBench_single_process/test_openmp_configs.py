#!/usr/bin/env python3.11
"""
测试不同OpenMP配置的性能
比较 8×2 vs 4×4 vs 16×1 配置
"""

import argparse
import time
import subprocess
import requests
import json

def test_config(workers, omp_threads, test_name):
    """测试特定配置的性能"""
    print(f"\n🔧 测试配置: {test_name}")
    print(f"   Workers: {workers}, OpenMP线程: {omp_threads}")
    print(f"   总线程: {workers} × {omp_threads} = {workers * omp_threads}")
    
    # 停止现有服务
    subprocess.run(["pkill", "-f", "smart_faiss_server"], capture_output=True)
    time.sleep(3)
    
    # 启动新配置
    cmd = [
        "numactl", "-C", "0-15",
        "python3.11", "smart_faiss_server.py",
        "--use-gunicorn",
        "--workers", str(workers),
        "--preload"
    ]
    
    print(f"🚀 启动服务: {' '.join(cmd)}")
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        cwd="/home/<USER>/VectorDBBench"
    )
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    for i in range(30):
        try:
            response = requests.get("http://localhost:8005/status", timeout=2)
            if response.status_code == 200:
                print(f"✅ 服务启动成功 (耗时: {i+1}秒)")
                break
        except:
            time.sleep(1)
    else:
        print("❌ 服务启动失败")
        return None
    
    # 运行性能测试
    print("🏃 运行性能测试...")
    test_cmd = [
        "python3.11", "-m", "vectordb_bench.cli.vectordbbench",
        "faissremote",
        "--uri", "http://***********:8005",
        "--case-type", "Performance768D1M",
        "--index-type", "HNSW",
        "--m", "30",
        "--ef-construction", "360",
        "--ef-search", "100",
        "--concurrency-duration", "20",  # 缩短测试时间
        "--num-concurrency", "4,8"      # 只测试关键并发数
    ]
    
    start_time = time.time()
    result = subprocess.run(
        test_cmd,
        capture_output=True,
        text=True,
        cwd="/home/<USER>/VectorDBBench",
        timeout=300  # 5分钟超时
    )
    test_time = time.time() - start_time
    
    # 停止服务
    subprocess.run(["pkill", "-f", "smart_faiss_server"], capture_output=True)
    
    if result.returncode == 0:
        # 解析结果
        output = result.stdout
        qps_lines = [line for line in output.split('\n') if 'qps' in line.lower()]
        
        print(f"✅ 测试完成 (耗时: {test_time:.1f}秒)")
        if qps_lines:
            print(f"📊 性能结果: {qps_lines[-1]}")
        
        return {
            "config": f"{workers}×{omp_threads}",
            "workers": workers,
            "omp_threads": omp_threads,
            "total_threads": workers * omp_threads,
            "test_time": test_time,
            "success": True,
            "output": output
        }
    else:
        print(f"❌ 测试失败: {result.stderr}")
        return {
            "config": f"{workers}×{omp_threads}",
            "success": False,
            "error": result.stderr
        }

def main():
    print("🧪 OpenMP配置性能对比测试")
    print("=" * 50)
    
    # 测试配置列表
    configs = [
        (8, 2, "当前配置 (8×2)"),
        (4, 4, "平衡配置 (4×4)"),
        (16, 1, "多进程配置 (16×1)")
    ]
    
    results = []
    
    for workers, omp_threads, name in configs:
        try:
            result = test_config(workers, omp_threads, name)
            if result:
                results.append(result)
        except Exception as e:
            print(f"❌ 配置 {name} 测试异常: {e}")
    
    # 输出对比结果
    print("\n" + "=" * 60)
    print("📊 性能对比总结")
    print("=" * 60)
    
    if results:
        print(f"{'配置':<12} {'Workers':<8} {'OpenMP':<8} {'总线程':<8} {'测试时间':<10} {'状态'}")
        print("-" * 60)
        
        for result in results:
            if result["success"]:
                status = "✅ 成功"
            else:
                status = "❌ 失败"
            
            print(f"{result['config']:<12} {result['workers']:<8} {result['omp_threads']:<8} "
                  f"{result['total_threads']:<8} {result.get('test_time', 0):<10.1f} {status}")
    
    print("\n💡 建议:")
    print("   - 8×2配置: 适合高并发HTTP请求")
    print("   - 4×4配置: 适合大批量搜索")
    print("   - 16×1配置: 适合极高QPS小批量")
    
    # 恢复原始配置
    print("\n🔄 恢复原始配置...")
    subprocess.run(["pkill", "-f", "smart_faiss_server"], capture_output=True)
    time.sleep(2)
    
    restore_cmd = [
        "numactl", "-C", "0-15",
        "python3.11", "smart_faiss_server.py",
        "--use-gunicorn", "--workers", "8", "--preload"
    ]
    
    subprocess.Popen(
        restore_cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        cwd="/home/<USER>/VectorDBBench"
    )
    
    print("✅ 原始配置已恢复")

if __name__ == "__main__":
    main()
