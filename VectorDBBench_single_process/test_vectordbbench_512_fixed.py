#!/usr/bin/env python3
"""
修复pickle问题后测试VectorDBBench 512并发
"""

import os
import sys
import time
import multiprocessing as mp
import logging
from concurrent.futures import ProcessPoolExecutor
import requests
import numpy as np

# 设置环境变量
os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s: %(message)s')
log = logging.getLogger(__name__)

# 全局函数，可以被pickle序列化
def simple_worker(worker_id):
    """简单的工作函数 - 全局函数可以被pickle"""
    import time
    import os
    time.sleep(0.1)
    return f"worker_{worker_id}_pid_{os.getpid()}_done"

def vectordb_search_worker(test_data, q, cond, worker_id, endpoint):
    """模拟VectorDBBench的搜索工作进程 - 全局函数"""
    try:
        # 1. 同步阶段
        q.put(1)
        
        # 2. 等待同步信号
        with cond:
            cond.wait(timeout=60)
        
        # 3. 执行搜索
        import requests
        session = requests.Session()
        
        count = 0
        for i in range(3):  # 执行3次搜索测试
            try:
                response = session.post(
                    endpoint,
                    json={"query": test_data, "topk": 10},
                    timeout=10
                )
                if response.status_code == 200:
                    count += 1
            except Exception as e:
                log.warning(f"Worker {worker_id} 搜索失败: {e}")
        
        return f"worker_{worker_id}_success_{count}"
        
    except Exception as e:
        return f"worker_{worker_id}_error_{e}"

class Fixed512ConcurrencyTest:
    def __init__(self):
        self.endpoint = "http://10.1.180.72:8005/search"
        
    def test_basic_process_creation_fixed(self):
        """修复pickle问题后的基础进程创建测试"""
        print("🔍 修复pickle问题后的进程创建测试")
        print("=" * 50)
        
        # 测试不同进程数
        test_levels = [1, 4, 8, 16, 32, 64, 128, 256, 384, 512]
        
        for level in test_levels:
            print(f"\n📊 测试 {level} 个进程...")
            
            try:
                start_time = time.time()
                
                with ProcessPoolExecutor(
                    max_workers=level,
                    mp_context=mp.get_context("spawn")
                ) as executor:
                    
                    # 提交任务
                    futures = [executor.submit(simple_worker, i) for i in range(level)]
                    
                    # 等待完成
                    completed = 0
                    failed = 0
                    
                    for future in futures:
                        try:
                            result = future.result(timeout=10)
                            if "done" in result:
                                completed += 1
                            else:
                                failed += 1
                        except Exception as e:
                            failed += 1
                            log.error(f"任务失败: {e}")
                    
                    total_time = time.time() - start_time
                    
                    print(f"   完成: {completed}/{level}")
                    print(f"   失败: {failed}")
                    print(f"   时间: {total_time:.2f}s")
                    
                    if completed == level:
                        print(f"   ✅ {level} 进程全部成功")
                    else:
                        print(f"   ❌ {level} 进程部分失败")
                        return level
                        
            except Exception as e:
                print(f"   ❌ {level} 进程创建异常: {e}")
                return level
        
        print(f"✅ 所有级别的进程创建都成功")
        return None
    
    def test_vectordbbench_512_simulation(self):
        """模拟VectorDBBench 512并发的完整流程"""
        print("\n🔍 VectorDBBench 512并发完整模拟")
        print("=" * 50)
        
        # 准备测试数据
        test_data = np.random.random(768).tolist()
        
        print(f"🚀 启动512进程VectorDBBench模拟...")
        
        try:
            start_time = time.time()
            
            with mp.Manager() as manager:
                q = manager.Queue()
                cond = manager.Condition()
                
                print(f"📊 创建512进程池...")
                pool_start = time.time()
                
                with ProcessPoolExecutor(
                    max_workers=512,
                    mp_context=mp.get_context("spawn")
                ) as executor:
                    
                    pool_time = time.time() - pool_start
                    print(f"✅ 进程池创建完成: {pool_time:.2f}s")
                    
                    print(f"📊 提交512个任务...")
                    submit_start = time.time()
                    
                    futures = [
                        executor.submit(vectordb_search_worker, test_data, q, cond, i, self.endpoint)
                        for i in range(512)
                    ]
                    
                    submit_time = time.time() - submit_start
                    print(f"✅ 任务提交完成: {submit_time:.2f}s")
                    
                    print(f"📊 等待进程同步...")
                    sync_start = time.time()
                    
                    # 等待队列填充
                    while q.qsize() < 512:
                        current_size = q.qsize()
                        elapsed = time.time() - sync_start
                        
                        if int(elapsed) % 10 == 0 and elapsed > 0:
                            progress = (current_size / 512) * 100
                            print(f"   同步进度: {current_size}/512 ({progress:.1f}%), 已等待: {elapsed:.1f}s")
                        
                        if elapsed > 120:  # 2分钟超时
                            print(f"   ❌ 进程同步超时，当前进度: {current_size}/512")
                            return False
                        
                        time.sleep(1)
                    
                    sync_time = time.time() - sync_start
                    print(f"✅ 进程同步完成: {sync_time:.2f}s")
                    
                    print(f"📊 发送同步信号，开始测试...")
                    with cond:
                        cond.notify_all()
                    
                    print(f"📊 等待任务完成...")
                    test_start = time.time()
                    
                    # 等待任务完成
                    completed = 0
                    failed = 0
                    success_count = 0
                    
                    for i, future in enumerate(futures):
                        try:
                            result = future.result(timeout=1)
                            if "success" in str(result):
                                completed += 1
                                # 提取成功次数
                                if "_success_" in str(result):
                                    count = int(str(result).split("_success_")[1])
                                    success_count += count
                            else:
                                failed += 1
                        except Exception as e:
                            failed += 1
                        
                        # 只检查前50个任务的结果
                        if i >= 49:
                            break
                    
                    test_time = time.time() - test_start
                    total_time = time.time() - start_time
                    
                    print(f"✅ VectorDBBench 512并发模拟完成!")
                    print(f"   总时间: {total_time:.2f}s")
                    print(f"   同步时间: {sync_time:.2f}s")
                    print(f"   测试时间: {test_time:.2f}s")
                    print(f"   成功任务: {completed}/50 (样本)")
                    print(f"   失败任务: {failed}")
                    print(f"   搜索成功次数: {success_count}")
                    
                    if completed >= 40:  # 80%成功率
                        print(f"🎉 512并发测试成功!")
                        return True
                    else:
                        print(f"❌ 512并发测试失败")
                        return False
                    
        except Exception as e:
            print(f"❌ 512并发模拟失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_complete_test(self):
        """运行完整测试"""
        print("🎯 修复pickle问题后的VectorDBBench 512并发测试")
        print("=" * 60)
        
        # 1. 基础进程创建测试
        failure_point = self.test_basic_process_creation_fixed()
        
        if failure_point:
            print(f"\n❌ 进程创建在 {failure_point} 个进程时失败")
            print(f"💡 建议将并发数限制在 {failure_point - 50} 以下")
            return False
        
        # 2. VectorDBBench 512并发模拟
        success = self.test_vectordbbench_512_simulation()
        
        print(f"\n" + "=" * 60)
        print(f"📋 测试结果总结")
        print(f"=" * 60)
        
        if success:
            print(f"🎉 512并发测试成功!")
            print(f"💡 VectorDBBench的512并发问题已解决")
            print(f"🚀 现在可以正常运行VectorDBBench 512并发测试")
        else:
            print(f"❌ 512并发测试仍然失败")
            print(f"💡 需要进一步调查其他问题")
        
        return success

def main():
    # 确保环境变量设置
    os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'
    
    tester = Fixed512ConcurrencyTest()
    tester.run_complete_test()

if __name__ == "__main__":
    main()
