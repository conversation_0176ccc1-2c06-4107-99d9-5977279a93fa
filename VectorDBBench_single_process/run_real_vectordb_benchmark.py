#!/usr/bin/env python3
"""
使用真实数据集运行 VectorDBBench FAISS 基准测试
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime

# 设置数据集路径
os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'

def run_real_vectordb_benchmark():
    """使用真实数据集运行 VectorDBBench 测试"""
    print("🔬 VectorDBBench FAISS 真实数据集测试")
    print("=" * 60)
    print(f"📁 数据集路径: {os.environ['DATASET_LOCAL_DIR']}")
    
    # 验证数据集存在
    dataset_path = Path(os.environ['DATASET_LOCAL_DIR'])
    if not dataset_path.exists():
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return False
    
    # 检查可用的数据集
    print("\n📊 可用数据集:")
    openai_datasets = list((dataset_path / 'openai').glob('*'))
    cohere_datasets = list((dataset_path / 'cohere').glob('*'))
    
    for dataset in openai_datasets:
        if dataset.is_dir():
            print(f"   🟢 OpenAI: {dataset.name}")
            
    for dataset in cohere_datasets:
        if dataset.is_dir():
            print(f"   🔵 Cohere: {dataset.name}")
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
        from vectordb_bench.backend.cases import CaseType
        from vectordb_bench.backend.clients.api import MetricType
        
        print("\n✅ VectorDBBench 模块加载成功")
        
        # 测试配置列表
        test_configs = [
            {
                "name": "OpenAI 小规模测试 (50K)",
                "case_type": "Performance1536D50K",
                "ef_search": 64,
                "description": "50K 向量，1536维，COSINE距离"
            },
            {
                "name": "OpenAI 中规模测试 (500K)", 
                "case_type": "Performance1536D500K",
                "ef_search": 128,
                "description": "500K 向量，1536维，COSINE距离"
            }
        ]
        
        results = []
        
        for config in test_configs:
            print(f"\n🎯 执行测试: {config['name']}")
            print(f"   📋 描述: {config['description']}")
            print(f"   🔍 ef_search: {config['ef_search']}")
            
            # 创建配置
            db_config = FaissLocalConfig(
                db_label=f"faiss_real_data_{config['case_type'].lower()}",
                index_type="HNSW"
            )
            
            db_case_config = HNSWConfig(
                m=16,
                ef_construction=200,
                ef_search=config['ef_search'],
                metric_type=MetricType.COSINE,
            )
            
            print(f"\n   🔧 FAISS 配置:")
            print(f"      索引类型: {db_config.index_type}")
            print(f"      HNSW-m: {db_case_config.m}")
            print(f"      ef_construction: {db_case_config.ef_construction}")
            print(f"      ef_search: {db_case_config.ef_search}")
            print(f"      距离度量: {db_case_config.metric_type}")
            
            # 测试参数
            test_params = {
                'case_type': config['case_type'],
                'k': 100,
                'num_concurrency': [1],
                'concurrency_duration': 30,
                'concurrency_timeout': 3600,
                'db_label': db_config.db_label,
                'load': True,
                'search_serial': True,
                'search_concurrent': True,
                'drop_old': True,
                'dry_run': False,
                'task_label': f'FAISS_Real_Data_{config["case_type"]}',
                'custom_case': {}
            }
            
            print(f"\n   🚀 开始基准测试...")
            print(f"      案例类型: {test_params['case_type']}")
            print(f"      k值: {test_params['k']}")
            print(f"      测试时长: {test_params['concurrency_duration']}s")
            print("   " + "="*50)
            
            start_time = time.time()
            
            try:
                # 运行测试
                run(
                    db=DB.FaissLocal,
                    db_config=db_config,
                    db_case_config=db_case_config,
                    **test_params
                )
                
                test_time = time.time() - start_time
                print(f"\n   ✅ 测试完成，耗时: {test_time:.2f}s")
                
                # 记录结果
                results.append({
                    "config": config,
                    "test_time": test_time,
                    "status": "success"
                })
                
            except Exception as e:
                test_time = time.time() - start_time
                print(f"\n   ❌ 测试失败: {e}")
                
                results.append({
                    "config": config,
                    "test_time": test_time,
                    "status": "failed",
                    "error": str(e)
                })
                
                # 打印更详细的错误信息
                import traceback
                print("   详细错误信息:")
                traceback.print_exc()
                
                # 继续下一个测试
                continue
        
        # 生成测试摘要
        print(f"\n📊 测试摘要:")
        print("="*50)
        
        successful_tests = [r for r in results if r["status"] == "success"]
        failed_tests = [r for r in results if r["status"] == "failed"]
        
        print(f"✅ 成功测试: {len(successful_tests)}")
        print(f"❌ 失败测试: {len(failed_tests)}")
        
        if successful_tests:
            total_time = sum(r["test_time"] for r in successful_tests)
            print(f"⏱️  总测试时间: {total_time:.2f}s")
            
            for result in successful_tests:
                config = result["config"]
                print(f"   🎯 {config['name']}: {result['test_time']:.2f}s")
        
        if failed_tests:
            print(f"\n❌ 失败的测试:")
            for result in failed_tests:
                config = result["config"]
                print(f"   💥 {config['name']}: {result.get('error', 'Unknown error')}")
        
        return len(successful_tests) > 0
        
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dataset_files():
    """检查数据集文件的详细信息"""
    print("\n🔍 检查数据集文件详情...")
    
    dataset_path = Path(os.environ['DATASET_LOCAL_DIR'])
    
    # 检查 OpenAI 数据集
    openai_small = dataset_path / 'openai' / 'openai_small_50k'
    if openai_small.exists():
        print(f"\n📁 {openai_small.name}:")
        for file in openai_small.glob('*.parquet'):
            size_mb = file.stat().st_size / 1024 / 1024
            print(f"   📄 {file.name}: {size_mb:.1f}MB")
    
    # 检查 OpenAI 中规模数据集
    openai_medium = dataset_path / 'openai' / 'openai_medium_500k'
    if openai_medium.exists():
        print(f"\n📁 {openai_medium.name}:")
        for file in openai_medium.glob('*.parquet'):
            size_mb = file.stat().st_size / 1024 / 1024
            print(f"   📄 {file.name}: {size_mb:.1f}MB")

def check_results():
    """检查并显示最新的测试结果"""
    print("\n📊 检查测试结果...")
    
    try:
        results_dir = Path('/home/<USER>/VectorDBBench/results')
        if not results_dir.exists():
            print("⚠️  结果目录不存在")
            return
        
        # 查找最新的结果文件
        result_files = list(results_dir.glob('**/*.json'))
        if not result_files:
            print("⚠️  未找到结果文件")
            return
        
        # 按修改时间排序，获取最新的
        latest_files = sorted(result_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
        
        print(f"📁 最新的 {len(latest_files)} 个结果文件:")
        for i, file in enumerate(latest_files):
            mtime = datetime.fromtimestamp(file.stat().st_mtime)
            print(f"   {i+1}. {file.name} ({mtime.strftime('%H:%M:%S')})")
        
        # 读取最新结果
        if latest_files:
            latest_file = latest_files[0]
            print(f"\n📊 最新结果详情 ({latest_file.name}):")
            
            with open(latest_file, 'r') as f:
                data = json.load(f)
            
            if 'results' in data:
                for result in data['results']:
                    case_name = result.get('case', {}).get('name', 'Unknown')
                    print(f"\n   📋 {case_name}:")
                    
                    if 'metrics' in result:
                        metrics = result['metrics']
                        print(f"      🚀 QPS: {metrics.get('qps', 'N/A'):.2f}")
                        print(f"      🎯 召回率: {metrics.get('recall', 'N/A'):.4f}")
                        print(f"      ⏱️  延迟 P50: {metrics.get('latency_p50', 'N/A'):.3f}ms")
                        print(f"      📊 延迟 P99: {metrics.get('latency_p99', 'N/A'):.3f}ms")
                        print(f"      🏗️  加载时间: {metrics.get('load_duration', 'N/A'):.2f}s")
            
    except Exception as e:
        print(f"❌ 检查结果失败: {e}")

if __name__ == "__main__":
    print("🎯 VectorDBBench 真实数据集基准测试")
    print("=" * 55)
    
    # 检查数据集
    check_dataset_files()
    
    # 运行基准测试
    success = run_real_vectordb_benchmark()
    
    # 等待结果写入
    if success:
        time.sleep(5)
        check_results()
    
    print(f"\n{'🎉 测试完成!' if success else '💥 测试失败!'}")
    sys.exit(0 if success else 1)
