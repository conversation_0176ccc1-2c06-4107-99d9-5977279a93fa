#!/usr/bin/env python3
'''
🔍 FAISS 远程连接测试脚本
用于验证客户端与服务器的连接
'''

import requests
import json
import sys
import numpy as np

def test_server_connection(host='localhost', port=8002):
    '''测试服务器连接'''
    print(f"🔍 测试连接到 {host}:{port}")
    
    base_url = f"http://{host}:{port}"
    
    try:
        # 健康检查
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接成功")
            return True
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_faiss_operations(host='localhost', port=8002):
    '''测试基本FAISS操作'''
    print(f"🧪 测试FAISS基本操作")
    
    base_url = f"http://{host}:{port}"
    
    try:
        # 创建索引
        index_data = {
            "dimension": 128,
            "index_type": "Flat",
            "metric_type": "COSINE"
        }
        
        response = requests.post(
            f"{base_url}/create_index", 
            json=index_data,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 索引创建成功")
        else:
            print(f"❌ 索引创建失败: {response.status_code}")
            return False
        
        # 插入向量
        vectors = np.random.rand(100, 128).astype(np.float32)
        ids = list(range(100))
        
        insert_data = {
            "vectors": vectors.tolist(),
            "ids": ids
        }
        
        response = requests.post(
            f"{base_url}/insert_bulk",
            json=insert_data,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 向量插入成功")
        else:
            print(f"❌ 向量插入失败: {response.status_code}")
            return False
        
        # 搜索测试
        query = np.random.rand(1, 128).astype(np.float32)
        
        search_data = {
            "query_vectors": query.tolist(),
            "k": 10
        }
        
        response = requests.post(
            f"{base_url}/search",
            json=search_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 搜索成功，返回{len(result['results'][0])}个结果")
            return True
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        host = sys.argv[1]
    else:
        host = input("请输入FAISS服务器地址 (默认localhost): ") or "localhost"
    
    if len(sys.argv) > 2:
        port = int(sys.argv[2])
    else:
        port = int(input("请输入FAISS服务器端口 (默认8002): ") or "8002")
    
    print("🎯 FAISS远程连接测试")
    print("=" * 40)
    
    # 测试连接
    if not test_server_connection(host, port):
        print("💥 连接测试失败，请检查服务器状态")
        sys.exit(1)
    
    # 测试操作
    if test_faiss_operations(host, port):
        print("🎉 所有测试通过！可以进行基准测试")
        sys.exit(0)
    else:
        print("💥 FAISS操作测试失败")
        sys.exit(1)
