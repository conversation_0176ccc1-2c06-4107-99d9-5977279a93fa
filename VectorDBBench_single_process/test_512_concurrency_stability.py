#!/usr/bin/env python3
"""
测试512并发的稳定性
"""

import os
import sys
import time
import subprocess
import signal
import psutil
import logging

# 设置环境变量
os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s: %(message)s')
log = logging.getLogger(__name__)

class ConcurrencyStabilityTester:
    def __init__(self):
        self.test_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.hang_count = 0
        
    def cleanup_processes(self):
        """清理可能残留的进程"""
        try:
            # 查找VectorDBBench相关进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'vectordbbench' in cmdline.lower() or 'benchmark' in cmdline.lower():
                        log.warning(f"发现残留进程: {proc.info['pid']} - {cmdline[:100]}")
                        proc.terminate()
                        proc.wait(timeout=5)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    pass
        except Exception as e:
            log.warning(f"清理进程时出错: {e}")
    
    def run_single_test(self, test_id: int, timeout: int = 120):
        """运行单次512并发测试"""
        print(f"\n🧪 测试 #{test_id}")
        print("=" * 50)
        
        # 清理环境
        self.cleanup_processes()
        time.sleep(2)
        
        # 构建命令
        cmd = [
            'python3.11', '-m', 'vectordb_bench.cli.vectordbbench',
            'faissremote',
            '--uri', 'http://***********:8005',
            '--case-type', 'Performance768D10M',
            '--index-type', 'HNSW',
            '--m', '30',
            '--ef-construction', '360',
            '--concurrency-duration', '10',  # 缩短到10秒
            '--num-concurrency', '512',
            '--skip-load',
            '--skip-search-serial'
        ]
        
        start_time = time.time()
        
        try:
            # 启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd='/home/<USER>/VectorDBBench',
                env=os.environ.copy()
            )
            
            # 等待完成或超时
            try:
                output, _ = process.communicate(timeout=timeout)
                elapsed = time.time() - start_time
                
                if process.returncode == 0:
                    # 检查输出是否包含成功标志
                    if 'qps=' in output.lower() and 'success to finish task' in output.lower():
                        print(f"✅ 测试 #{test_id} 成功 ({elapsed:.1f}s)")
                        
                        # 提取QPS
                        lines = output.split('\n')
                        qps = None
                        for line in lines:
                            if 'End search in concurrency 512' in line and 'qps=' in line:
                                try:
                                    qps_part = line.split('qps=')[1].split()[0]
                                    qps = float(qps_part)
                                    break
                                except:
                                    pass
                        
                        print(f"   QPS: {qps if qps else 'N/A'}")
                        self.success_count += 1
                        return True
                    else:
                        print(f"❌ 测试 #{test_id} 失败 - 输出异常")
                        print(f"   输出片段: {output[-200:]}")
                        self.failure_count += 1
                        return False
                else:
                    print(f"❌ 测试 #{test_id} 失败 - 退出码: {process.returncode}")
                    print(f"   输出片段: {output[-200:]}")
                    self.failure_count += 1
                    return False
                    
            except subprocess.TimeoutExpired:
                print(f"⏰ 测试 #{test_id} 超时 ({timeout}s)")
                print(f"   可能卡住了，强制终止进程")
                
                # 强制终止进程树
                try:
                    parent = psutil.Process(process.pid)
                    children = parent.children(recursive=True)
                    for child in children:
                        child.terminate()
                    parent.terminate()
                    
                    # 等待进程结束
                    time.sleep(2)
                    
                    # 如果还没结束，强制杀死
                    for child in children:
                        try:
                            child.kill()
                        except:
                            pass
                    try:
                        parent.kill()
                    except:
                        pass
                        
                except Exception as e:
                    log.warning(f"终止进程时出错: {e}")
                
                self.hang_count += 1
                return False
                
        except Exception as e:
            print(f"❌ 测试 #{test_id} 异常: {e}")
            self.failure_count += 1
            return False
    
    def run_stability_test(self, num_tests: int = 5):
        """运行稳定性测试"""
        print("🔬 VectorDBBench 512并发稳定性测试")
        print("=" * 60)
        
        for i in range(1, num_tests + 1):
            self.test_count += 1
            success = self.run_single_test(i, timeout=120)
            
            # 测试间隔
            if i < num_tests:
                print(f"⏳ 等待5秒后进行下一次测试...")
                time.sleep(5)
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print(f"\n" + "=" * 60)
        print(f"📋 512并发稳定性测试报告")
        print(f"=" * 60)
        
        success_rate = (self.success_count / self.test_count) * 100 if self.test_count > 0 else 0
        
        print(f"📊 测试统计:")
        print(f"   总测试数: {self.test_count}")
        print(f"   成功次数: {self.success_count}")
        print(f"   失败次数: {self.failure_count}")
        print(f"   卡住次数: {self.hang_count}")
        print(f"   成功率: {success_rate:.1f}%")
        
        print(f"\n🎯 结论:")
        if success_rate >= 80:
            print(f"✅ 512并发稳定性良好 (成功率 {success_rate:.1f}%)")
        elif success_rate >= 50:
            print(f"⚠️  512并发稳定性一般 (成功率 {success_rate:.1f}%)")
        else:
            print(f"❌ 512并发稳定性差 (成功率 {success_rate:.1f}%)")
        
        if self.hang_count > 0:
            print(f"⚠️  发现 {self.hang_count} 次卡住现象，需要进一步调查")
        
        print(f"\n💡 建议:")
        if success_rate < 100:
            print(f"1. 检查系统资源使用情况")
            print(f"2. 监控FAISS服务器状态")
            print(f"3. 考虑降低并发数到稳定范围")
        else:
            print(f"1. 512并发工作正常，可以放心使用")

def main():
    tester = ConcurrencyStabilityTester()
    tester.run_stability_test(num_tests=3)  # 运行3次测试

if __name__ == "__main__":
    main()
