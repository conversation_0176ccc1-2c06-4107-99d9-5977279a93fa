#!/usr/bin/env python3
"""
修复512并发问题的脚本
优化 mp_runner.py 中的进程同步逻辑
"""

import os
import shutil
import time

def backup_original_file():
    """备份原始文件"""
    original_file = "vectordb_bench/backend/runner/mp_runner.py"
    backup_file = f"{original_file}.backup_{int(time.time())}"
    
    if os.path.exists(original_file):
        shutil.copy2(original_file, backup_file)
        print(f"✅ 已备份原始文件: {backup_file}")
        return backup_file
    else:
        print(f"❌ 原始文件不存在: {original_file}")
        return None

def create_optimized_wait_function():
    """创建优化的等待函数"""
    return '''    def _wait_for_queue_fill(self, q: Queue, size: int):
        """优化的队列填充等待函数 - 修复512并发问题"""
        wait_t = 0
        check_interval = 0.1  # 初始检查间隔：100ms
        max_interval = 2.0    # 最大检查间隔：2秒
        
        log.info(f"等待 {size} 个进程启动并加入队列...")
        
        while q.qsize() < size:
            current_size = q.qsize()
            
            # 动态调整检查间隔
            if current_size == 0:
                sleep_t = check_interval  # 开始时快速检查
            elif current_size < size // 2:
                sleep_t = min(0.5, check_interval * 2)  # 进程启动中，中等频率
            else:
                sleep_t = min(max_interval, check_interval * 5)  # 大部分进程已启动，降低频率
            
            wait_t += sleep_t
            
            # 超时检查
            if wait_t > self.concurrency_timeout > 0:
                log.error(f"进程启动超时: 已等待 {wait_t:.1f}s, 当前队列大小: {current_size}/{size}")
                raise ConcurrencySlotTimeoutError
            
            # 进度日志
            if int(wait_t) % 10 == 0 and wait_t > 10:  # 每10秒打印一次进度
                progress = (current_size / size) * 100
                log.info(f"进程启动进度: {current_size}/{size} ({progress:.1f}%), 已等待: {wait_t:.1f}s")
            
            time.sleep(sleep_t)
        
        log.info(f"✅ 所有 {size} 个进程已启动完成，总耗时: {wait_t:.2f}s")'''

def apply_fix():
    """应用修复"""
    original_file = "vectordb_bench/backend/runner/mp_runner.py"
    
    if not os.path.exists(original_file):
        print(f"❌ 文件不存在: {original_file}")
        return False
    
    # 备份原始文件
    backup_file = backup_original_file()
    if not backup_file:
        return False
    
    try:
        # 读取原始文件
        with open(original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到需要替换的函数
        start_marker = "    def _wait_for_queue_fill(self, q: Queue, size: int):"
        end_marker = "            time.sleep(sleep_t)"
        
        start_idx = content.find(start_marker)
        if start_idx == -1:
            print("❌ 找不到 _wait_for_queue_fill 函数")
            return False
        
        # 找到函数结束位置（下一个函数定义或类结束）
        lines = content[start_idx:].split('\n')
        end_idx = start_idx
        function_lines = []
        
        for i, line in enumerate(lines):
            if i == 0:  # 函数定义行
                function_lines.append(line)
                continue
            
            # 如果遇到同级别的函数定义或类结束，停止
            if line.strip() and not line.startswith('    '):
                break
            if line.strip().startswith('def ') and not line.startswith('        '):
                break
            
            function_lines.append(line)
            end_idx += len(line) + 1  # +1 for newline
        
        end_idx = start_idx + end_idx
        
        # 替换函数
        optimized_function = create_optimized_wait_function()
        new_content = content[:start_idx] + optimized_function + content[end_idx:]
        
        # 写入修改后的文件
        with open(original_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 成功应用512并发修复")
        print("🔧 修复内容:")
        print("   - 优化进程启动等待逻辑")
        print("   - 动态调整检查间隔 (0.1s -> 2s)")
        print("   - 添加进度日志")
        print("   - 改善超时处理")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用修复失败: {e}")
        # 恢复备份
        if backup_file and os.path.exists(backup_file):
            shutil.copy2(backup_file, original_file)
            print(f"✅ 已恢复备份文件")
        return False

def verify_fix():
    """验证修复是否成功"""
    original_file = "vectordb_bench/backend/runner/mp_runner.py"
    
    try:
        with open(original_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修复内容
        checks = [
            "check_interval = 0.1",
            "max_interval = 2.0",
            "动态调整检查间隔",
            "进程启动进度"
        ]
        
        all_found = True
        for check in checks:
            if check not in content:
                print(f"❌ 验证失败: 找不到 '{check}'")
                all_found = False
        
        if all_found:
            print("✅ 修复验证成功")
            return True
        else:
            print("❌ 修复验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def create_test_script():
    """创建测试脚本"""
    test_script = """#!/usr/bin/env python3
'''
测试512并发修复效果
'''

import sys
import os
sys.path.append('/home/<USER>/VectorDBBench')

from vectordb_bench.backend.runner.mp_runner import MultiProcessingSearchRunner
import multiprocessing as mp
import time

class MockDB:
    def init(self):
        return self
    
    def __enter__(self):
        return self
    
    def __exit__(self, *args):
        pass
    
    def prepare_filter(self, filters):
        pass
    
    def search_embedding(self, data, k):
        time.sleep(0.001)  # 模拟搜索
        return []

def test_512_concurrency():
    print("🧪 测试512并发修复效果")
    print("=" * 50)
    
    # 创建模拟数据
    test_data = [[0.1] * 768 for _ in range(100)]
    mock_db = MockDB()
    
    # 创建runner，只测试512并发
    runner = MultiProcessingSearchRunner(
        db=mock_db,
        test_data=test_data,
        concurrencies=[512],  # 只测试512
        duration=5,  # 短时间测试
        concurrency_timeout=300  # 5分钟超时
    )
    
    try:
        print("🚀 开始512并发测试...")
        start_time = time.time()
        
        result = runner.run()
        
        end_time = time.time()
        print(f"✅ 512并发测试成功!")
        print(f"   耗时: {end_time - start_time:.2f} 秒")
        print(f"   结果: {result}")
        
    except Exception as e:
        print(f"❌ 512并发测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_512_concurrency()
"""
    
    with open("test_512_fix.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    os.chmod("test_512_fix.py", 0o755)
    print("✅ 已创建测试脚本: test_512_fix.py")

def main():
    """主函数"""
    print("🔧 修复512并发问题")
    print("=" * 50)
    
    # 1. 应用修复
    if not apply_fix():
        print("❌ 修复失败")
        return False
    
    # 2. 验证修复
    if not verify_fix():
        print("❌ 验证失败")
        return False
    
    # 3. 创建测试脚本
    create_test_script()
    
    print("\n" + "=" * 50)
    print("✅ 512并发问题修复完成!")
    print("\n🧪 测试修复效果:")
    print("   python3 test_512_fix.py")
    print("\n🚀 重新运行VectorDBBench:")
    print("   现在512并发应该能正常启动了")
    
    return True

if __name__ == "__main__":
    main()
