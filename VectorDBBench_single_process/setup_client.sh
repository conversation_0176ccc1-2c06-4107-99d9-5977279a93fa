#!/bin/bash
# 客户端环境设置脚本 (setup_client.sh)
# 用于在客户机上设置VectorDBBench远程测试环境

set -e  # 遇到错误时退出

echo "🔧 设置VectorDBBench客户端环境"
echo "================================"

# 检查Python版本
echo "📋 检查Python版本..."
python3 --version || {
    echo "❌ 错误: 需要Python 3.8+，请先安装Python3"
    exit 1
}

# 创建虚拟环境
echo "📦 创建Python虚拟环境..."
if [ -d "vdbench-client-env" ]; then
    echo "⚠️  虚拟环境已存在，跳过创建"
else
    python3 -m venv vdbench-client-env
    echo "✅ 虚拟环境创建完成"
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source vdbench-client-env/bin/activate

# 安装基础依赖
echo "📥 安装基础依赖..."
pip install --upgrade pip

# 安装VectorDBBench所需依赖
echo "📥 安装VectorDBBench依赖..."
pip install requests          # HTTP客户端
pip install pandas            # 数据处理
pip install numpy             # 数值计算
pip install pydantic          # 数据验证
pip install click             # CLI框架
pip install pyyaml           # YAML配置
pip install tqdm             # 进度条
pip install psutil           # 系统信息

# 检查是否有pyproject.toml，使用editable install
if [ -f "pyproject.toml" ]; then
    echo "📥 安装VectorDBBench包 (editable)..."
    pip install -e .
else
    echo "⚠️  未找到pyproject.toml，跳过包安装"
fi

echo ""
echo "✅ 客户端环境设置完成！"
echo ""
echo "🚀 使用方法:"
echo "   1. 激活环境: source vdbench-client-env/bin/activate"
echo "   2. 配置远程服务器地址"
echo "   3. 运行测试: python run_real_vectordb_benchmark.py"
echo ""
echo "📋 环境信息:"
echo "   Python版本: $(python --version)"
echo "   虚拟环境: vdbench-client-env"
echo "   工作目录: $(pwd)"
