#!/usr/bin/env python3
"""
诊断512并发问题的专用脚本
"""

import os
import sys
import time
import multiprocessing as mp
import psutil
import subprocess
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import threading
import signal

class ConcurrencyDiagnostic:
    def __init__(self):
        self.max_processes = 512
        self.test_duration = 10  # 短时间测试
        self.results = []
        
    def check_system_limits(self):
        """检查系统限制"""
        print("🔍 系统限制检查")
        print("=" * 50)
        
        # 进程限制
        try:
            result = subprocess.run(['ulimit', '-u'], capture_output=True, text=True, shell=True)
            max_processes = result.stdout.strip()
            print(f"📊 最大进程数: {max_processes}")
        except:
            print("❌ 无法获取进程限制")
        
        # 文件描述符限制
        try:
            result = subprocess.run(['ulimit', '-n'], capture_output=True, text=True, shell=True)
            max_files = result.stdout.strip()
            print(f"📊 最大文件描述符: {max_files}")
        except:
            print("❌ 无法获取文件描述符限制")
        
        # 内存信息
        memory = psutil.virtual_memory()
        print(f"📊 总内存: {memory.total // (1024**3)} GB")
        print(f"📊 可用内存: {memory.available // (1024**3)} GB")
        print(f"📊 内存使用率: {memory.percent}%")
        
        # CPU信息
        cpu_count = psutil.cpu_count()
        load_avg = os.getloadavg()
        print(f"📊 CPU核心数: {cpu_count}")
        print(f"📊 系统负载: {load_avg}")
        
        # 当前进程数
        current_processes = len(psutil.pids())
        print(f"📊 当前进程数: {current_processes}")
        
        return {
            'max_processes': max_processes,
            'max_files': max_files,
            'memory_available': memory.available,
            'cpu_count': cpu_count,
            'current_processes': current_processes,
            'load_avg': load_avg[0]
        }
    
    def test_process_creation(self, target_processes):
        """测试进程创建能力"""
        print(f"\n🧪 测试创建 {target_processes} 个进程")
        print("=" * 50)
        
        created_processes = []
        start_time = time.time()
        
        try:
            for i in range(target_processes):
                try:
                    # 创建简单的子进程
                    proc = mp.Process(target=self.dummy_worker, args=(i,))
                    proc.start()
                    created_processes.append(proc)
                    
                    if (i + 1) % 50 == 0:
                        print(f"✅ 已创建 {i + 1} 个进程")
                        
                except Exception as e:
                    print(f"❌ 在第 {i + 1} 个进程时失败: {e}")
                    break
            
            creation_time = time.time() - start_time
            print(f"⏱️  进程创建耗时: {creation_time:.2f} 秒")
            print(f"📊 成功创建进程数: {len(created_processes)}")
            
            # 等待一段时间
            print("⏳ 等待进程运行...")
            time.sleep(2)
            
            # 检查进程状态
            alive_count = sum(1 for p in created_processes if p.is_alive())
            print(f"📊 存活进程数: {alive_count}")
            
        except Exception as e:
            print(f"❌ 进程创建测试失败: {e}")
        
        finally:
            # 清理进程
            print("🧹 清理进程...")
            cleanup_start = time.time()
            
            for proc in created_processes:
                try:
                    if proc.is_alive():
                        proc.terminate()
                        proc.join(timeout=1)
                        if proc.is_alive():
                            proc.kill()
                except:
                    pass
            
            cleanup_time = time.time() - cleanup_start
            print(f"⏱️  进程清理耗时: {cleanup_time:.2f} 秒")
        
        return len(created_processes)
    
    def dummy_worker(self, worker_id):
        """简单的工作进程"""
        try:
            # 模拟一些工作
            time.sleep(5)
        except:
            pass
    
    def test_thread_creation(self, target_threads):
        """测试线程创建能力"""
        print(f"\n🧪 测试创建 {target_threads} 个线程")
        print("=" * 50)
        
        created_threads = []
        start_time = time.time()
        
        try:
            for i in range(target_threads):
                try:
                    thread = threading.Thread(target=self.dummy_thread_worker, args=(i,))
                    thread.start()
                    created_threads.append(thread)
                    
                    if (i + 1) % 50 == 0:
                        print(f"✅ 已创建 {i + 1} 个线程")
                        
                except Exception as e:
                    print(f"❌ 在第 {i + 1} 个线程时失败: {e}")
                    break
            
            creation_time = time.time() - start_time
            print(f"⏱️  线程创建耗时: {creation_time:.2f} 秒")
            print(f"📊 成功创建线程数: {len(created_threads)}")
            
            # 等待一段时间
            print("⏳ 等待线程运行...")
            time.sleep(2)
            
            # 检查线程状态
            alive_count = sum(1 for t in created_threads if t.is_alive())
            print(f"📊 存活线程数: {alive_count}")
            
        except Exception as e:
            print(f"❌ 线程创建测试失败: {e}")
        
        finally:
            # 等待线程结束
            print("🧹 等待线程结束...")
            for thread in created_threads:
                try:
                    thread.join(timeout=1)
                except:
                    pass
        
        return len(created_threads)
    
    def dummy_thread_worker(self, thread_id):
        """简单的工作线程"""
        try:
            time.sleep(3)
        except:
            pass
    
    def test_process_pool(self, pool_size):
        """测试进程池"""
        print(f"\n🧪 测试进程池 (大小: {pool_size})")
        print("=" * 50)
        
        try:
            start_time = time.time()
            
            with ProcessPoolExecutor(max_workers=pool_size) as executor:
                # 提交一些任务
                futures = []
                for i in range(pool_size * 2):  # 提交比池大小多的任务
                    future = executor.submit(self.dummy_pool_worker, i)
                    futures.append(future)
                
                print(f"✅ 已提交 {len(futures)} 个任务")
                
                # 等待部分任务完成
                completed = 0
                for future in futures:
                    try:
                        result = future.result(timeout=1)
                        completed += 1
                    except:
                        break
                
                print(f"📊 完成任务数: {completed}")
            
            total_time = time.time() - start_time
            print(f"⏱️  进程池测试耗时: {total_time:.2f} 秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 进程池测试失败: {e}")
            return False
    
    def dummy_pool_worker(self, task_id):
        """进程池工作函数"""
        time.sleep(0.1)
        return task_id
    
    def run_full_diagnostic(self):
        """运行完整诊断"""
        print("🚀 512并发问题诊断")
        print("=" * 60)
        
        # 1. 检查系统限制
        limits = self.check_system_limits()
        
        # 2. 测试进程创建
        max_processes_created = self.test_process_creation(100)  # 先测试100个
        
        if max_processes_created >= 100:
            print("\n✅ 100进程测试通过，尝试更多...")
            max_processes_created = self.test_process_creation(256)
            
            if max_processes_created >= 256:
                print("\n✅ 256进程测试通过，尝试512...")
                max_processes_created = self.test_process_creation(512)
        
        # 3. 测试线程创建
        max_threads_created = self.test_thread_creation(512)
        
        # 4. 测试进程池
        pool_success = self.test_process_pool(min(64, max_processes_created))
        
        # 5. 生成报告
        self.generate_report(limits, max_processes_created, max_threads_created, pool_success)
    
    def generate_report(self, limits, max_processes, max_threads, pool_success):
        """生成诊断报告"""
        print("\n" + "=" * 60)
        print("📋 诊断报告")
        print("=" * 60)
        
        print(f"🔧 系统配置:")
        print(f"   CPU核心数: {limits.get('cpu_count', 'N/A')}")
        print(f"   可用内存: {limits.get('memory_available', 0) // (1024**3)} GB")
        print(f"   系统负载: {limits.get('load_avg', 'N/A'):.2f}")
        
        print(f"\n📊 并发能力测试:")
        print(f"   最大进程创建数: {max_processes}")
        print(f"   最大线程创建数: {max_threads}")
        print(f"   进程池测试: {'✅ 通过' if pool_success else '❌ 失败'}")
        
        print(f"\n💡 512并发分析:")
        if max_processes >= 512:
            print("   ✅ 系统支持512进程并发")
        else:
            print(f"   ❌ 系统最多支持 {max_processes} 进程并发")
            print("   🔧 建议:")
            print("      - 检查系统进程限制 (ulimit -u)")
            print("      - 检查内存是否充足")
            print("      - 考虑使用线程池而非进程池")
        
        if max_threads >= 512:
            print("   ✅ 系统支持512线程并发")
        else:
            print(f"   ❌ 系统最多支持 {max_threads} 线程并发")
        
        # 推荐配置
        recommended_concurrency = min(max_processes, limits.get('cpu_count', 1) * 4)
        print(f"\n🎯 推荐并发配置:")
        print(f"   建议最大并发数: {recommended_concurrency}")
        print(f"   安全并发数: {recommended_concurrency // 2}")

def main():
    diagnostic = ConcurrencyDiagnostic()
    diagnostic.run_full_diagnostic()

if __name__ == "__main__":
    main()
