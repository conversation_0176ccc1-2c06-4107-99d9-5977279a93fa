#!/usr/bin/env python3
"""
深度诊断512并发卡住问题
"""

import os
import sys
import time
import psutil
import subprocess
import multiprocessing as mp
import threading
import signal
from concurrent.futures import ProcessPoolExecutor
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s: %(message)s')
log = logging.getLogger(__name__)

class Deep512Diagnosis:
    def __init__(self):
        self.diagnosis_results = {}
        
    def check_system_limits(self):
        """检查系统限制"""
        print("🔍 系统限制详细检查")
        print("=" * 50)
        
        limits = {}
        
        # 1. 进程限制
        try:
            result = subprocess.run(['bash', '-c', 'ulimit -u'], capture_output=True, text=True)
            limits['max_processes'] = result.stdout.strip()
            print(f"📊 最大进程数: {limits['max_processes']}")
        except:
            print("❌ 无法获取进程限制")
        
        # 2. 文件描述符限制
        try:
            result = subprocess.run(['bash', '-c', 'ulimit -n'], capture_output=True, text=True)
            limits['max_files'] = result.stdout.strip()
            print(f"📊 最大文件描述符: {limits['max_files']}")
        except:
            print("❌ 无法获取文件描述符限制")
        
        # 3. 内存限制
        try:
            result = subprocess.run(['bash', '-c', 'ulimit -v'], capture_output=True, text=True)
            limits['max_memory'] = result.stdout.strip()
            print(f"📊 最大虚拟内存: {limits['max_memory']}")
        except:
            print("❌ 无法获取内存限制")
        
        # 4. 栈大小限制
        try:
            result = subprocess.run(['bash', '-c', 'ulimit -s'], capture_output=True, text=True)
            limits['stack_size'] = result.stdout.strip()
            print(f"📊 栈大小: {limits['stack_size']}")
        except:
            print("❌ 无法获取栈大小")
        
        # 5. 当前系统状态
        memory = psutil.virtual_memory()
        cpu_count = psutil.cpu_count()
        load_avg = os.getloadavg()
        current_processes = len(psutil.pids())
        
        print(f"📊 CPU核心数: {cpu_count}")
        print(f"📊 总内存: {memory.total // (1024**3)} GB")
        print(f"📊 可用内存: {memory.available // (1024**3)} GB")
        print(f"📊 内存使用率: {memory.percent}%")
        print(f"📊 系统负载: {load_avg}")
        print(f"📊 当前进程数: {current_processes}")
        
        return limits
    
    def test_process_creation_stages(self):
        """分阶段测试进程创建"""
        print("\n🧪 分阶段进程创建测试")
        print("=" * 50)
        
        stages = [50, 100, 200, 300, 400, 500, 512]
        results = {}
        
        for stage in stages:
            print(f"\n📊 测试 {stage} 个进程创建...")
            
            try:
                start_time = time.time()
                processes = []
                
                # 创建进程
                for i in range(stage):
                    p = mp.Process(target=self.dummy_worker, args=(i,))
                    p.start()
                    processes.append(p)
                    
                    # 每50个进程检查一次
                    if (i + 1) % 50 == 0:
                        alive_count = sum(1 for p in processes if p.is_alive())
                        print(f"   已创建 {i + 1} 个进程，存活 {alive_count} 个")
                
                creation_time = time.time() - start_time
                alive_count = sum(1 for p in processes if p.is_alive())
                
                print(f"✅ {stage} 进程创建成功")
                print(f"   创建时间: {creation_time:.2f} 秒")
                print(f"   存活进程: {alive_count}/{stage}")
                
                results[stage] = {
                    'success': True,
                    'creation_time': creation_time,
                    'alive_count': alive_count
                }
                
                # 清理进程
                for p in processes:
                    try:
                        if p.is_alive():
                            p.terminate()
                            p.join(timeout=1)
                            if p.is_alive():
                                p.kill()
                    except:
                        pass
                
                time.sleep(1)  # 清理间隔
                
            except Exception as e:
                print(f"❌ {stage} 进程创建失败: {e}")
                results[stage] = {
                    'success': False,
                    'error': str(e)
                }
                break
        
        return results
    
    def dummy_worker(self, worker_id):
        """简单工作进程"""
        try:
            time.sleep(3)
        except:
            pass
    
    def test_process_pool_executor(self):
        """测试ProcessPoolExecutor的行为"""
        print("\n🔧 ProcessPoolExecutor 测试")
        print("=" * 50)
        
        stages = [100, 200, 300, 400, 500, 512]
        results = {}
        
        for stage in stages:
            print(f"\n📊 测试 ProcessPoolExecutor({stage})...")
            
            try:
                start_time = time.time()
                
                with ProcessPoolExecutor(max_workers=stage) as executor:
                    # 提交任务
                    futures = []
                    for i in range(stage):
                        future = executor.submit(self.pool_worker, i)
                        futures.append(future)
                    
                    submission_time = time.time() - start_time
                    print(f"   任务提交时间: {submission_time:.2f} 秒")
                    
                    # 等待部分任务完成（不等待全部）
                    completed = 0
                    for i, future in enumerate(futures):
                        try:
                            result = future.result(timeout=0.1)
                            completed += 1
                        except:
                            break
                        
                        if i >= 10:  # 只等待前10个任务
                            break
                    
                    total_time = time.time() - start_time
                    print(f"✅ ProcessPoolExecutor({stage}) 测试完成")
                    print(f"   总时间: {total_time:.2f} 秒")
                    print(f"   完成任务: {completed}")
                    
                    results[stage] = {
                        'success': True,
                        'submission_time': submission_time,
                        'total_time': total_time,
                        'completed': completed
                    }
                
            except Exception as e:
                print(f"❌ ProcessPoolExecutor({stage}) 失败: {e}")
                results[stage] = {
                    'success': False,
                    'error': str(e)
                }
                break
        
        return results
    
    def pool_worker(self, worker_id):
        """进程池工作函数"""
        time.sleep(0.1)
        return worker_id
    
    def test_mp_manager_queue(self):
        """测试multiprocessing.Manager().Queue()的性能"""
        print("\n📡 multiprocessing.Manager().Queue() 测试")
        print("=" * 50)
        
        stages = [100, 200, 300, 400, 500, 512]
        results = {}
        
        for stage in stages:
            print(f"\n📊 测试 {stage} 个进程使用共享队列...")
            
            try:
                start_time = time.time()
                
                with mp.Manager() as manager:
                    q = manager.Queue()
                    
                    # 启动进程
                    processes = []
                    for i in range(stage):
                        p = mp.Process(target=self.queue_worker, args=(q, i))
                        p.start()
                        processes.append(p)
                    
                    # 等待队列填充
                    queue_fill_start = time.time()
                    while q.qsize() < stage:
                        time.sleep(0.1)
                        if time.time() - queue_fill_start > 30:  # 30秒超时
                            break
                    
                    queue_fill_time = time.time() - queue_fill_start
                    queue_size = q.qsize()
                    
                    print(f"   队列填充时间: {queue_fill_time:.2f} 秒")
                    print(f"   队列大小: {queue_size}/{stage}")
                    
                    # 清理进程
                    for p in processes:
                        try:
                            if p.is_alive():
                                p.terminate()
                                p.join(timeout=1)
                                if p.is_alive():
                                    p.kill()
                        except:
                            pass
                    
                    total_time = time.time() - start_time
                    
                    if queue_size >= stage * 0.9:  # 90%成功率
                        print(f"✅ 队列测试成功")
                        results[stage] = {
                            'success': True,
                            'queue_fill_time': queue_fill_time,
                            'total_time': total_time,
                            'queue_size': queue_size
                        }
                    else:
                        print(f"❌ 队列测试失败")
                        results[stage] = {
                            'success': False,
                            'queue_fill_time': queue_fill_time,
                            'queue_size': queue_size
                        }
                        break
                
            except Exception as e:
                print(f"❌ 队列测试失败: {e}")
                results[stage] = {
                    'success': False,
                    'error': str(e)
                }
                break
        
        return results
    
    def queue_worker(self, q, worker_id):
        """队列工作进程"""
        try:
            time.sleep(0.1)  # 模拟启动时间
            q.put(worker_id)
            time.sleep(2)  # 模拟工作
        except:
            pass
    
    def check_vectordbbench_specific(self):
        """检查VectorDBBench特定问题"""
        print("\n🔍 VectorDBBench 特定问题检查")
        print("=" * 50)
        
        # 1. 检查FAISS服务器状态
        try:
            import requests
            response = requests.get("http://localhost:8005/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                print(f"✅ FAISS服务器状态: {status}")
            else:
                print(f"❌ FAISS服务器响应异常: {response.status_code}")
        except Exception as e:
            print(f"❌ 无法连接FAISS服务器: {e}")
        
        # 2. 检查网络连接限制
        try:
            result = subprocess.run(['ss', '-tuln'], capture_output=True, text=True)
            connections = result.stdout.count('\n')
            print(f"📊 当前网络连接数: {connections}")
        except:
            print("❌ 无法获取网络连接信息")
        
        # 3. 检查系统负载
        load_avg = os.getloadavg()
        cpu_count = psutil.cpu_count()
        if load_avg[0] > cpu_count * 2:
            print(f"⚠️  系统负载过高: {load_avg[0]:.2f} (CPU核心数: {cpu_count})")
        else:
            print(f"✅ 系统负载正常: {load_avg[0]:.2f}")
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🔬 512并发深度诊断")
        print("=" * 60)
        
        # 1. 系统限制检查
        limits = self.check_system_limits()
        
        # 2. 分阶段进程创建测试
        process_results = self.test_process_creation_stages()
        
        # 3. ProcessPoolExecutor测试
        pool_results = self.test_process_pool_executor()
        
        # 4. 队列测试
        queue_results = self.test_mp_manager_queue()
        
        # 5. VectorDBBench特定检查
        self.check_vectordbbench_specific()
        
        # 6. 生成诊断报告
        self.generate_diagnosis_report(limits, process_results, pool_results, queue_results)
    
    def generate_diagnosis_report(self, limits, process_results, pool_results, queue_results):
        """生成诊断报告"""
        print("\n" + "=" * 60)
        print("📋 512并发问题诊断报告")
        print("=" * 60)
        
        print("🔍 问题定位:")
        
        # 分析进程创建结果
        max_successful_processes = 0
        for stage, result in process_results.items():
            if result.get('success', False):
                max_successful_processes = stage
        
        print(f"   最大成功进程数: {max_successful_processes}")
        
        if max_successful_processes < 512:
            print(f"❌ 进程创建在 {max_successful_processes} 处失败")
            print("   可能原因:")
            print("   - 系统进程限制")
            print("   - 内存不足")
            print("   - 文件描述符限制")
        
        # 分析进程池结果
        max_successful_pool = 0
        for stage, result in pool_results.items():
            if result.get('success', False):
                max_successful_pool = stage
        
        print(f"   最大成功进程池: {max_successful_pool}")
        
        # 分析队列结果
        max_successful_queue = 0
        for stage, result in queue_results.items():
            if result.get('success', False):
                max_successful_queue = stage
        
        print(f"   最大成功队列同步: {max_successful_queue}")
        
        print("\n💡 建议解决方案:")
        if max_successful_processes < 512:
            print("1. 增加系统进程限制: ulimit -u 65536")
        if max_successful_pool < 512:
            print("2. 优化ProcessPoolExecutor配置")
        if max_successful_queue < 512:
            print("3. 优化进程间通信机制")
        
        print("4. 考虑降低并发数到安全范围")
        print(f"5. 推荐最大并发数: {min(max_successful_processes, max_successful_pool, max_successful_queue)}")

def main():
    diagnosis = Deep512Diagnosis()
    diagnosis.run_full_diagnosis()

if __name__ == "__main__":
    main()
