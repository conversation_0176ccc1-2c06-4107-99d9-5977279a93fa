#!/bin/bash
# 服务器端环境设置脚本 (setup_server.sh)
# 用于在服务器上设置FAISS服务环境

set -e  # 遇到错误时退出

echo "🔧 设置FAISS服务器环境"
echo "====================="

# 检查Python版本
echo "📋 检查Python版本..."
python3 --version || {
    echo "❌ 错误: 需要Python 3.8+，请先安装Python3"
    exit 1
}

# 创建虚拟环境
echo "📦 创建Python虚拟环境..."
if [ -d "faiss-server-env" ]; then
    echo "⚠️  虚拟环境已存在，跳过创建"
else
    python3 -m venv faiss-server-env
    echo "✅ 虚拟环境创建完成"
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source faiss-server-env/bin/activate

# 安装基础依赖
echo "📥 升级pip..."
pip install --upgrade pip

# 安装FAISS服务器依赖
echo "📥 安装FastAPI和Uvicorn..."
pip install fastapi
pip install "uvicorn[standard]"

echo "📥 安装FAISS库..."
# 检查是否有NVIDIA GPU
if command -v nvidia-smi &> /dev/null; then
    echo "🔍 检测到NVIDIA GPU，安装faiss-gpu..."
    pip install faiss-gpu
else
    echo "💾 未检测到GPU，安装faiss-cpu..."
    pip install faiss-cpu
fi

echo "📥 安装其他依赖..."
pip install numpy             # 数值计算
pip install pydantic          # 数据验证

# 验证安装
echo ""
echo "🔍 验证安装..."
python -c "import faiss; print(f'FAISS版本: {faiss.__version__}')"
python -c "import fastapi; print(f'FastAPI版本: {fastapi.__version__}')"
python -c "import uvicorn; print(f'Uvicorn版本: {uvicorn.__version__}')"

echo ""
echo "✅ 服务器环境设置完成！"
echo ""
echo "🚀 启动FAISS服务器："
echo "   source faiss-server-env/bin/activate"
echo "   uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002"
echo ""
echo "🔗 服务器将在以下地址提供服务："
echo "   http://0.0.0.0:8002"
echo "   文档地址: http://服务器IP:8002/docs"
echo ""
echo "📋 环境信息:"
echo "   Python版本: $(python --version)"
echo "   虚拟环境: faiss-server-env"
echo "   工作目录: $(pwd)"
