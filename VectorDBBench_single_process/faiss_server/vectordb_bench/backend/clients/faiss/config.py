"""Faiss 专用 DBConfig / DBCaseConfig"""

from pydantic import BaseModel
from vectordb_bench.backend.clients.api import DBConfig, DBCaseConfig, MetricType


class FaissConfig(DBConfig):
    """连接本地 FastAPI-Faiss 服务的配置"""

    host: str = "127.0.0.1"
    port: int = 8002
    index_type: str = "Flat"          # Flat / IVFFlat …

    def to_dict(self) -> dict:
        return self.dict()


class FaissDBCaseConfig(BaseModel, DBCaseConfig):
    """远程 FAISS 的 case 配置，包含 metric_type 字段"""
    
    metric_type: MetricType | None = None
    
    def index_param(self) -> dict:
        return {}
    
    def search_param(self) -> dict:
        return {}

