from enum import Enum

from .api import (
    DBCaseConfig,
    DBConfig,
    EmptyDBCaseConfig,
    IndexType,
    MetricType,
    VectorDB,
)

###############################################################################
# ⬇ 仅在官方文件的基础上新增 "Faiss" 分支，其余保持原状
###############################################################################

class DB(Enum):
    """Database types"""

    Faiss = "Faiss"                         # ★ 新增
    FaissLocal = "FaissLocal"               # ★ 新增本地FAISS
    Milvus = "Milvus"
    ZillizCloud = "ZillizCloud"
    Pinecone = "Pinecone"
    ElasticCloud = "ElasticCloud"
    QdrantCloud = "QdrantCloud"
    QdrantLocal = "QdrantLocal"
    WeaviateCloud = "WeaviateCloud"
    PgVector = "PgVector"
    PgVectoRS = "PgVectoRS"
    PgVectorScale = "PgVectorScale"
    PgDiskANN = "PgDiskANN"
    AlloyDB = "AlloyDB"
    Redis = "Redis"
    MemoryDB = "MemoryDB"
    Chroma = "Chroma"
    AWSOpenSearch = "OpenSearch"
    AliyunElasticsearch = "AliyunElasticsearch"
    MariaDB = "MariaDB"
    Test = "test"
    AliyunOpenSearch = "AliyunOpenSearch"
    MongoDB = "MongoDB"
    TiDB = "TiDB"
    Clickhouse = "Clickhouse"
    Vespa = "Vespa"
    LanceDB = "LanceDB"
    OceanBase = "OceanBase"

    # ------------------------------------------------------------------ init_cls
    @property
    def init_cls(self) -> type[VectorDB]:  # noqa: PLR0911
        """Return VectorDB implementation class"""
        if self == DB.Faiss:                              # ★ 新增
            from .faiss.faiss import FaissClient
            return FaissClient
            
        if self == DB.FaissLocal:                         # ★ 新增本地FAISS
            from .faiss_local.faiss_local import FaissLocalClient
            return FaissLocalClient

        if self == DB.Milvus:
            from .milvus.milvus import Milvus
            return Milvus
        # … 其余保持不变 …
        from .test.test import Test
        return Test  # fallback (不会走到此行)

    # ---------------------------------------------------------------- config_cls
    @property
    def config_cls(self) -> type[DBConfig]:  # noqa: PLR0911
        """Return DBConfig subclass"""
        if self == DB.Faiss:                              # ★ 新增
            from .faiss.config import FaissConfig
            return FaissConfig
            
        if self == DB.FaissLocal:                         # ★ 新增本地FAISS
            from .faiss_local.config import FaissLocalConfig
            return FaissLocalConfig

        if self == DB.Milvus:
            from .milvus.config import MilvusConfig
            return MilvusConfig
        # … 其余保持不变 …
        from .test.config import TestConfig
        return TestConfig

    # ----------------------------------------------------------- case_config_cls
    def case_config_cls(     # noqa: PLR0911
        self,
        index_type: IndexType | None = None,
    ) -> type[DBCaseConfig]:
        if self == DB.Faiss:                              # ★ 新增
            return EmptyDBCaseConfig
            
        if self == DB.FaissLocal:                         # ★ 新增本地FAISS
            from .faiss_local.config import HNSWConfig, IVFFlatConfig, IVFPQConfig
            if index_type == IndexType.HNSW:
                return HNSWConfig
            elif index_type == IndexType.IVFFlat:
                return IVFFlatConfig
            elif index_type == IndexType.IVFPQ:
                return IVFPQConfig
            return EmptyDBCaseConfig

        if self == DB.Milvus:
            from .milvus.config import _milvus_case_config
            return _milvus_case_config.get(index_type)
        # … 其余保持不变 …
        return EmptyDBCaseConfig


__all__ = [
    "DB",
    "DBCaseConfig",
    "DBConfig",
    "EmptyDBCaseConfig",
    "IndexType",
    "MetricType",
    "VectorDB",
]

