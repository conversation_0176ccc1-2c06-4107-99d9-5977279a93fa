# FAISS 服务器部署包

## 📋 概述
这个包包含了运行FAISS服务器所需的所有文件。

## 🔧 环境要求
- Python 3.8+
- FAISS库 (CPU或GPU版本)
- 足够的内存和计算资源

## 📥 安装步骤

### 1. 解压部署包
```bash
tar -xzf faiss_server.tar.gz
cd faiss_server
```

### 2. 设置环境
```bash
chmod +x setup_server.sh
./setup_server.sh
```

### 3. 激活环境
```bash
source faiss-server-env/bin/activate
```

## 🚀 启动服务器

### 标准启动
```bash
uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002
```

### 生产环境启动
```bash
uvicorn vectordb_bench.backend.clients.faiss.server:app \
    --host 0.0.0.0 \
    --port 8002 \
    --workers 4 \
    --access-log
```

## 🔗 API 端点
服务器启动后，可以访问：
- API文档: http://服务器IP:8002/docs
- 健康检查: http://服务器IP:8002/health

## 📊 支持的操作
- POST /create_index - 创建FAISS索引
- POST /insert_bulk - 批量插入向量
- POST /search - 向量搜索
- GET /health - 健康检查

## 🔥 防火墙设置
确保开放8002端口：
```bash
sudo ufw allow 8002
# 或者
sudo iptables -A INPUT -p tcp --dport 8002 -j ACCEPT
```

## 📈 性能监控
建议监控以下指标：
- CPU使用率
- 内存使用率
- 网络I/O
- API响应时间

## 🔍 故障排除
1. 检查端口是否被占用: `netstat -tlnp | grep 8002`
2. 查看服务器日志输出
3. 验证FAISS库安装: `python -c "import faiss; print(faiss.__version__)"`
4. 检查防火墙设置

## 📞 支持
如有问题，请检查服务器日志和系统资源使用情况。
