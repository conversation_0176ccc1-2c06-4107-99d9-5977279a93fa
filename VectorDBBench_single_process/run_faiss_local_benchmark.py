#!/usr/bin/env python3
"""
使用本地生成数据运行完整的 FAISS VectorDBBench 测试
避免网络下载，直接使用本地数据进行标准化基准测试
"""

import os
import sys
import time
import numpy as np
import pandas as pd
from pathlib import Path
import logging
from datetime import datetime

# 设置环境
sys.path.insert(0, '/home/<USER>/VectorDBBench')
os.environ['DATASET_LOCAL_DIR'] = '/home/<USER>/VectorDBBench/dataset'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')

def create_local_openai_dataset():
    """创建本地 OpenAI 兼容数据集"""
    print("🔧 创建本地 OpenAI 兼容数据集...")
    
    # 创建数据集目录
    dataset_dir = Path('/home/<USER>/VectorDBBench/dataset/openai/openai_small_50k')
    dataset_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置参数
    np.random.seed(42)
    dim = 1536
    train_size = 50000
    test_size = 1000
    
    print(f"   📊 生成 {train_size:,} 训练向量 + {test_size:,} 测试查询")
    print(f"   📏 向量维度: {dim}D")
    
    # 生成训练数据
    train_vectors = np.random.normal(0, 1, (train_size, dim)).astype(np.float32)
    train_vectors = train_vectors / np.linalg.norm(train_vectors, axis=1, keepdims=True)
    train_ids = np.arange(train_size)
    
    # 生成测试查询
    test_vectors = np.random.normal(0, 1, (test_size, dim)).astype(np.float32)
    test_vectors = test_vectors / np.linalg.norm(test_vectors, axis=1, keepdims=True)
    
    # 计算 ground truth (使用内积，因为向量已归一化)
    print("   🧮 计算 ground truth...")
    similarities = np.dot(test_vectors, train_vectors.T)
    k_for_gt = 100
    neighbors = np.argsort(-similarities, axis=1)[:, :k_for_gt]  # 降序排列，取前k个
    
    # 保存为 parquet 格式
    print("   💾 保存数据集文件...")
    
    # 训练数据
    train_df = pd.DataFrame({
        'id': train_ids,
        'vector': [vec.tolist() for vec in train_vectors]
    })
    train_df.to_parquet(dataset_dir / 'shuffle_train.parquet', index=False)
    
    # 测试查询
    test_df = pd.DataFrame({
        'id': np.arange(test_size),
        'vector': [vec.tolist() for vec in test_vectors]
    })
    test_df.to_parquet(dataset_dir / 'test.parquet', index=False)
    
    # Ground truth neighbors
    neighbors_df = pd.DataFrame({
        'id': np.arange(test_size),
        'neighbors': [neighbors[i].tolist() for i in range(test_size)]
    })
    neighbors_df.to_parquet(dataset_dir / 'neighbors.parquet', index=False)
    
    # 标量标签 (用于过滤，这里生成随机标签)
    scalar_labels = np.random.randint(0, 100, train_size)
    labels_df = pd.DataFrame({
        'id': train_ids,
        'label': scalar_labels
    })
    labels_df.to_parquet(dataset_dir / 'scalar_labels.parquet', index=False)
    
    print(f"   ✅ 数据集创建完成，保存在: {dataset_dir}")
    return dataset_dir

def run_vectordb_benchmark():
    """运行标准 VectorDBBench 测试"""
    print("🚀 启动 VectorDBBench 标准测试...")
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
        from vectordb_bench.backend.cases import CaseType
        from vectordb_bench.backend.clients.api import MetricType
        
        # 确保有数据集
        dataset_dir = create_local_openai_dataset()
        
        # 创建测试配置
        db_config = FaissLocalConfig(
            db_label="faiss_local_benchmark",
            index_type="HNSW"
        )
        
        db_case_config = HNSWConfig(
            m=16,
            ef_construction=200,
            ef_search=64,
            metric_type=MetricType.COSINE,
        )
        
        print("\n🔧 测试配置:")
        print(f"   🏗️  索引类型: {db_config.index_type}")
        print(f"   🔗 HNSW-m: {db_case_config.m}")
        print(f"   🏭 ef_construction: {db_case_config.ef_construction}")
        print(f"   🔍 ef_search: {db_case_config.ef_search}")
        print(f"   📏 距离度量: {db_case_config.metric_type}")
        
        # 运行测试参数
        test_params = {
            'case_type': 'Performance1536D50K',
            'k': 100,
            'num_concurrency': [1],
            'concurrency_duration': 30,
            'concurrency_timeout': 3600,
            'db_label': 'faiss_local_benchmark',
            'load': True,
            'search_serial': True,
            'search_concurrent': True,
            'drop_old': True,
            'dry_run': False,
            'task_label': 'FAISS_Local_Benchmark',
            'custom_case': {}
        }
        
        print(f"\n🎯 测试参数:")
        print(f"   📊 案例类型: {test_params['case_type']}")
        print(f"   🔍 k值: {test_params['k']}")
        print(f"   🧵 并发数: {test_params['num_concurrency']}")
        print(f"   ⏱️  测试时长: {test_params['concurrency_duration']}s")
        
        print("\n🚀 开始基准测试...")
        print("=" * 60)
        
        start_time = time.time()
        
        # 运行基准测试
        run(
            db=DB.FaissLocal,
            db_config=db_config,
            db_case_config=db_case_config,
            **test_params
        )
        
        total_time = time.time() - start_time
        print(f"\n⏱️  总测试时间: {total_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_and_display_results():
    """检查并显示测试结果"""
    print("\n📊 检查测试结果...")
    
    try:
        # 查找结果文件
        results_dir = Path('/home/<USER>/VectorDBBench/results')
        if not results_dir.exists():
            print("⚠️  结果目录不存在")
            return
        
        # 查找最新的结果文件
        result_files = list(results_dir.glob('**/*.json'))
        if not result_files:
            print("⚠️  未找到 JSON 结果文件")
            return
        
        latest_result = max(result_files, key=lambda x: x.stat().st_mtime)
        print(f"📁 最新结果文件: {latest_result.name}")
        
        # 读取并解析结果
        import json
        with open(latest_result, 'r') as f:
            result_data = json.load(f)
        
        print("\n🎯 基准测试结果:")
        print("=" * 50)
        
        if 'results' in result_data:
            for result in result_data['results']:
                case_name = result.get('case', {}).get('name', 'Unknown')
                print(f"\n📋 测试案例: {case_name}")
                
                if 'metrics' in result:
                    metrics = result['metrics']
                    print(f"   🚀 QPS (查询/秒): {metrics.get('qps', 'N/A'):.2f}")
                    print(f"   🎯 召回率: {metrics.get('recall', 'N/A'):.3f}")
                    print(f"   ⏱️  平均延迟: {metrics.get('latency_p50', 'N/A'):.3f}ms")
                    print(f"   📊 P99延迟: {metrics.get('latency_p99', 'N/A'):.3f}ms")
                    print(f"   🏗️  索引构建时间: {metrics.get('load_duration', 'N/A'):.2f}s")
                    print(f"   💾 内存使用: {metrics.get('memory_usage', 'N/A')}")
                
                if 'config' in result:
                    config = result['config']
                    print(f"   ⚙️  配置: {config}")
        
        # 检查日志中的详细信息
        log_file = Path('/home/<USER>/VectorDBBench/logs/vectordb_bench.log')
        if log_file.exists():
            print(f"\n📜 关键日志信息:")
            with open(log_file, 'r') as f:
                lines = f.readlines()
                for line in lines[-20:]:  # 显示最后20行
                    if any(keyword in line.lower() for keyword in ['qps', 'recall', 'latency', 'error', 'failed']):
                        print(f"   {line.strip()}")
        
        print(f"\n✅ 结果检查完成!")
        
    except Exception as e:
        print(f"❌ 检查结果失败: {e}")

def create_performance_summary():
    """创建性能摘要报告"""
    print("\n📈 生成性能摘要报告...")
    
    summary_file = '/home/<USER>/VectorDBBench/faiss_benchmark_summary.txt'
    
    with open(summary_file, 'w') as f:
        f.write("FAISS Local Benchmark 性能摘要报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"测试环境: VectorDBBench + FAISS Local Client\n")
        f.write(f"数据集: 本地生成的 OpenAI 兼容数据 (50K 向量, 1536D)\n")
        f.write(f"索引类型: HNSW\n")
        f.write(f"距离度量: COSINE\n\n")
        f.write("关键配置:\n")
        f.write("- HNSW m=16\n")
        f.write("- ef_construction=200\n")
        f.write("- ef_search=64\n")
        f.write("- k=100\n\n")
        f.write("测试完成状态: 等待结果解析\n")
    
    print(f"📋 摘要报告已保存到: {summary_file}")

if __name__ == "__main__":
    print("🔬 FAISS Local VectorDBBench 集成测试")
    print("=" * 55)
    
    # 运行测试
    success = run_vectordb_benchmark()
    
    # 等待结果写入
    if success:
        time.sleep(3)
        check_and_display_results()
        create_performance_summary()
    
    print(f"\n{'🎉 测试完成!' if success else '💥 测试失败!'}")
    sys.exit(0 if success else 1)
