#!/usr/bin/env python3
"""
测试 VectorDBBench 是否支持远程 FAISS 客户端连接
"""

import sys
from pathlib import Path

def test_remote_faiss_support():
    """测试远程 FAISS 支持情况"""
    print("🔍 检查 VectorDBBench 远程 FAISS 支持")
    print("=" * 50)
    
    try:
        # 1. 检查 DB 枚举中的 Faiss 支持
        from vectordb_bench.backend.clients import DB
        
        available_dbs = [db.value for db in DB]
        print(f"📊 可用的数据库类型:")
        for db in available_dbs:
            if 'faiss' in db.lower() or 'Faiss' in db:
                print(f"   🎯 {db}")
        
        # 2. 测试远程 FAISS 枚举
        if hasattr(DB, 'Faiss'):
            print(f"\n✅ 发现远程 FAISS 支持: DB.Faiss")
            
            # 测试配置类
            try:
                faiss_config_cls = DB.Faiss.config_cls
                print(f"   📋 配置类: {faiss_config_cls.__name__}")
                
                # 创建配置实例
                config = faiss_config_cls()
                print(f"   🔧 默认配置:")
                print(f"      主机: {getattr(config, 'host', 'N/A')}")
                print(f"      端口: {getattr(config, 'port', 'N/A')}")
                print(f"      索引类型: {getattr(config, 'index_type', 'N/A')}")
                
            except Exception as e:
                print(f"   ❌ 配置类加载失败: {e}")
            
            # 测试客户端类
            try:
                faiss_client_cls = DB.Faiss.init_cls
                print(f"   🔌 客户端类: {faiss_client_cls.__name__}")
                
            except Exception as e:
                print(f"   ❌ 客户端类加载失败: {e}")
                
        else:
            print(f"\n❌ 未发现远程 FAISS 支持 (DB.Faiss)")
        
        # 3. 测试本地 FAISS 支持
        if hasattr(DB, 'FaissLocal'):
            print(f"\n✅ 发现本地 FAISS 支持: DB.FaissLocal")
        else:
            print(f"\n❌ 未发现本地 FAISS 支持 (DB.FaissLocal)")
            
        # 4. 检查 FastAPI 服务器代码
        faiss_server_path = Path("vectordb_bench/backend/clients/faiss/server.py")
        if faiss_server_path.exists():
            print(f"\n🌐 发现 FAISS 服务器实现: {faiss_server_path}")
            print("   这表明 VectorDBBench 支持远程 FAISS 服务器模式")
        else:
            print(f"\n⚠️  未找到 FAISS 服务器实现")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cli_run_with_remote_faiss():
    """测试使用 CLI run 函数调用远程 FAISS"""
    print(f"\n🚀 测试 CLI run 函数对远程 FAISS 的支持")
    print("=" * 50)
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        
        # 检查是否可以获取远程 FAISS 配置
        if hasattr(DB, 'Faiss'):
            print("✅ 可以通过 CLI run 函数使用远程 FAISS")
            print("   使用方式:")
            print("   ```python")
            print("   from vectordb_bench.cli.cli import run")
            print("   from vectordb_bench.backend.clients import DB")
            print("   from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig")
            print("")
            print("   # 创建远程 FAISS 配置")
            print("   db_config = FaissConfig(")
            print("       host='your-faiss-server-host',")
            print("       port=8002,")
            print("       index_type='Flat'")
            print("   )")
            print("")
            print("   db_case_config = FaissDBCaseConfig()")
            print("")
            print("   # 运行测试")
            print("   run(")
            print("       db=DB.Faiss,")
            print("       db_config=db_config,")
            print("       db_case_config=db_case_config,")
            print("       case_type='Performance1536D50K',")
            print("       k=100,")
            print("       # ... 其他参数")
            print("   )")
            print("   ```")
            return True
        else:
            print("❌ 无法通过 CLI run 函数使用远程 FAISS")
            print("   原因: DB.Faiss 枚举不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_faiss_server_usage():
    """显示如何启动和使用 FAISS 服务器"""
    print(f"\n📖 FAISS 服务器使用指南")
    print("=" * 50)
    
    server_path = Path("vectordb_bench/backend/clients/faiss/server.py")
    if server_path.exists():
        print("🌐 启动 FAISS 服务器:")
        print("   ```bash")
        print("   cd /home/<USER>/VectorDBBench")
        print("   python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002")
        print("   ```")
        print("")
        print("🔗 服务器 API 端点:")
        print("   • POST /create_index - 创建索引")
        print("   • POST /insert_bulk - 批量插入向量")
        print("   • POST /search - 搜索向量")
        print("")
        print("📊 支持的索引类型:")
        print("   • Flat - 暴力搜索")
        print("   • IVF{数字} - 例如 IVF1024")
    else:
        print("❌ 未找到 FAISS 服务器实现文件")

if __name__ == "__main__":
    print("🎯 VectorDBBench 远程 FAISS 支持测试")
    print("=" * 55)
    
    # 测试远程 FAISS 支持
    support_test = test_remote_faiss_support()
    
    # 测试 CLI 函数支持
    cli_test = test_cli_run_with_remote_faiss()
    
    # 显示使用指南
    show_faiss_server_usage()
    
    print(f"\n📋 测试总结:")
    print(f"   远程 FAISS 支持: {'✅' if support_test else '❌'}")
    print(f"   CLI run 函数支持: {'✅' if cli_test else '❌'}")
    
    if support_test and cli_test:
        print(f"\n🎉 VectorDBBench 完全支持远程 FAISS 客户端连接!")
    else:
        print(f"\n⚠️  VectorDBBench 对远程 FAISS 的支持可能有限")
    
    sys.exit(0 if (support_test and cli_test) else 1)
