#!/usr/bin/env python3
"""
VectorDBBench FAISS 端到端测试
"""

import sys
import time
import random
sys.path.append('/home/<USER>/VectorDBBench')

from vectordb_bench.backend.clients.faiss.faiss import FaissClient
from vectordb_bench.backend.clients.faiss.config import FaissConfig

def test_full_faiss_workflow():
    print("🚀 VectorDBBench FAISS 完整流程测试")
    print("="*60)
    
    # 创建配置
    config = FaissConfig(
        host="***********",
        port=8001,
        index_type="HNSW"
    )
    
    # 创建客户端
    client = FaissClient(
        dim=1536,
        db_config=config,
        db_case_config=None,
        collection_name="test_collection"
    )
    
    # 初始化
    print("🔧 初始化客户端...")
    client.init()
    
    # 测试搜索性能
    print("\n🔍 测试搜索性能...")
    
    # 执行多次搜索测试
    search_times = []
    for i in range(10):
        # 生成随机查询向量
        test_query = [random.uniform(-1, 1) for _ in range(1536)]
        
        start_time = time.time()
        try:
            results = client.search_embedding(test_query, k=10)
            search_time = time.time() - start_time
            search_times.append(search_time)
            
            print(f"  搜索 #{i+1}: {search_time:.4f}秒, 返回 {len(results)} 个结果")
            
        except Exception as e:
            print(f"  ❌ 搜索 #{i+1} 失败: {e}")
            return False
    
    # 计算统计信息
    avg_time = sum(search_times) / len(search_times)
    min_time = min(search_times)
    max_time = max(search_times)
    
    print(f"\n📊 搜索性能统计:")
    print(f"   ⏱️  平均时间: {avg_time:.4f} 秒")
    print(f"   ⚡ 最快时间: {min_time:.4f} 秒")
    print(f"   🐌 最慢时间: {max_time:.4f} 秒")
    print(f"   📈 QPS估算: {1/avg_time:.1f} 查询/秒")
    
    print(f"\n✅ 测试成功！VectorDBBench与FAISS服务器完美集成")
    return True

if __name__ == "__main__":
    success = test_full_faiss_workflow()
    exit(0 if success else 1)
