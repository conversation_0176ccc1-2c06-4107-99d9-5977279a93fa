#!/usr/bin/env python3
"""
专门测试FAISS缓存功能 - 聚焦于缓存逻辑
"""

import sys
import numpy as np
import time

# 添加项目路径
sys.path.append('/home/<USER>/VectorDBBench')

from vectordb_bench.backend.clients.faiss.faiss import FaissClient
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig

def test_cache_scenario(name, index_type, expect_cache=True):
    """测试缓存场景"""
    print(f"\n{'🧪 ' + name}")
    print("-" * 60)
    
    db_config = FaissConfig(
        host="***********",
        port=8000,
        index_type=index_type
    )
    
    db_case_config = FaissDBCaseConfig(m=16, ef_construction=200)
    
    start_time = time.time()
    
    # 创建客户端
    client = FaissClient(
        dim=1536,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="cache_test",
        drop_old=False
    )
    
    # 检查缓存状态
    cache_used = client._server_has_data and client._cache_validated
    
    print(f"📊 缓存状态检查:")
    print(f"   - 服务端有数据: {client._server_has_data}")
    print(f"   - 缓存已验证: {client._cache_validated}")
    print(f"   - 预期使用缓存: {expect_cache}")
    print(f"   - 实际使用缓存: {cache_used}")
    
    # 测试插入行为
    test_vectors = np.random.random((1000, 1536)).tolist()
    metadata = list(range(1000))
    
    insert_start = time.time()
    result, error = client.insert_embeddings(test_vectors, metadata)
    insert_time = time.time() - insert_start
    
    print(f"📝 插入测试结果:")
    print(f"   - 插入向量数: {result}")
    print(f"   - 插入耗时: {insert_time:.3f}秒")
    print(f"   - 错误信息: {error}")
    
    # 判断是否符合预期
    cache_worked = (cache_used == expect_cache)
    fast_insert = insert_time < 0.1  # 缓存应该很快
    
    if expect_cache:
        success = cache_worked and fast_insert
        print(f"✅ 缓存预期: {'成功' if success else '失败'}")
    else:
        success = not cache_used
        print(f"✅ 非缓存预期: {'成功' if success else '失败'}")
    
    total_time = time.time() - start_time
    print(f"⏱️  总耗时: {total_time:.3f}秒")
    
    return success

def main():
    print("🚀 FAISS 缓存功能专项测试")
    print("=" * 80)
    
    # 缓存测试场景
    test_cases = [
        ("场景1: HNSW - 应该使用现有缓存", "HNSW", True),
        ("场景2: HNSW - 再次测试缓存复用", "HNSW", True), 
        ("场景3: Flat - 索引类型不匹配", "Flat", False),
        ("场景4: HNSW - 回到HNSW应该重新缓存", "HNSW", True),
    ]
    
    results = []
    
    for name, index_type, expect_cache in test_cases:
        success = test_cache_scenario(name, index_type, expect_cache)
        results.append((name, success))
        time.sleep(1)  # 稍等片刻
    
    # 结果汇总
    print(f"\n{'='*80}")
    print("📋 缓存功能测试结果")
    print(f"{'='*80}")
    
    success_count = 0
    for name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {name}")
        if success:
            success_count += 1
    
    print(f"\n🎯 缓存功能测试: {success_count}/{len(results)} 成功")
    
    if success_count == len(results):
        print("🎉 缓存功能完全正常！数据复用机制工作完美！")
    else:
        print("⚠️ 部分缓存测试失败")
    
    print(f"\n💡 总结: 对于相同数据集和索引类型，系统能够智能复用缓存，避免重复embind操作")

if __name__ == "__main__":
    main()
