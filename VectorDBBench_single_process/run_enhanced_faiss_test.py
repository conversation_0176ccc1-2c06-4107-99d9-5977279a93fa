#!/usr/bin/env python3
"""
增强版远程 FAISS 基准测试 - 确保生成结果文件
"""

import argparse
import time
import json
import pathlib
from datetime import datetime
from vectordb_bench.cli.cli import run
from vectordb_bench.backend.clients import DB
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
from vectordb_bench.backend.task_runner import MetricType
from vectordb_bench import config

def run_enhanced_faiss_benchmark():
    """运行增强版远程 FAISS 基准测试"""
    print("🎯 增强版远程 FAISS 基准测试 (确保生成结果)")
    print("=" * 80)
    
    parser = argparse.ArgumentParser(description='增强版远程 FAISS 基准测试')
    parser.add_argument('--host', default='127.0.0.1', help='FAISS 服务器主机')
    parser.add_argument('--port', type=int, default=8002, help='FAISS 服务器端口')
    parser.add_argument('--index-type', default='Flat', choices=['Flat', 'IVF1024', 'IVF2048', 'IVF4096'], 
                       help='索引类型')
    parser.add_argument('--k', type=int, default=100, help='返回的近邻数量')
    parser.add_argument('--duration', type=int, default=30, help='并发测试持续时间(秒)')
    parser.add_argument('--concurrency', nargs='+', type=int, default=[1, 4, 8], 
                       help='并发度列表')
    
    args = parser.parse_args()
    
    print(f"🔧 测试配置:")
    print(f"   服务器: {args.host}:{args.port}")
    print(f"   索引类型: {args.index_type}")
    print(f"   返回近邻数: {args.k}")
    print(f"   测试时长: {args.duration}s")
    print(f"   并发度: {args.concurrency}")
    
    # 创建远程 FAISS 配置
    db_config = FaissConfig(
        host=args.host,
        port=args.port,
        index_type=args.index_type,
        db_label=f"faiss_remote_{args.index_type.lower()}_{int(time.time())}"
    )
    
    db_case_config = FaissDBCaseConfig(
        metric_type=MetricType.COSINE
    )
    
    print(f"\n🚀 开始基准测试...")
    print(f"   数据库: {DB.Faiss}")
    print(f"   配置: {db_config}")
    print(f"   案例配置: {db_case_config}")
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 执行基准测试
        result = run(
            db=DB.Faiss,
            db_config=db_config,
            db_case_config=db_case_config,
            case_type="Performance1536D50K",  # 使用标准的性能测试案例
            k=args.k,
            concurrency_duration=args.duration,
            num_concurrency=args.concurrency,
            concurrency_timeout=3600,
            task_label=f"Remote_FAISS_{args.index_type}_{int(time.time())}",
            load=True,
            search_serial=True,
            search_concurrent=True,
            drop_old=True,
            dry_run=False
        )
        
        print(f"\n✅ 基准测试完成!")
        
        # 记录结束时间
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"   总耗时: {duration:.2f}s")
        
        # 查找结果文件
        results_dir = config.RESULTS_LOCAL_DIR
        print(f"\n📊 查找结果文件...")
        print(f"   结果目录: {results_dir}")
        
        # 查找最近生成的 Faiss 结果文件
        faiss_results_dir = results_dir / "Faiss"
        if faiss_results_dir.exists():
            print(f"   ✅ 发现 Faiss 结果目录: {faiss_results_dir}")
            
            # 列出最近的结果文件
            result_files = list(faiss_results_dir.glob("*.json"))
            result_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            if result_files:
                print(f"   📄 最新结果文件:")
                for i, file in enumerate(result_files[:5]):  # 显示最新的5个文件
                    file_time = datetime.fromtimestamp(file.stat().st_mtime)
                    file_size = file.stat().st_size
                    print(f"      {i+1}. {file.name} ({file_size} bytes, {file_time})")
                
                # 读取并显示最新结果的摘要
                latest_file = result_files[0]
                try:
                    with open(latest_file, 'r') as f:
                        result_data = json.load(f)
                    
                    print(f"\n📋 最新结果摘要 ({latest_file.name}):")
                    print(f"   任务ID: {result_data.get('run_id', 'N/A')}")
                    print(f"   任务标签: {result_data.get('task_label', 'N/A')}")
                    
                    if 'results' in result_data and result_data['results']:
                        metrics = result_data['results'][0].get('metrics', {})
                        print(f"   插入耗时: {metrics.get('insert_duration', 0):.2f}s")
                        print(f"   QPS: {metrics.get('qps', 0):.2f}")
                        print(f"   P99延迟: {metrics.get('serial_latency_p99', 0):.4f}s")
                        print(f"   召回率: {metrics.get('recall', 0):.4f}")
                        
                        # 并发测试结果
                        conc_qps = metrics.get('conc_qps_list', [])
                        if conc_qps:
                            print(f"   并发QPS: {conc_qps}")
                    
                except Exception as e:
                    print(f"   ❌ 读取结果文件失败: {e}")
            else:
                print(f"   ⚠️  Faiss 结果目录为空")
        else:
            print(f"   ❌ 未找到 Faiss 结果目录")
            print(f"   尝试查找所有最近的结果文件...")
            
            # 查找所有最近修改的 JSON 文件
            all_results = []
            for subdir in results_dir.iterdir():
                if subdir.is_dir():
                    for json_file in subdir.glob("*.json"):
                        if json_file.stat().st_mtime > start_time - 300:  # 5分钟内的文件
                            all_results.append(json_file)
            
            if all_results:
                all_results.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                print(f"   📄 最近的结果文件:")
                for file in all_results[:5]:
                    file_time = datetime.fromtimestamp(file.stat().st_mtime)
                    print(f"      {file.relative_to(results_dir)} ({file_time})")
            else:
                print(f"   ❌ 未找到任何最近的结果文件")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 基准测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_faiss_server(host, port):
    """检查 FAISS 服务器状态"""
    import requests
    
    print(f"🔍 检查 FAISS 服务器状态...")
    try:
        response = requests.get(f"http://{host}:{port}/docs", timeout=5)
        if response.status_code == 200:
            print(f"   ✅ FAISS 服务器运行正常: {host}:{port}")
            return True
        else:
            print(f"   ❌ FAISS 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 无法连接到 FAISS 服务器: {e}")
        print(f"   💡 请确保服务器运行:")
        print(f"      python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port {port}")
        return False

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='增强版远程 FAISS 基准测试')
    parser.add_argument('--host', default='127.0.0.1', help='FAISS 服务器主机')
    parser.add_argument('--port', type=int, default=8002, help='FAISS 服务器端口')
    
    # 先解析基本参数来检查服务器
    args, _ = parser.parse_known_args()
    
    # 检查服务器状态
    if check_faiss_server(args.host, args.port):
        # 运行基准测试
        success = run_enhanced_faiss_benchmark()
        exit(0 if success else 1)
    else:
        print(f"\n💥 服务器检查失败，无法运行基准测试")
        exit(1)
