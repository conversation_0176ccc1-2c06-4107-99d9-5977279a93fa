#!/usr/bin/env python3
"""
🎯 完全无数据集依赖的 FAISS 远程客户端
通过服务端 API 获取所有数据集信息，客户端无需访问本地数据集
"""

import click
import requests
import os
import tempfile
from typing import Annotated, Unpack
from pathlib import Path

from vectordb_bench.cli.cli import (
    CommonTypedDict,
    cli,
    click_parameter_decorators_from_typed_dict,
    run,
)
from vectordb_bench.backend.clients import DB

class ServerOnlyFaissTypedDict(CommonTypedDict):
    """完全无数据集依赖的FAISS客户端配置"""
    uri: Annotated[
        str, click.option(
            "--uri", 
            type=str, 
            help="远程 FAISS 服务器 URI", 
            default="localhost:8002"
        ),
    ]

def create_mock_dataset_structure(case_type: str, server_info: dict):
    """基于服务端信息创建最小化的本地数据集结构"""
    
    # 创建临时目录结构
    temp_dir = Path(tempfile.gettempdir()) / "vectordb_bench_mock" / "dataset"
    
    # 根据 case_type 确定路径结构
    case_mapping = {
        "Performance1536D50K": "openai/openai_small_50k",
        "Performance1536D500K": "openai/openai_medium_500k", 
        "Performance768D1M": "cohere/cohere_medium_1m"
    }
    
    relative_path = case_mapping.get(case_type, f"unknown/{case_type.lower()}")
    dataset_dir = temp_dir / relative_path
    dataset_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建最小化的占位文件（VectorDBBench 需要检查路径存在）
    placeholder_file = dataset_dir / "shuffle_train.parquet"
    if not placeholder_file.exists():
        # 创建一个空的占位文件
        placeholder_file.touch()
    
    return str(temp_dir)

@cli.command()
@click_parameter_decorators_from_typed_dict(ServerOnlyFaissTypedDict)
def faissremoteonly(**parameters: Unpack[ServerOnlyFaissTypedDict]):
    """
    完全服务端化的远程 FAISS 客户端
    
    客户端无需任何数据集访问权限，所有数据集处理都在服务端！
    """
    from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
    
    # 解析服务器地址
    uri = parameters.get("uri", "localhost:8002")
    if ':' in uri:
        host, port = uri.split(':')
        port = int(port.strip())
    else:
        host, port = uri.strip(), 8002
    
    case_type = parameters.get("case_type")
    
    print(f"🌐 连接服务端数据集管理的 FAISS 服务器: {host}:{port}")
    
    try:
        # 1. 从服务端获取数据集信息
        response = requests.get(f"http://{host}:{port}/datasets", timeout=10)
        if response.status_code != 200:
            print(f"❌ 无法获取服务端数据集信息: HTTP {response.status_code}")
            return
            
        server_datasets = response.json()
        print(f"✅ 服务端管理 {len(server_datasets.get('datasets', []))} 个数据集")
        
        # 2. 验证案例类型在服务端是否可用
        available_cases = [ds['case_type'] for ds in server_datasets.get('datasets', [])]
        if case_type and case_type not in available_cases:
            print(f"⚠️ 案例类型 {case_type} 在服务端不可用")
            print(f"📋 可用案例: {', '.join(available_cases)}")
            
        # 3. 获取具体案例的服务端信息
        if case_type:
            response = requests.post(f"http://{host}:{port}/switch_dataset/{case_type}", timeout=30)
            if response.status_code == 200:
                dataset_info = response.json()
                print(f"✅ 服务端已准备数据集: {case_type}")
                if 'dataset_info' in dataset_info:
                    info = dataset_info['dataset_info']
                    print(f"   📊 维度: {info.get('dim', 'N/A')}")
                    print(f"   📈 大小: {info.get('size', 'N/A'):,} 向量")
                    print(f"   📂 服务端路径: {info.get('path', 'N/A')}")
        
        # 4. 创建最小化的本地占位结构（欺骗 VectorDBBench 框架）
        mock_dataset_dir = create_mock_dataset_structure(case_type, server_datasets)
        
        # 5. 临时设置环境变量（仅用于框架兼容）
        original_dataset_dir = os.environ.get('DATASET_LOCAL_DIR')
        os.environ['DATASET_LOCAL_DIR'] = mock_dataset_dir
        
        print(f"🎭 创建临时占位数据集结构: {mock_dataset_dir}")
        print(f"⚡ 所有实际数据处理都在服务端执行！")
        
        try:
            # 6. 运行 VectorDBBench（框架会使用占位结构，实际数据来自服务端）
            print(f"🎯 启动完全服务端化的测试...")
            
            run(
                db=DB.Faiss,
                db_config=FaissConfig(
                    host=host,
                    port=port,
                    index_type="Auto",
                    case_type=case_type
                ),
                db_case_config=FaissDBCaseConfig(),
                **parameters,
            )
            
            print(f"🎊 测试完成！所有数据处理都在服务端完成。")
            
        finally:
            # 7. 恢复原始环境变量
            if original_dataset_dir:
                os.environ['DATASET_LOCAL_DIR'] = original_dataset_dir
            else:
                os.environ.pop('DATASET_LOCAL_DIR', None)
                
    except requests.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        print(f"💡 请确保 FAISS 服务器在 {host}:{port} 运行")
    except Exception as e:
        print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    print("🎯 完全服务端化的 FAISS 远程客户端")
    print("\n✨ 特点:")
    print("   🚫 客户端无需访问任何数据集")
    print("   📡 所有数据处理都在服务端")
    print("   🎭 自动创建最小化占位结构") 
    print("   ⚡ 与 VectorDBBench 框架完全兼容")
    print("\n🔧 使用方式:")
    print("python enhanced_server_only_client.py faissremoteonly --case-type Performance1536D50K")
