# blank_template.py
from typing import Any, get_origin
import pandas as pd
from pydantic import BaseModel
from vectordb_bench.backend.cases import CapacityDim128  # 换 Case！

def blank(model: type[BaseModel]) -> dict:
    tmpl = {}
    for name, field in model.__fields__.values():
        if not field.required:
            continue                                # 只管必填项
        py_t = field.outer_type_
        # 跳过 DataFrame
        if py_t is pd.DataFrame or get_origin(py_t) is pd.DataFrame:
            continue
        if py_t in (int, float):
            tmpl[name] = 0
        elif py_t is bool:
            tmpl[name] = False
        elif py_t is str:
            tmpl[name] = ""
        elif get_origin(py_t) is list:
            tmpl[name] = []
        elif issubclass(type(py_t), type) and issubclass(py_t, BaseModel):
            tmpl[name] = blank(py_t)               # 递归
        else:
            tmpl[name] = None
    return tmpl

from pprint import pprint
pprint(blank(CapacityDim128), depth=None)

