#!/usr/bin/env python3
"""
通过HTTP API直接测试 Cohere 10M 数据集
"""

import json
import time
import numpy as np
try:
    import requests
except ImportError:
    print("❌ 需要安装 requests: pip3 install requests")
    exit(1)

def test_faiss_server():
    """测试FAISS服务器"""
    base_url = "http://***********:8001"
    
    print("🔌 测试服务器连接...")
    
    # 1. 测试服务器状态
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器连接成功")
            print(f"📊 服务器信息: {response.json()}")
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False
    
    # 2. 获取服务器信息
    try:
        response = requests.get(f"{base_url}/info", timeout=10)
        if response.status_code == 200:
            info = response.json()
            print("📋 服务器详细信息:")
            print(json.dumps(info, indent=2, ensure_ascii=False))
        else:
            print(f"⚠️ 无法获取服务器信息: {response.status_code}")
    except Exception as e:
        print(f"⚠️ 获取信息失败: {e}")
    
    # 3. 创建768维HNSW索引 (对应 Cohere 数据集)
    print("\n🏗️ 创建 768维 HNSW 索引...")
    
    create_request = {
        "dim": 768,
        "index_type": "HNSW",
        "m": 16,
        "ef_construction": 200
    }
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/create_index",
            json=create_request,
            timeout=1800  # 30分钟超时，因为要加载10M数据
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 索引创建成功: {result}")
            print(f"⏱️ 创建耗时: {end_time - start_time:.2f} 秒")
        else:
            print(f"❌ 索引创建失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
    except requests.exceptions.Timeout:
        print("⏰ 索引创建超时")
        return False
    except Exception as e:
        print(f"❌ 索引创建异常: {e}")
        return False
    
    # 4. 检查索引状态
    try:
        response = requests.get(f"{base_url}/status", timeout=10)
        if response.status_code == 200:
            status = response.json()
            print("📊 当前索引状态:")
            print(json.dumps(status, indent=2, ensure_ascii=False))
            
            total_vectors = status.get('total_vectors', 0)
            if total_vectors >= 1000000:
                print(f"✅ 成功加载 {total_vectors:,} 个向量")
            else:
                print(f"⚠️ 向量数量较少: {total_vectors:,}")
        else:
            print(f"⚠️ 无法获取状态: {response.status_code}")
    except Exception as e:
        print(f"⚠️ 状态检查失败: {e}")
    
    # 5. 测试搜索性能
    print("\n🔍 测试搜索性能...")
    
    # 生成随机查询向量
    query_vector = np.random.random(768).tolist()
    
    search_times = []
    num_searches = 10
    
    for i in range(num_searches):
        search_request = {
            "query": query_vector,
            "topk": 100
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/search",
                json=search_request,
                timeout=30
            )
            end_time = time.time()
            
            if response.status_code == 200:
                search_time = end_time - start_time
                search_times.append(search_time)
                
                result = response.json()
                ids = result.get('ids', [[]])[0]
                distances = result.get('distances', [[]])[0]
                
                print(f"🎯 搜索 {i+1}/{num_searches}: {search_time*1000:.2f}ms, 返回 {len(ids)} 个结果")
                
                if i == 0:  # 显示第一次搜索的详细结果
                    print(f"   前5个结果ID: {ids[:5]}")
                    print(f"   前5个距离: {[f'{d:.4f}' for d in distances[:5]]}")
            else:
                print(f"❌ 搜索失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 搜索异常: {e}")
    
    # 6. 计算性能指标
    if search_times:
        avg_latency = np.mean(search_times) * 1000  # ms
        min_latency = np.min(search_times) * 1000
        max_latency = np.max(search_times) * 1000
        qps = 1.0 / np.mean(search_times)
        
        print(f"\n📈 性能统计 (基于 {len(search_times)} 次搜索):")
        print(f"   平均延迟: {avg_latency:.2f} ms")
        print(f"   最小延迟: {min_latency:.2f} ms")
        print(f"   最大延迟: {max_latency:.2f} ms")
        print(f"   估算QPS: {qps:.2f}")
    
    return True

def main():
    print("🧪 Cohere 768维 10M 数据集 HTTP API 测试")
    print("=" * 60)
    
    success = test_faiss_server()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试完成")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
