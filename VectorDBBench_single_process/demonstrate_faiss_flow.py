#!/usr/bin/env python3
"""
🎯 直接展示FAISS数据集调用全流程
通过实际API调用展示服务端如何使用您的数据集
"""
import requests
import json
import time

def main():
    print("🎯 FAISS数据集调用全流程实战演示")
    print("=" * 60)
    
    server_url = "http://localhost:8005"  # 使用运行中的高级服务器
    
    print(f"🔧 服务器: {server_url}")
    print(f"📂 目标数据集: Performance1536D50K")
    print(f"📍 预期路径: /nas/yvan.chen/milvus/dataset/openai/openai_small_50k")
    
            # 第一步：触发数据集加载，观察服务端的完整调用过程
            print(f"\n🚀 第一步: 触发数据集加载")
            print("-" * 40)
            
            # 使用查询参数而不是JSON请求体
            params = {
                "case_type": "Performance1536D50K",
                "force_reload": False
            }
            
            print(f"📤 发送加载请求: {params}")
            
            try:
                load_response = requests.post(f"{server_url}/load_dataset", params=params, timeout=60)        if load_response.status_code == 200:
            load_result = load_response.json()
            print(f"✅ 数据集加载成功!")
            print(f"📊 响应: {json.dumps(load_result, indent=2, ensure_ascii=False)}")
            
            # 第二步：查看服务器状态，确认数据集已加载
            print(f"\n🔍 第二步: 查看服务器状态")
            print("-" * 40)
            
            status_response = requests.get(f"{server_url}/status")
            if status_response.status_code == 200:
                status = status_response.json()
                print(f"📋 服务器状态:")
                print(f"   🟢 运行状态: {status.get('status')}")
                print(f"   📊 数据集数量: {len(status.get('datasets', {}))}")
                
                # 显示详细的数据集信息
                datasets = status.get('datasets', {})
                for name, info in datasets.items():
                    print(f"\n   📁 数据集 '{name}':")
                    print(f"      📂 实际路径: {info.get('path')}")
                    print(f"      📐 向量维度: {info.get('vector_dim')}")
                    print(f"      📊 向量数量: {info.get('vector_count'):,}")
                    print(f"      📅 加载时间: {info.get('loaded_at')}")
                    print(f"      💾 内存占用: {info.get('memory_mb'):.1f} MB")
            
            # 第三步：执行FAISS测试，观察索引创建和搜索过程
            print(f"\n🧪 第三步: 执行FAISS基准测试")
            print("-" * 40)
            
            benchmark_data = {
                "case_type": "Performance1536D50K",
                "test_params": {
                    "num_queries": 50,
                    "topk": 10
                }
            }
            
            print(f"📤 发送测试请求: {benchmark_data}")
            print(f"⏰ 开始时间: {time.strftime('%H:%M:%S')}")
            
            bench_response = requests.post(f"{server_url}/benchmark", json=benchmark_data, timeout=120)
            
            if bench_response.status_code == 200:
                result = bench_response.json()
                print(f"✅ FAISS测试完成!")
                
                # 展示关键的FAISS标识信息
                print(f"\n🎯 FAISS验证信息:")
                print(f"   🏷️ 引擎类型: {result.get('engine')}")
                print(f"   🆔 索引名称: {result.get('index_name')}")
                print(f"   🔧 索引类型: {result.get('index_type')}")
                
                # 显示库信息确认使用FAISS
                lib_info = result.get('library_info', {})
                print(f"   📚 库名称: {lib_info.get('name')}")
                print(f"   🔢 版本: {lib_info.get('version')}")
                print(f"   🏗️ 索引类: {lib_info.get('index_class')}")
                print(f"   🆔 对象ID: {lib_info.get('index_object_id')}")
                
                # 显示性能指标
                perf = result.get('performance_metrics', {})
                print(f"\n📊 性能指标:")
                print(f"   🚀 QPS: {perf.get('qps', 0):,.2f}")
                print(f"   ⏱️ 平均延迟: {perf.get('avg_latency_ms', 0):.3f} ms")
                print(f"   📈 P99延迟: {perf.get('p99_latency_ms', 0):.3f} ms")
                print(f"   🕐 总耗时: {perf.get('total_time_s', 0):.3f} 秒")
                
                # 显示数据集信息确认使用了正确的数据
                dataset_info = result.get('dataset_info', {})
                print(f"\n📁 使用的数据集:")
                print(f"   📂 路径: {dataset_info.get('path')}")
                print(f"   📐 维度: {dataset_info.get('dim')}")
                print(f"   📊 大小: {dataset_info.get('size'):,} 向量")
                
                # 验证结果
                print(f"\n🔍 验证结果:")
                is_faiss = all([
                    result.get('engine') == 'faiss',
                    'faiss' in result.get('index_name', '').lower(),
                    lib_info.get('name') == 'faiss',
                    'Index' in lib_info.get('index_class', ''),
                    perf.get('qps', 0) > 100
                ])
                
                if is_faiss:
                    print(f"   ✅ 确认: 这是真正的FAISS测试!")
                    print(f"   ✅ 确认: 使用了您提供的数据集!")
                    print(f"   ✅ 确认: 服务端正确调用了数据路径!")
                else:
                    print(f"   ❌ 警告: 无法完全确认是FAISS测试")
            else:
                print(f"❌ 测试失败: HTTP {bench_response.status_code}")
                print(f"📝 错误信息: {bench_response.text}")
                
        else:
            print(f"❌ 数据集加载失败: HTTP {load_response.status_code}")
            print(f"📝 错误信息: {load_response.text}")
            
    except requests.exceptions.Timeout:
        print(f"⏰ 请求超时 - 数据集可能很大，需要更长时间加载")
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

    print(f"\n🎊 演示完成!")
    print(f"📋 核心要点:")
    print(f"   1️⃣ 服务端通过case_type精确映射到您的数据集路径")
    print(f"   2️⃣ 使用pandas读取parquet文件，提取'emb'列")
    print(f"   3️⃣ 创建真正的FAISS索引(IndexHNSWFlat)")
    print(f"   4️⃣ 执行FAISS搜索并测量性能")
    print(f"   5️⃣ 返回详细的FAISS标识信息供验证")

if __name__ == "__main__":
    main()
