2025-07-18 14:33:05,133 | DEBUG | Environment variable FAISS_OPT_LEVEL is not set, so let's pick the instruction set according to the current CPU
2025-07-18 14:33:05,134 | INFO | Loading faiss with SVE support.
2025-07-18 14:33:05,134 | INFO | Could not load library with SVE support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_sve'")
2025-07-18 14:33:05,134 | INFO | Loading faiss.
2025-07-18 14:33:05,145 | INFO | Successfully loaded faiss.
2025-07-18 14:33:05,147 | INFO | Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-18 14:40:20,724 | DEBUG | Environment variable FAISS_OPT_LEVEL is not set, so let's pick the instruction set according to the current CPU
2025-07-18 14:40:20,725 | INFO | Loading faiss with SVE support.
2025-07-18 14:40:20,725 | INFO | Could not load library with SVE support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_sve'")
2025-07-18 14:40:20,725 | INFO | Loading faiss.
2025-07-18 14:40:20,736 | INFO | Successfully loaded faiss.
2025-07-18 14:40:20,738 | INFO | Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
