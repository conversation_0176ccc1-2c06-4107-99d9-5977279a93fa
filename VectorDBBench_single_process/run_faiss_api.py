#!/usr/bin/env python
"""
直接跑  Performance768D1M  +  Faiss

🔹 已假定你的 FastAPI-Faiss 服务器监听 0.0.0.0:8002
🔹 数据集用官方枚举 DatasetWithSizeType.CohereMedium
"""

import multiprocessing as mp
import time
import uvicorn

from vectordb_bench.backend.task_runner import TaskRunner, CaseRunner
from vectordb_bench.backend.clients import DB            # 我们在 __init__.py 里已注册 Faiss
from vectordb_bench.backend.cases import Performance768D1M
from vectordb_bench.backend.dataset import DatasetWithSizeType
from vectordb_bench.backend.data_source import DatasetSource
from vectordb_bench.backend.clients.faiss.config import FaissConfig


def start_faiss_server():
    """本地启动 FastAPI Server（如已有独立进程可删掉）"""
    uvicorn.run(
        "vectordb_bench.backend.clients.faiss.server:app",
        host="0.0.0.0",
        port=8002,
        reload=False,
    )


def build_case() -> Performance768D1M:
    """用官方枚举，而不是自己拼 dict"""

    return Performance768D1M(
        case_id=3,
        name="Perf-768D-1M (Faiss Demo)",
        description="Benchmark Faiss with Cohere-medium-1M",
        dataset_with_size_type=DatasetWithSizeType.CohereMedium,
    )


def run_vectordb_bench():
    # -------------------- Case --------------------
    case = build_case()

    # -------------------- DB Config ----------------
    db_cfg = FaissConfig(host="127.0.0.1", port=8002, index_type="Flat")

    # CaseRunner 里所有 config 字段都要是 *基本类型*，不能塞 BaseModel
    case_runner = CaseRunner.model_validate(
        {
            "run_id": "faiss_run_001",
            "config": {
                "db": DB.Faiss.value,
                "db_config": db_cfg.to_dict(),
                "db_case_config": {},         # 目前 Faiss 没有 per-case 参数
                "case_config": {},            # 可以留空
            },
            "ca": case,
            "status": 1,                      # RunningStatus.PENDING
            "dataset_source": DatasetSource.S3.value,
        }
    )

    task_runner = TaskRunner.model_validate(
        {
            "run_id": "faiss_run_001",
            "task_label": "faiss_benchmark",
            "case_runners": [case_runner],
            "status": 1,                      # RunningStatus.PENDING
        }
    )

    task_runner.run()                         # 🚀 真正开始 benchmark


def main():
    print("[*] 启动 FAISS 服务 ...")
    p = mp.Process(target=start_faiss_server, daemon=True)
    p.start()
    time.sleep(3)                             # 等服务就绪

    try:
        run_vectordb_bench()
    finally:
        print("[*] 关闭 FAISS 服务 ...")
        p.terminate()
        p.join()


if __name__ == "__main__":
    main()

