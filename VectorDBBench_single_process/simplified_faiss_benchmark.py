#!/usr/bin/env python3
"""
简化的VectorDBBench FAISS测试，避免下载大数据集
"""

import sys
import os
import time
import random
import numpy as np

sys.path.append('/home/<USER>/VectorDBBench')

from vectordb_bench.backend.clients.faiss.faiss import FaissClient
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig

def simplified_benchmark_test():
    """简化的基准测试，模拟VectorDBBench的工作流程"""
    
    print("🎯 简化版VectorDBBench FAISS测试")
    print("=" * 60)
    
    # 配置参数 (模拟你的命令行参数)
    host = "localhost"
    port = 8000
    index_type = "Flat"
    dim = 768
    
    print(f"🌐 连接FAISS服务器: {host}:{port}")
    print(f"📊 索引类型: {index_type}")
    print(f"📏 向量维度: {dim}")
    
    # 创建客户端配置
    db_config = FaissConfig(
        host=host,
        port=port,
        index_type=index_type
    )
    
    db_case_config = FaissDBCaseConfig()
    
    try:
        # 初始化客户端
        print(f"\n🔧 初始化FAISS客户端...")
        client = FaissClient(
            dim=dim,
            db_config=db_config,
            db_case_config=db_case_config,
            collection_name="benchmark_test",
            drop_old=True  # 模拟VectorDBBench的drop_old参数
        )
        
        with client.init():
            # 阶段1: 数据加载测试
            print(f"\n📈 阶段1: 数据加载测试")
            print("-" * 40)
            
            # 模拟插入一些测试数据 (而不是下载1M数据集)
            test_size = 10000  # 用10K向量代替1M向量进行快速测试
            print(f"📝 模拟插入 {test_size:,} 个向量...")
            
            start_time = time.time()
            
            # 分批生成测试向量，避免内存问题
            batch_size = 1000
            total_inserted = 0
            
            for i in range(0, test_size, batch_size):
                current_batch_size = min(batch_size, test_size - i)
                test_vectors = np.random.random((current_batch_size, dim)).tolist()
                test_metadata = list(range(i, i + current_batch_size))
                
                inserted_count, error = client.insert_embeddings(test_vectors, test_metadata)
                
                if error is None:
                    total_inserted += inserted_count
                    if (i // batch_size + 1) % 5 == 0:  # 每5批显示进度
                        print(f"   📊 已处理 {total_inserted:,} / {test_size:,} 个向量")
                else:
                    print(f"❌ 插入失败: {error}")
                    break
            
            load_duration = time.time() - start_time
            print(f"✅ 数据加载完成！耗时: {load_duration:.2f} 秒")
            print(f"📈 加载性能: {total_inserted / load_duration:.1f} 向量/秒")
            
            # 阶段2: 搜索性能测试
            print(f"\n🔍 阶段2: 搜索性能测试")
            print("-" * 40)
            
            # 生成搜索查询
            num_queries = 100  # 100个查询
            k = 100  # top-100结果
            queries = np.random.random((num_queries, dim)).tolist()
            
            print(f"🎯 执行 {num_queries} 次搜索，每次返回top-{k}结果...")
            
            # 预热
            warmup_query = queries[0]
            try:
                client.search_embedding(warmup_query, k=10)
                print("🔥 预热完成")
            except Exception as e:
                print(f"⚠️ 预热失败: {e}")
            
            # 性能测试
            start_time = time.time()
            successful_searches = 0
            total_results = 0
            latencies = []
            
            for i, query in enumerate(queries):
                try:
                    query_start = time.time()
                    result = client.search_embedding(query, k=k)
                    query_latency = (time.time() - query_start) * 1000  # ms
                    
                    latencies.append(query_latency)
                    total_results += len(result)
                    successful_searches += 1
                    
                    if (i + 1) % 20 == 0:
                        elapsed = time.time() - start_time
                        current_qps = (i + 1) / elapsed
                        print(f"   进度: {i+1}/{num_queries}, QPS: {current_qps:.2f}")
                        
                except Exception as e:
                    print(f"   ❌ 搜索 {i+1} 失败: {e}")
                    continue
            
            search_duration = time.time() - start_time
            
            # 计算性能指标
            if successful_searches > 0:
                avg_qps = successful_searches / search_duration
                avg_latency = sum(latencies) / len(latencies)
                p99_latency = sorted(latencies)[int(len(latencies) * 0.99)] if latencies else 0
                avg_results = total_results / successful_searches
                
                print(f"\n📊 性能测试结果:")
                print(f"   ✅ 成功查询: {successful_searches}/{num_queries}")
                print(f"   ⚡ 平均QPS: {avg_qps:.2f}")
                print(f"   ⏱️ 平均延迟: {avg_latency:.2f} ms")
                print(f"   📈 P99延迟: {p99_latency:.2f} ms") 
                print(f"   📋 平均返回结果数: {avg_results:.1f}")
                print(f"   🕒 总搜索时间: {search_duration:.2f} 秒")
                
                # 显示示例结果
                print(f"\n🔍 示例搜索结果:")
                for i in range(min(3, len(queries))):
                    try:
                        result = client.search_embedding(queries[i], k=5)
                        print(f"   查询 {i+1}: {result[:5]}")
                    except:
                        pass
            else:
                print("❌ 所有搜索都失败了")
            
            # 总结
            print(f"\n🎉 基准测试完成!")
            print("=" * 60)
            print(f"📋 测试总结:")
            print(f"   🏷️  配置: {index_type} 索引, {dim}维向量")
            print(f"   📊 数据量: {total_inserted:,} 个向量")
            print(f"   ⚡ 加载性能: {total_inserted / load_duration:.1f} 向量/秒")
            if successful_searches > 0:
                print(f"   🔍 搜索性能: {avg_qps:.2f} QPS")
                print(f"   ⏱️ 搜索延迟: {avg_latency:.2f} ms (avg), {p99_latency:.2f} ms (p99)")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simplified_benchmark_test()
