#!/bin/bash
echo "🧪 测试VectorDBBench并发参数传递"
echo "命令行参数: $@"

# 设置环境变量来追踪参数
export PYTHONPATH="/home/<USER>/VectorDBBench:$PYTHONPATH"

# 运行带调试的VectorDBBench
python3 -c "
import sys
print('Python参数:', sys.argv)
sys.path.append('/home/<USER>/VectorDBBench')

# 导入并检查配置
from vectordb_bench import config
print('默认并发配置:', config.NUM_CONCURRENCY)

# 模拟CLI调用
from vectordb_bench.cli.cli import click_arg_split
concurrency_str = '128,256,512'
parsed = click_arg_split(None, None, concurrency_str)
int_parsed = list(map(int, parsed))
print('解析的并发参数:', int_parsed)
" "$@"
