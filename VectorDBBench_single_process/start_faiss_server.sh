#!/bin/bash
"""
启动内存优化FAISS服务器脚本
使用单进程+OpenMP并行，避免内存爆炸
"""

echo "🚀 启动内存优化FAISS服务器..."
echo "💡 特性：单进程+OpenMP并行，避免内存爆炸"
echo "💡 使用预构建索引，避免加载时间过长"
echo ""

# 检查预构建索引文件
INDEX_FILE="/home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index"
if [ ! -f "$INDEX_FILE" ]; then
    echo "❌ 预构建索引文件不存在: $INDEX_FILE"
    echo "💡 请确保索引文件存在"
    exit 1
fi

echo "✅ 预构建索引文件存在: $(du -h $INDEX_FILE | cut -f1)"

# 设置参数
BATCH_SIZE=${BATCH_SIZE:-1000}
BASE_DELAY=${BASE_DELAY:-0.01}
MEMORY_THRESHOLD=${MEMORY_THRESHOLD:-75}
PORT=${PORT:-8005}

echo "📊 启动参数:"
echo "   批次大小: $BATCH_SIZE"
echo "   基础延迟: ${BASE_DELAY}s"
echo "   内存阈值: ${MEMORY_THRESHOLD}%"
echo "   端口: $PORT"
echo ""

# 启动服务器
echo "🔄 启动服务器..."
nohup python3 smart_faiss_server.py \
    --batch-size $BATCH_SIZE \
    --base-delay $BASE_DELAY \
    --memory-threshold $MEMORY_THRESHOLD \
    --port $PORT \
    > nohup.out 2>&1 &

SERVER_PID=$!
echo "✅ 服务器已启动，PID: $SERVER_PID"
echo "📁 日志文件: nohup.out"
echo "🌐 服务地址: http://localhost:$PORT"
echo ""

# 等待服务器启动
echo "⏳ 等待服务器启动..."
sleep 10

# 检查服务器状态
if curl -s http://localhost:$PORT/status > /dev/null; then
    echo "✅ 服务器启动成功!"
    echo "📊 状态检查:"
    curl -s http://localhost:$PORT/status | python3 -m json.tool
else
    echo "❌ 服务器启动失败，请检查日志: nohup.out"
    exit 1
fi

echo ""
echo "🎯 服务器已就绪，可以开始使用!"
echo "💡 停止服务器: kill $SERVER_PID"
echo "💡 查看日志: tail -f nohup.out"
