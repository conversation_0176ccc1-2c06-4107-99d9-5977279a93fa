#!/usr/bin/env python3
"""
使用pyarrow重新创建标准的parquet文件格式
"""

import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
import numpy as np
from pathlib import Path

def recreate_parquet_with_pyarrow():
    """使用pyarrow重新创建parquet文件确保格式正确"""
    
    dataset_dir = Path("/home/<USER>/VectorDBBench/dataset/openai/openai_small_50k")
    
    print("🔧 使用PyArrow重新创建parquet文件...")
    
    # 重新生成完全标准的数据集
    np.random.seed(42)
    
    # 1. 创建训练数据 shuffle_train.parquet
    print("📊 创建 shuffle_train.parquet...")
    train_size = 1000
    dim = 1536
    
    train_vectors = np.random.random((train_size, dim)).astype(np.float32)
    train_vectors = train_vectors / np.linalg.norm(train_vectors, axis=1, keepdims=True)
    
    train_table = pa.table({
        'id': pa.array(range(train_size)),
        'emb': pa.array([vec.tolist() for vec in train_vectors])
    })
    
    train_file = dataset_dir / "shuffle_train.parquet"
    pq.write_table(train_table, train_file)
    print(f"✅ {train_file.name}: {train_table.num_rows} 行")
    
    # 2. 创建测试数据 test.parquet
    print("📊 创建 test.parquet...")
    test_size = 100
    
    test_vectors = np.random.random((test_size, dim)).astype(np.float32)
    test_vectors = test_vectors / np.linalg.norm(test_vectors, axis=1, keepdims=True)
    
    test_table = pa.table({
        'id': pa.array(range(test_size)),
        'emb': pa.array([vec.tolist() for vec in test_vectors])
    })
    
    test_file = dataset_dir / "test.parquet"
    pq.write_table(test_table, test_file)
    print(f"✅ {test_file.name}: {test_table.num_rows} 行")
    
    # 3. 创建neighbors (ground truth)
    print("📊 创建 neighbors.parquet...")
    
    from sklearn.metrics.pairwise import cosine_similarity
    similarities = cosine_similarity(test_vectors, train_vectors)
    
    neighbors_data = []
    distances_data = []
    
    for i in range(test_size):
        top_k_indices = np.argsort(similarities[i])[-100:][::-1]
        top_k_distances = similarities[i][top_k_indices]
        neighbors_data.append(top_k_indices.tolist())
        distances_data.append(top_k_distances.tolist())
    
    neighbors_table = pa.table({
        'id': pa.array(range(test_size)),
        'neighbors_id': pa.array(neighbors_data),
        'distances': pa.array(distances_data)
    })
    
    neighbors_file = dataset_dir / "neighbors.parquet"
    pq.write_table(neighbors_table, neighbors_file)
    print(f"✅ {neighbors_file.name}: {neighbors_table.num_rows} 行")
    
    # 4. 创建scalar labels
    print("📊 创建 scalar_labels.parquet...")
    
    labels_table = pa.table({
        'id': pa.array(range(train_size)),
        'label': pa.array([f"category_{i % 10}" for i in range(train_size)])
    })
    
    labels_file = dataset_dir / "scalar_labels.parquet"
    pq.write_table(labels_table, labels_file)
    print(f"✅ {labels_file.name}: {labels_table.num_rows} 行")
    
    # 验证所有文件
    print("\n✅ 验证PyArrow生成的文件:")
    files = ["shuffle_train.parquet", "test.parquet", "neighbors.parquet", "scalar_labels.parquet"]
    
    for filename in files:
        filepath = dataset_dir / filename
        try:
            # 用pyarrow验证
            table = pq.read_table(filepath)
            # 用pandas验证
            df = pd.read_parquet(filepath)
            size = filepath.stat().st_size
            print(f"   ✅ {filename}: {table.num_rows} 行, {len(table.columns)} 列, {size:,} bytes")
            print(f"      列名: {table.column_names}")
        except Exception as e:
            print(f"   ❌ {filename}: 验证失败 - {e}")
    
    return dataset_dir

if __name__ == "__main__":
    print("🎯 使用PyArrow重新创建标准parquet文件")
    print("=" * 50)
    
    dataset_dir = recreate_parquet_with_pyarrow()
    
    print(f"\n🎊 PyArrow数据集创建完成！")
    print(f"📁 位置: {dataset_dir}")
    print(f"\n🚀 现在可以运行FAISS测试:")
    print(f"vectordbbench faisslocalhnsw --case-type Performance1536D50K --m 16 --ef-construction 200 --ef-search 100")
