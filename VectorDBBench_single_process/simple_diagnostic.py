#!/usr/bin/env python3
"""
简单诊断：为什么没有生成QPS、时延、召回率等结果
"""

import sys
import time
import requests
from pathlib import Path

def check_benchmark_execution_issue():
    """检查基准测试执行问题"""
    print("🔍 诊断基准测试结果问题")
    print("=" * 50)
    
    print("📋 从你的测试输出分析:")
    print("✅ 任务创建成功:")
    print("   • 任务 UUID: fa20baac100b48db8f04985a7dcb6e2c")
    print("   • 任务 UUID: 87fc9ef425ab434897737b887c6975e6") 
    print("   • 配置正确: host='127.0.0.1', port=8002")
    print("")
    
    print("❌ 问题现象:")
    print("   • 每个测试只用了 5 秒（预期应该是 60 秒）")
    print("   • 没有生成 QPS、时延、召回率数据")
    print("   • results/Faiss/ 目录为空")
    print("")
    
    print("🎯 可能的原因分析:")
    print("   1. 远程连接问题（虽然连接检查通过）")
    print("   2. 数据加载失败（导致快速结束）")
    print("   3. FAISS 服务器处理异常")
    print("   4. 任务配置问题")

def test_faiss_server_manually():
    """手动测试 FAISS 服务器"""
    print(f"\n🌐 手动测试 FAISS 服务器")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8002"
    
    try:
        # 1. 测试服务器状态
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器响应正常")
        else:
            print(f"⚠️  服务器响应异常: {response.status_code}")
        
        # 2. 测试创建索引 API
        create_data = {
            "index_type": "Flat",
            "dim": 1536
        }
        
        response = requests.post(f"{base_url}/create_index", json=create_data, timeout=10)
        print(f"📊 创建索引测试: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ 索引创建成功: {response.json()}")
        else:
            print(f"   ❌ 索引创建失败: {response.text}")
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 服务器测试失败: {e}")
        return False

def check_data_loading_issue():
    """检查数据加载问题"""
    print(f"\n📊 检查数据加载问题")
    print("=" * 50)
    
    # 检查数据集文件
    openai_datasets = [
        "dataset/openai",
        "dataset/openai_test"
    ]
    
    for dataset_path in openai_datasets:
        path = Path(dataset_path)
        if path.exists():
            print(f"✅ 数据集: {dataset_path}")
            
            # 检查关键文件
            required_files = ["train.parquet", "test.parquet", "neighbors.parquet"]
            for file_name in required_files:
                file_path = path / file_name
                if file_path.exists():
                    size = file_path.stat().st_size / (1024*1024)  # MB
                    print(f"   📄 {file_name}: {size:.1f}MB")
                    
                    # 检查 neighbors.parquet 大小
                    if file_name == "neighbors.parquet" and size < 0.1:
                        print(f"   ⚠️  {file_name} 太小，可能导致测试快速结束")
                else:
                    print(f"   ❌ 缺少: {file_name}")

def analyze_test_duration_issue():
    """分析测试持续时间问题"""
    print(f"\n⏱️  分析测试持续时间问题")
    print("=" * 50)
    
    print("🎯 配置分析:")
    print("   • 预期测试时间: 60 秒 (concurrency_duration=60)")
    print("   • 实际测试时间: 5 秒")
    print("   • 阶段配置: ['drop_old', 'load', 'search_serial', 'search_concurrent']")
    print("")
    
    print("🔍 可能原因:")
    print("   1. 数据加载阶段失败，导致提前结束")
    print("   2. 远程服务器连接在测试过程中断开")
    print("   3. 数据集太小，没有足够的向量进行测试")
    print("   4. FAISS 服务器处理异常，返回错误")
    print("")
    
    print("💡 解决建议:")
    print("   1. 检查 FAISS 服务器日志")
    print("   2. 使用更详细的日志输出")
    print("   3. 尝试只运行 load 阶段")
    print("   4. 检查数据集大小和质量")

def show_debugging_commands():
    """显示调试命令"""
    print(f"\n🔧 推荐的调试命令")
    print("=" * 50)
    
    print("1️⃣ 检查 FAISS 服务器日志:")
    print("   启动服务器时添加详细日志:")
    print("   ```bash")
    print("   python -m uvicorn vectordb_bench.backend.clients.faiss.server:app \\")
    print("       --host 0.0.0.0 --port 8002 --log-level debug")
    print("   ```")
    print("")
    
    print("2️⃣ 运行最小化测试:")
    print("   ```bash")
    print("   python run_faiss_benchmark_enhanced.py \\")
    print("       --mode remote \\")
    print("       --host 127.0.0.1 \\")
    print("       --port 8002 \\")
    print("       --index-type Flat \\")
    print("       --test-duration 10 \\")
    print("       --small-dataset")
    print("   ```")
    print("")
    
    print("3️⃣ 检查数据集:")
    print("   ```bash")
    print("   du -sh dataset/openai*/*")
    print("   ```")
    print("")
    
    print("4️⃣ 手动测试 API:")
    print("   ```bash")
    print("   curl -X POST http://127.0.0.1:8002/create_index \\")
    print("     -H 'Content-Type: application/json' \\")
    print("     -d '{\"index_type\": \"Flat\", \"dim\": 1536}'")
    print("   ```")

def main():
    print("🎯 基准测试结果问题诊断")
    print("=" * 60)
    
    # 问题分析
    check_benchmark_execution_issue()
    
    # 服务器测试
    server_ok = test_faiss_server_manually()
    
    # 数据检查
    check_data_loading_issue()
    
    # 时间分析
    analyze_test_duration_issue()
    
    # 调试建议
    show_debugging_commands()
    
    print(f"\n📋 总结:")
    print("=" * 15)
    print("🎯 核心问题：基准测试任务创建成功，但没有实际执行完整的性能测试")
    print("")
    print("✅ 已确认正常：")
    print("   • 远程 FAISS 连接功能")
    print("   • 任务配置和提交")
    print("   • 服务器 API 可用性")
    print("")
    print("❌ 需要解决：")
    print("   • 为什么测试只用了 5 秒而不是 60 秒")
    print("   • 为什么没有生成 QPS、时延、召回率数据")
    print("   • 数据加载和实际测试执行问题")
    print("")
    print("🔧 下一步：")
    print("   建议按照上面的调试命令逐步排查问题")

if __name__ == "__main__":
    main()
