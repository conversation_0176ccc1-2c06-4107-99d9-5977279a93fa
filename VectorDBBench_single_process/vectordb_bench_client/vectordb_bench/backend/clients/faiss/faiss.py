"""
简易 REST Faiss 客户端 —— 适配 VectorDBBench 的 VectorDB 抽象
"""

import requests
from contextlib import contextmanager
from typing import List, Tuple

from vectordb_bench.backend.clients.api import (
    VectorDB,
    DBConfig,
    DBCaseConfig,
    FilterOp,  # 添加FilterOp导入
)

# 避免循环引用，config 单独放在 faiss/config.py
from .config import FaissConfig, FaissDBCaseConfig


class FaissClient(VectorDB):
    """调用你写的 FastAPI 服务 (create_index / insert_bulk / search)"""

    supported_filter_types = [FilterOp.NonFilter]  # 修复：支持NonFilter

    def __init__(
        self,
        dim: int,
        db_config: DBConfig | dict,
        db_case_config: DBCaseConfig | None,
        collection_name: str = "faiss_collection",  # 提供默认值
        drop_old: bool = False,
        **kwargs,
    ) -> None:
        cfg = (
            db_config if isinstance(db_config, dict) else db_config.to_dict()
        )
        self.base_url: str = f"http://{cfg['host']}:{cfg['port']}"
        self.dim = dim
        self.index_type = cfg.get("index_type", "Flat")
        self.session = requests.Session()

        # 初始化索引
        resp = self.session.post(
            f"{self.base_url}/create_index",
            json={"dim": self.dim, "index_type": self.index_type},
            timeout=30,
        )
        resp.raise_for_status()

    # --------------------- lifecycle ---------------------
    @contextmanager
    def init(self):
        """VectorDBBench 在每个进程里都会 with obj.init()"""
        yield                              # 无长连接，可直接 yield

    # --------------------- CRUD --------------------------
    def insert_embeddings(
        self,
        embeddings: List[List[float]],
        metadata: List[int],
        labels_data: List[str] | None = None,
        **kwargs,
    ) -> Tuple[int, Exception | None]:
        resp = self.session.post(
            f"{self.base_url}/insert_bulk",
            json={"vectors": embeddings},
            timeout=120,
        )
        resp.raise_for_status()
        return len(embeddings), None

    def search_embedding(self, query: List[float], k: int = 100) -> List[int]:
        resp = self.session.post(
            f"{self.base_url}/search",
            json={"query": query, "topk": k},
            timeout=60,
        )
        resp.raise_for_status()
        return [int(x) for x in resp.json()["ids"][0]]

    # --------------------- optimize ----------------------
    def optimize(self, data_size: int | None = None):
        """FAISS (Flat / IVF) 本 demo 不做额外优化"""
        return

