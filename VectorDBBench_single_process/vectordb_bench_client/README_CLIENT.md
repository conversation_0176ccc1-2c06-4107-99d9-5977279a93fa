# VectorDBBench 客户端部署包

## 📋 概述
这个包包含了在客户机上进行远程FAISS基准测试所需的所有文件。

## 🔧 环境要求
- Python 3.8+
- 网络连接到FAISS服务器

## 📥 安装步骤

### 1. 解压部署包
```bash
tar -xzf vectordb_bench_client.tar.gz
cd vectordb_bench_client
```

### 2. 设置环境
```bash
chmod +x setup_client.sh
./setup_client.sh
```

### 3. 激活环境
```bash
source vdbench-client-env/bin/activate
```

## 🚀 使用方法

### 1. 配置远程服务器
修改测试脚本中的配置，指向远程FAISS服务器：

```python
from vectordb_bench.backend.clients.faiss.config import FaissConfig

db_config = FaissConfig(
    host='你的服务器IP',      # 例如: '*************'
    port=8002,               # FAISS服务器端口
    index_type='Flat',       # 索引类型
    db_label='remote_test'
)
```

### 2. 运行基准测试
```bash
python run_real_vectordb_benchmark.py
```

## 📊 测试结果
测试结果将保存在 `vectordb_bench/results/` 目录下。

## 🔍 故障排除
1. 检查网络连接到服务器
2. 确认服务器端口开放
3. 验证FAISS服务器正在运行
4. 查看客户端错误日志

## 📞 支持
如有问题，请检查服务器端日志和客户端错误信息。
