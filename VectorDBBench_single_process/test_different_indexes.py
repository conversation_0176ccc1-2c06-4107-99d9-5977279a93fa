#!/usr/bin/env python3
"""
测试不同FAISS索引类型的性能脚本
"""

import requests
import time
import json
from typing import Dict, List

class FAISSIndexTester:
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        
    def test_index_performance(self, index_configs: List[Dict]):
        """测试不同索引配置的性能"""
        results = []
        
        for config in index_configs:
            print(f"\n🧪 测试索引配置: {config['name']}")
            print(f"   类型: {config['index_type']}")
            print(f"   参数: {config.get('params', {})}")
            
            try:
                # 创建索引
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/create_index_advanced",
                    json={
                        "case_type": "Performance768D10M",
                        "index_type": config["index_type"],
                        "index_params": config.get("params", {})
                    },
                    timeout=600  # 10分钟超时
                )
                
                if response.status_code == 200:
                    build_time = time.time() - start_time
                    result = response.json()
                    
                    print(f"   ✅ 索引构建成功")
                    print(f"   📊 构建时间: {build_time:.2f}秒")
                    print(f"   📈 向量数量: {result.get('total_vectors', 'N/A')}")
                    
                    # 进行搜索性能测试
                    search_result = self.test_search_performance(config["index_type"])
                    
                    results.append({
                        "config": config,
                        "build_time": build_time,
                        "search_performance": search_result,
                        "status": "success"
                    })
                else:
                    print(f"   ❌ 索引构建失败: {response.text}")
                    results.append({
                        "config": config,
                        "status": "failed",
                        "error": response.text
                    })
                    
            except Exception as e:
                print(f"   ❌ 测试失败: {str(e)}")
                results.append({
                    "config": config,
                    "status": "error", 
                    "error": str(e)
                })
                
        return results
    
    def test_search_performance(self, index_type: str, num_queries: int = 100):
        """测试搜索性能"""
        print(f"   🔍 测试搜索性能 ({num_queries}次查询)")
        
        # 获取测试查询向量
        try:
            status_resp = requests.get(f"{self.base_url}/status")
            if status_resp.status_code != 200:
                return {"error": "无法获取状态"}
            
            # 模拟查询向量 (768维)
            import random
            query_vector = [random.random() for _ in range(768)]
            
            # 测试多次查询
            start_time = time.time()
            success_count = 0
            
            for i in range(num_queries):
                try:
                    search_resp = requests.post(
                        f"{self.base_url}/search",
                        json={"query": query_vector, "topk": 100},
                        timeout=10
                    )
                    if search_resp.status_code == 200:
                        success_count += 1
                except:
                    pass
            
            total_time = time.time() - start_time
            avg_latency = total_time / num_queries if num_queries > 0 else 0
            qps = success_count / total_time if total_time > 0 else 0
            
            print(f"   📊 平均延迟: {avg_latency*1000:.2f}ms")
            print(f"   📊 QPS: {qps:.2f}")
            print(f"   📊 成功率: {success_count}/{num_queries} ({100*success_count/num_queries:.1f}%)")
            
            return {
                "avg_latency_ms": avg_latency * 1000,
                "qps": qps,
                "success_rate": success_count / num_queries,
                "total_queries": num_queries
            }
            
        except Exception as e:
            return {"error": str(e)}

def main():
    """主函数"""
    tester = FAISSIndexTester()
    
    # 定义要测试的索引配置
    index_configs = [
        {
            "name": "精确搜索 (当前默认)",
            "index_type": "Flat",
            "params": {}
        },
        {
            "name": "IVF4096 - 推荐配置",
            "index_type": "IVF4096", 
            "params": {"nlist": 4096}
        },
        {
            "name": "IVF8192 - 高精度配置",
            "index_type": "IVF8192",
            "params": {"nlist": 8192}
        },
        {
            "name": "HNSW - 高性能配置",
            "index_type": "HNSW",
            "params": {"m": 32, "ef_construction": 400}
        }
    ]
    
    print("🚀 开始FAISS索引性能测试")
    print("="*60)
    
    # 运行测试
    results = tester.test_index_performance(index_configs)
    
    # 输出总结报告
    print("\n" + "="*60)
    print("📋 测试总结报告")
    print("="*60)
    
    for result in results:
        config = result["config"]
        print(f"\n📊 {config['name']}")
        print(f"   索引类型: {config['index_type']}")
        
        if result["status"] == "success":
            print(f"   构建时间: {result['build_time']:.2f}秒")
            search = result.get("search_performance", {})
            if "avg_latency_ms" in search:
                print(f"   平均延迟: {search['avg_latency_ms']:.2f}ms")
                print(f"   QPS: {search['qps']:.2f}")
                print(f"   成功率: {search['success_rate']*100:.1f}%")
        else:
            print(f"   状态: {result['status']}")
            if "error" in result:
                print(f"   错误: {result['error']}")
    
    # 保存结果到文件
    with open("/home/<USER>/VectorDBBench/index_performance_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: index_performance_results.json")

if __name__ == "__main__":
    main()
