nohup: ignoring input
/home/<USER>/VectorDBBench/smart_faiss_server.py:35: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:__main__:🧵 使用同步模型 (无线程池) - 避免内存不足问题
INFO:resource_manager:Applying resource limits: 16 CPU cores, 64.0GB RAM
INFO:resource_manager:CPU affinity set to cores: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
INFO:resource_manager:Memory limit set to 64.0GB
INFO:__main__:✅ 资源限制已应用: 16核心, 64GB内存
INFO:__main__:🧵 FAISS OpenMP线程数设置为: 2
[2025-07-26 22:35:07 +0800] [187805] [INFO] Starting gunicorn 23.0.0
[2025-07-26 22:35:07 +0800] [187805] [INFO] Listening at: http://0.0.0.0:8005 (187805)
[2025-07-26 22:35:07 +0800] [187805] [INFO] Using worker: uvicorn.workers.UvicornWorker
[2025-07-26 22:35:07 +0800] [188310] [INFO] Booting worker with pid: 188310
[2025-07-26 22:35:07 +0800] [188310] [INFO] Started server process [188310]
[2025-07-26 22:35:07 +0800] [188310] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
ERROR:__main__:❌ 预加载失败: Performance768D1M - name 'time' is not defined
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  没有预加载任何索引，将使用动态加载模式
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:35:07 +0800] [188310] [INFO] Application startup complete.
[2025-07-26 22:35:07 +0800] [188400] [INFO] Booting worker with pid: 188400
[2025-07-26 22:35:07 +0800] [188400] [INFO] Started server process [188400]
[2025-07-26 22:35:07 +0800] [188400] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
ERROR:__main__:❌ 预加载失败: Performance768D1M - name 'time' is not defined
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  没有预加载任何索引，将使用动态加载模式
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:35:07 +0800] [188400] [INFO] Application startup complete.
[2025-07-26 22:35:07 +0800] [188414] [INFO] Booting worker with pid: 188414
[2025-07-26 22:35:07 +0800] [188414] [INFO] Started server process [188414]
[2025-07-26 22:35:07 +0800] [188414] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
ERROR:__main__:❌ 预加载失败: Performance768D1M - name 'time' is not defined
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  没有预加载任何索引，将使用动态加载模式
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:35:07 +0800] [188414] [INFO] Application startup complete.
[2025-07-26 22:35:07 +0800] [188440] [INFO] Booting worker with pid: 188440
[2025-07-26 22:35:07 +0800] [188440] [INFO] Started server process [188440]
[2025-07-26 22:35:07 +0800] [188440] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
ERROR:__main__:❌ 预加载失败: Performance768D1M - name 'time' is not defined
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  没有预加载任何索引，将使用动态加载模式
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:35:07 +0800] [188440] [INFO] Application startup complete.
[2025-07-26 22:35:07 +0800] [188444] [INFO] Booting worker with pid: 188444
[2025-07-26 22:35:07 +0800] [188444] [INFO] Started server process [188444]
[2025-07-26 22:35:07 +0800] [188444] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
ERROR:__main__:❌ 预加载失败: Performance768D1M - name 'time' is not defined
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  没有预加载任何索引，将使用动态加载模式
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:35:07 +0800] [188444] [INFO] Application startup complete.
[2025-07-26 22:35:08 +0800] [188502] [INFO] Booting worker with pid: 188502
[2025-07-26 22:35:08 +0800] [188502] [INFO] Started server process [188502]
[2025-07-26 22:35:08 +0800] [188502] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
ERROR:__main__:❌ 预加载失败: Performance768D1M - name 'time' is not defined
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  没有预加载任何索引，将使用动态加载模式
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:35:08 +0800] [188502] [INFO] Application startup complete.
[2025-07-26 22:35:08 +0800] [188669] [INFO] Booting worker with pid: 188669
[2025-07-26 22:35:08 +0800] [188669] [INFO] Started server process [188669]
[2025-07-26 22:35:08 +0800] [188669] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
ERROR:__main__:❌ 预加载失败: Performance768D1M - name 'time' is not defined
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  没有预加载任何索引，将使用动态加载模式
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:35:08 +0800] [188669] [INFO] Application startup complete.
[2025-07-26 22:35:08 +0800] [188683] [INFO] Booting worker with pid: 188683
[2025-07-26 22:35:08 +0800] [188683] [INFO] Started server process [188683]
[2025-07-26 22:35:08 +0800] [188683] [INFO] Waiting for application startup.
INFO:__main__:🚀 应用启动，开始预加载索引...
INFO:__main__:🔄 预加载索引: Performance768D1M
ERROR:__main__:❌ 预加载失败: Performance768D1M - name 'time' is not defined
INFO:__main__:⚠️  索引文件不存在: /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
INFO:__main__:⚠️  没有预加载任何索引，将使用动态加载模式
INFO:__main__:✅ 应用启动完成
[2025-07-26 22:35:08 +0800] [188683] [INFO] Application startup complete.
[2025-07-26 22:36:03 +0800] [187805] [INFO] Handling signal: term
[2025-07-26 22:36:03 +0800] [188669] [INFO] Shutting down
[2025-07-26 22:36:03 +0800] [188440] [INFO] Shutting down
[2025-07-26 22:36:03 +0800] [188414] [INFO] Shutting down
[2025-07-26 22:36:03 +0800] [188502] [INFO] Shutting down
[2025-07-26 22:36:03 +0800] [188400] [INFO] Shutting down
[2025-07-26 22:36:03 +0800] [188444] [INFO] Shutting down
[2025-07-26 22:36:03 +0800] [188683] [INFO] Shutting down
[2025-07-26 22:36:03 +0800] [188310] [INFO] Shutting down
[2025-07-26 22:36:03 +0800] [188669] [INFO] Waiting for application shutdown.
[2025-07-26 22:36:03 +0800] [188669] [INFO] Application shutdown complete.
[2025-07-26 22:36:03 +0800] [188669] [INFO] Finished server process [188669]
[2025-07-26 22:36:03 +0800] [188440] [INFO] Waiting for application shutdown.
[2025-07-26 22:36:03 +0800] [188440] [INFO] Application shutdown complete.
[2025-07-26 22:36:03 +0800] [188440] [INFO] Finished server process [188440]
[2025-07-26 22:36:03 +0800] [187805] [ERROR] Worker (pid:188669) was sent SIGTERM!
[2025-07-26 22:36:03 +0800] [187805] [ERROR] Worker (pid:188440) was sent SIGTERM!
[2025-07-26 22:36:03 +0800] [188414] [INFO] Waiting for application shutdown.
[2025-07-26 22:36:03 +0800] [188414] [INFO] Application shutdown complete.
[2025-07-26 22:36:03 +0800] [188414] [INFO] Finished server process [188414]
[2025-07-26 22:36:03 +0800] [188502] [INFO] Waiting for application shutdown.
[2025-07-26 22:36:03 +0800] [188502] [INFO] Application shutdown complete.
[2025-07-26 22:36:03 +0800] [188502] [INFO] Finished server process [188502]
[2025-07-26 22:36:03 +0800] [187805] [ERROR] Worker (pid:188414) was sent SIGTERM!
[2025-07-26 22:36:03 +0800] [187805] [ERROR] Worker (pid:188502) was sent SIGTERM!
[2025-07-26 22:36:03 +0800] [188400] [INFO] Waiting for application shutdown.
[2025-07-26 22:36:03 +0800] [188400] [INFO] Application shutdown complete.
[2025-07-26 22:36:03 +0800] [188400] [INFO] Finished server process [188400]
[2025-07-26 22:36:03 +0800] [188444] [INFO] Waiting for application shutdown.
[2025-07-26 22:36:03 +0800] [188444] [INFO] Application shutdown complete.
[2025-07-26 22:36:03 +0800] [188444] [INFO] Finished server process [188444]
[2025-07-26 22:36:03 +0800] [187805] [ERROR] Worker (pid:188400) was sent SIGTERM!
[2025-07-26 22:36:03 +0800] [187805] [ERROR] Worker (pid:188444) was sent SIGTERM!
[2025-07-26 22:36:03 +0800] [188683] [INFO] Waiting for application shutdown.
[2025-07-26 22:36:03 +0800] [188683] [INFO] Application shutdown complete.
[2025-07-26 22:36:03 +0800] [188683] [INFO] Finished server process [188683]
[2025-07-26 22:36:03 +0800] [187805] [ERROR] Worker (pid:188683) was sent SIGTERM!
[2025-07-26 22:36:03 +0800] [188310] [INFO] Waiting for application shutdown.
[2025-07-26 22:36:03 +0800] [188310] [INFO] Application shutdown complete.
[2025-07-26 22:36:03 +0800] [188310] [INFO] Finished server process [188310]
[2025-07-26 22:36:03 +0800] [187805] [ERROR] Worker (pid:188310) was sent SIGTERM!
[2025-07-26 22:36:03 +0800] [187805] [INFO] Shutting down: Master
🚀 启动智能FAISS服务器...
============================================================
💡 特性：使用真实数据集，避免重复下载
🌐 服务器地址: http://0.0.0.0:8005
� 数据集路径: /nas/yvan.chen/milvus/dataset
🚀 启动模式: Gunicorn 多进程 (8 workers)
🔑 预加载模式: 启用
💾 内存优化: Copy-on-Write 共享
�📊 可用数据集:
   ✅ Performance768D1M: 1,000,000 向量, 768维
   ✅ Performance768D10M: 10,000,000 向量, 768维
   ✅ Performance1536D50K: 50,000 向量, 1536维
   ✅ Performance1536D500K: 500,000 向量, 1536维
   ✅ Performance1536D5M: 5,000,000 向量, 1536维
⚡ 智能缓存: 自动跳过重复插入
🔧 资源限制: 16核心64GB内存

⚡ FAISS多进程优化:
   Worker进程数: 8
   每Worker线程数: 2
   总线程数: 8 × 2 = 16
🚀 启动 Gunicorn 多进程FAISS服务器:
   监听地址: 0.0.0.0:8005
   Worker进程数: 8
   预加载模式: 启用
   内存优化: Copy-on-Write 共享

