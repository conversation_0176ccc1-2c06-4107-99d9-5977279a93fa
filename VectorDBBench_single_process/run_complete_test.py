#!/usr/bin/env python3

import os
import sys
import json
import logging
from pathlib import Path

# 设置环境变量
os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'

# 添加项目路径
sys.path.insert(0, '/home/<USER>/VectorDBBench')

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s: %(message)s (%(filename)s:%(lineno)d)',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/home/<USER>/VectorDBBench/logs/complete_test.log')
    ]
)

print("=== 完整的 FAISS 基准测试 ===")

try:
    from vectordb_bench.cli.cli import run
    from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
    from vectordb_bench.backend.task_runner import TaskConfig
    from vectordb_bench.backend.cases import Performance1536D50K, CaseType
    from vectordb_bench import config
    from vectordb_bench.models import DB, CaseConfig
    from vectordb_bench.backend.assembler import Assembler
    from vectordb_bench.backend.data_source import DatasetSource
    
    print("正在设置测试配置...")
    
    # 创建配置
    db_config = FaissLocalConfig(
        db_label="faiss_detailed_test",
        index_type="HNSW"
    )
    
    db_case_config = HNSWConfig(
        m=16,
        ef_construction=200,
        ef_search=64
    )
    
    case_config = CaseConfig(
        case_id=CaseType.Performance1536D50K,
        custom_case={},
        k=10
    )
    
    task_config = TaskConfig(
        db=DB.FaissLocal,
        db_config=db_config,
        db_case_config=db_case_config,
        case_config=case_config,
        stages=['drop_old', 'load', 'search_serial']  # 简化测试，跳过并发搜索
    )
    
    print("正在初始化数据源...")
    data_source = DatasetSource()
    
    print("正在组装运行器...")
    runner = Assembler.assemble("test_run_001", task_config, data_source)
    
    print("开始运行测试...")
    print("=" * 50)
    
    # 运行测试
    result = runner.run(drop_old=True)
    
    print("=" * 50)
    print("测试完成！")
    print(f"结果: {result}")
    
    # 保存结果
    if result:
        results_dir = Path("/home/<USER>/VectorDBBench/results")
        results_dir.mkdir(exist_ok=True)
        
        result_file = results_dir / "faiss_test_result.json"
        with result_file.open('w') as f:
            # 将结果转换为可序列化的格式
            result_dict = {
                'test_name': 'FAISS HNSW Performance Test',
                'dataset': 'OpenAI 50K 1536D',
                'parameters': {
                    'm': 16,
                    'ef_construction': 200,
                    'ef_search': 64,
                    'k': 10
                },
                'metrics': str(result)  # 将结果转换为字符串以便查看
            }
            json.dump(result_dict, f, indent=2, default=str)
            
        print(f"结果已保存到: {result_file}")
    
except Exception as e:
    print(f"测试过程中出现错误: {e}")
    import traceback
    traceback.print_exc()
