#!/usr/bin/env python3
"""
内存优化FAISS服务器 - 单进程+OpenMP并行，避免内存爆炸
使用预构建索引文件，避免加载时间过长
"""

import os
import sys
import time
import argparse
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional
import json
import logging
from datetime import datetime
import psutil
import gc  # 垃圾回收

try:
    from fastapi import FastAPI, HTTPException, Request
    from pydantic import BaseModel
    import faiss
except ImportError as e:
    print(f"❌ 依赖缺失: {e}")
    print("💡 请安装: pip install fastapi uvicorn faiss-cpu pandas")
    sys.exit(1)

# 配置日志 - 同时输出到控制台和文件
log_file = "/home/<USER>/VectorDBBench/faiss_server.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, mode='a', encoding='utf-8'),
        logging.StreamHandler()  # 同时输出到控制台
    ]
)
logger = logging.getLogger(__name__)

# 记录启动信息
logger.info("🚀 FAISS服务器启动，日志文件: " + log_file)

app = FastAPI(title="Smart FAISS Server", version="3.0.0")

def warmup_index_cache(index, dimension):
    """
    预热索引缓存，提高后续搜索性能
    通过执行一系列随机查询，将索引数据预加载到CPU缓存中
    """
    logger.info("🔥 开始预热索引缓存...")
    try:
        # 生成随机查询进行预热
        warmup_queries = np.random.random((1000, dimension)).astype('float32')
        
        # 分批预热，使用多种批次大小，模拟真实负载
        batch_sizes = [1, 8, 32, 64, 128, 256]
        for batch_size in batch_sizes:
            start_time = time.time()
            # 使用不同的topk值
            for topk in [10, 50, 100]:
                # 分批预热
                for i in range(0, 1000, batch_size):
                    end_idx = min(i + batch_size, 1000)
                    batch = warmup_queries[i:end_idx]
                    index.search(batch, topk)
            
            elapsed = time.time() - start_time
            logger.info(f"✅ 完成批次大小 {batch_size} 的预热, 耗时: {elapsed:.2f}秒")
        
        logger.info("🚀 索引缓存预热完成，搜索性能已优化")
        return True
    except Exception as e:
        logger.error(f"❌ 索引预热失败: {e}")
        return False

# 🔑 应用启动时预加载索引 (父进程执行)
@app.on_event("startup")
async def startup_event():
    """应用启动时预加载常用索引"""
    logger.info("🚀 应用启动，开始预加载索引...")
    preload_common_indexes()
    
    # 预热索引缓存，提高后续搜索性能
    if server_state["current_index"] is not None:
        dimension = server_state["server_status"]["dimension"]
        warmup_index_cache(server_state["current_index"], dimension)
    
    logger.info("✅ 应用启动完成")

# 数据集配置 - 使用你的真实数据集
DATASET_BASE_PATH = "/nas/yvan.chen/milvus/dataset"

# 数据集配置 - 根据实际数据集情况修正
DATASET_MAPPING = {
    "Performance768D1M": {"dataset": "cohere", "subset": "cohere_medium_1m", "dimension": 768, "target_count": 1000000, "path": "cohere/cohere_medium_1m", "vectors": 1000000},
    "Performance768D10M": {"dataset": "cohere", "subset": "cohere_large_10m", "dimension": 768, "target_count": 10000000, "path": "cohere/cohere_large_10m", "vectors": 10000000},
    "Performance1536D50K": {"dataset": "openai", "subset": "openai_small_50k", "dimension": 1536, "target_count": 50000, "path": "openai/openai_small_50k", "vectors": 50000},
    "Performance1536D500K": {"dataset": "openai", "subset": "openai_medium_500k", "dimension": 1536, "target_count": 500000, "path": "openai/openai_medium_500k", "vectors": 500000},
    "Performance1536D5M": {"dataset": "openai", "subset": "openai_large_5m", "dimension": 1536, "target_count": 5000000, "path": "openai/openai_large_5m", "vectors": 5000000},
}

# 全局状态 - 预加载真实数据集
server_state = {
    "current_index": None,
    "loaded_dataset": None,
    "server_status": {
        "status": "ready",
        "total_vectors": 0,
        "vectors_count": 0,
        "vectors_loaded": 0,
        "index_type": "HNSW",
        "dimension": 768
    }
}

# 🚦 流量控制配置 - 高性能优化
FLOW_CONTROL_CONFIG = {
    "batch_size": 10000,          # 批次大小 (增加到10K，提高吞吐量)
    "base_delay": 0.001,          # 基础延迟(秒) (减少到0.001s)
    "memory_threshold": 85,       # 内存使用阈值(%) (提高到85%)
    "adaptive_enabled": False,    # 禁用自适应调整，避免不必要的延迟
}

def get_system_resources():
    """获取系统资源使用情况"""
    try:
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk_io = psutil.disk_io_counters()

        return {
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": memory.available / (1024**3),
            "disk_read_mb_s": getattr(disk_io, 'read_bytes', 0) / (1024**2),
            "disk_write_mb_s": getattr(disk_io, 'write_bytes', 0) / (1024**2),
        }
    except Exception as e:
        logger.warning(f"⚠️ 无法获取系统资源信息: {e}")
        return {"cpu_percent": 0, "memory_percent": 0, "memory_available_gb": 0}

def calculate_adaptive_delay(resources, base_delay=0.01):
    """根据系统资源动态计算延迟 - 内存优先"""
    if not FLOW_CONTROL_CONFIG["adaptive_enabled"]:
        return base_delay

    memory_factor = 1.0
    cpu_factor = 1.0

    # 内存压力调整 - 更激进的内存保护
    if resources["memory_percent"] > FLOW_CONTROL_CONFIG["memory_threshold"]:
        memory_factor = 1 + (resources["memory_percent"] - FLOW_CONTROL_CONFIG["memory_threshold"]) / 10

        # 内存接近阈值时，强制垃圾回收
        if resources["memory_percent"] > FLOW_CONTROL_CONFIG["memory_threshold"] + 10:
            gc.collect()

    # CPU压力调整 - 次要因素
    if resources["cpu_percent"] > 90:  # 提高CPU阈值
        cpu_factor = 1 + (resources["cpu_percent"] - 90) / 10

    # 计算最终延迟 - 内存因素权重更高
    adaptive_delay = base_delay * (memory_factor * 1.5 + cpu_factor * 0.5) / 2
    return min(adaptive_delay, 1.0)  # 最大延迟1秒

# 🔑 预加载索引缓存 (父进程加载，子进程共享内存)
PRELOADED_INDEXES = {}
PREBUILT_INDEX_PATH = "/home/<USER>/VectorDBBench/prebuilt_indexes"

# 🚀 内存优化并发模型 - 单进程+OpenMP
# 策略：禁用多进程，使用OpenMP并行计算
# 问题：Gunicorn多进程 + 大索引 = 内存爆炸 (每进程都加载索引)
# 解决：单进程 + OpenMP线程并行 + 预构建索引
logger.info(f"🧵 使用单进程+OpenMP并行模型 - 避免内存爆炸")

def configure_openmp_threads(args, cpu_count):
    """
    🔧 配置OpenMP线程数 - 单进程优化模式
    """
    print(f"🔧 配置OpenMP并行 (CPU核数: {cpu_count})")

    # 强制禁用多进程模式，避免内存爆炸
    args.use_gunicorn = False
    args.workers = 1

    # 设置OpenMP线程数
    if args.omp_threads is None:
        # 自动配置：使用所有CPU核心
        args.omp_threads = cpu_count
        
    # 计算最佳OpenMP线程数 - 一般为可用核心数的75%
    optimal_threads = max(int(cpu_count * 0.75), 1)
    print(f"💡 计算得到最佳OpenMP线程数: {optimal_threads} (CPU核心数的75%)")
        
    # 当未指定线程数时，使用最优配置
    if args.omp_threads is None:
        args.omp_threads = optimal_threads
        print(f"💡 自动设置OpenMP线程数为最优值: {args.omp_threads}")

    # 限制最大线程数，避免过度并行
    max_threads = min(cpu_count, 128)  # 最多支持128线程
    if args.omp_threads > max_threads:
        print(f"⚠️  线程数过多，限制为 {max_threads}")
        args.omp_threads = max_threads

    print("📊 最终并发配置:")
    print(f"   模式: 单进程 + OpenMP并行")
    print(f"   OpenMP线程数: {args.omp_threads}")
    print(f"   内存优化: 避免多进程重复加载索引")
    
    # 设置OpenMP环境变量，调整并行策略
    os.environ['OMP_WAIT_POLICY'] = 'ACTIVE'  # 活跃等待，减少延迟
    os.environ['OMP_DYNAMIC'] = 'true'        # 允许动态调整线程数
    os.environ['OMP_NESTED'] = 'true'         # 允许嵌套并行
    os.environ['OMP_PROC_BIND'] = 'close'     # 将线程绑定到相邻核心

    return args

def preload_common_indexes():
    """
    🔑 预加载预构建索引到内存 - 单进程模式，避免内存爆炸
    直接使用 /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
    """
    global PRELOADED_INDEXES

    # 检查预构建索引目录
    if not os.path.exists(PREBUILT_INDEX_PATH):
        logger.info(f"📁 预构建索引目录不存在: {PREBUILT_INDEX_PATH}")
        return

    # 直接加载10M向量的预构建索引
    target_index_file = "faiss_hnsw_768d_10m.index"
    index_path = os.path.join(PREBUILT_INDEX_PATH, target_index_file)

    if os.path.exists(index_path):
        try:
            logger.info(f"🔄 加载预构建索引: {target_index_file}")
            start_time = time.time()

            # 加载预构建索引
            index = faiss.read_index(index_path)
            load_time = time.time() - start_time

            # 缓存到内存
            PRELOADED_INDEXES["Performance768D10M"] = {
                "index": index,
                "dimension": index.d,
                "total_vectors": index.ntotal,
                "index_type": "HNSW",
                "load_time": load_time
            }

            file_size = os.path.getsize(index_path) / (1024**3)  # GB
            logger.info(f"✅ 预构建索引加载完成:")
            logger.info(f"   向量数量: {index.ntotal:,}")
            logger.info(f"   维度: {index.d}")
            logger.info(f"   文件大小: {file_size:.2f} GB")
            logger.info(f"   加载时间: {load_time:.2f} 秒")

            # 设置为当前索引
            server_state["current_index"] = index
            server_state["loaded_dataset"] = "Performance768D10M"
            server_state["server_status"].update({
                "status": "ready",
                "total_vectors": index.ntotal,
                "vectors_count": index.ntotal,
                "vectors_loaded": index.ntotal,
                "index_type": "HNSW",
                "dimension": index.d
            })

        except Exception as e:
            logger.error(f"❌ 预构建索引加载失败: {e}")
    else:
        logger.error(f"❌ 预构建索引文件不存在: {index_path}")
        logger.info("💡 请确保文件存在或重新生成索引")

    if PRELOADED_INDEXES:
        total_memory = sum(
            idx_info["index"].ntotal * idx_info["dimension"] * 4 / (1024**3)
            for idx_info in PRELOADED_INDEXES.values()
        )
        logger.info(f"🎯 索引加载完成，内存使用约: {total_memory:.2f} GB")
        logger.info(f"💡 单进程模式，无内存重复占用问题")
    else:
        logger.warning(f"⚠️  没有加载任何索引，服务可能无法正常工作")

class LegacyCreateIndexRequest(BaseModel):
    dim: int
    index_type: str
    m: Optional[int] = 30  # 修改为你要求的值
    ef_construction: Optional[int] = 360  # 修改为你要求的值
    batch_limit: Optional[int] = None  # 批次限制，None表示加载全部数据
    incremental: Optional[bool] = False  # 是否增量添加到现有索引

class InsertRequest(BaseModel):
    vectors: List[List[float]]

class SearchRequest(BaseModel):
    """搜索请求"""
    query: List[float]
    topk: int
    index_name: Optional[str] = None

class SearchRequestLegacy(BaseModel):
    query: List[float]
    topk: int = 100

class SearchResponseLegacy(BaseModel):
    ids: List[List[int]]
    distances: List[List[float]]

class LoadRequest(BaseModel):
    """加载请求 - 兼容VectorDBBench"""
    case_config: Dict

@app.get("/")
async def root():
    return {"message": "Smart FAISS Server", "status": "running", "version": "3.0.0"}

@app.post("/load")
async def load_dataset(request: LoadRequest):
    """加载数据集 - VectorDBBench兼容接口"""
    try:
        case_config = request.case_config
        logger.info(f"🔄 加载请求: {case_config}")
        
        # 提取case信息
        custom_case = case_config.get("custom_case", {})
        case_id = custom_case.get("case_id", "")
        dataset = custom_case.get("dataset", "")
        
        logger.info(f"📊 Case ID: {case_id}, Dataset: {dataset}")
        
        # 检查是否是支持的数据集
        if case_id not in DATASET_MAPPING:
            logger.error(f"❌ 不支持的数据集: {case_id}")
            raise HTTPException(status_code=400, detail=f"不支持的数据集: {case_id}")
        
        dataset_info = DATASET_MAPPING[case_id]
        dataset_path = Path(DATASET_BASE_PATH) / dataset_info["path"]
        
        if not dataset_path.exists():
            logger.error(f"❌ 数据集路径不存在: {dataset_path}")
            raise HTTPException(status_code=404, detail=f"数据集路径不存在: {dataset_path}")
        
        # 创建索引
        dim = dataset_info["dimension"]
        logger.info(f"🔧 创建{dim}维HNSW索引...")

        index = faiss.IndexHNSWFlat(dim, 30)  # 使用客户端兼容的默认值
        index.hnsw.efConstruction = 360  # 使用客户端兼容的默认值
        
        # 加载数据集
        logger.info(f"📁 加载数据集: {dataset_info['path']}")
        train_files = list(dataset_path.glob("*train*.parquet"))
        
        if not train_files:
            logger.error(f"❌ 未找到训练文件: {dataset_path}")
            raise HTTPException(status_code=404, detail=f"未找到训练文件: {dataset_path}")
        
        logger.info(f"📊 找到 {len(train_files)} 个训练文件")
        
        total_loaded = 0
        for i, train_file in enumerate(train_files):
            logger.info(f"📄 [{i+1}/{len(train_files)}] 读取: {train_file.name}")
            df = pd.read_parquet(train_file)
            
            # 提取向量数据
            if 'emb' in df.columns:
                vectors = np.vstack(df['emb'].values).astype('float32')
            elif 'embedding' in df.columns:
                vectors = np.vstack(df['embedding'].values).astype('float32')
            else:
                # 假设数值列都是向量维度
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                vectors = df[numeric_cols].values.astype('float32')
            
            # 批量添加到索引
            batch_size = 10000
            for j in range(0, len(vectors), batch_size):
                batch = vectors[j:j+batch_size]
                index.add(batch)
                total_loaded += len(batch)
                
                if total_loaded % 100000 == 0:
                    logger.info(f"📊 已加载 {total_loaded:,} 个向量")
        
        # 更新全局状态
        server_state["current_index"] = index
        server_state["loaded_dataset"] = dataset_info["path"]
        server_state["server_status"].update({
            "status": "loaded",
            "dimension": dim,
            "index_type": "HNSW",
            "total_vectors": index.ntotal,
            "vectors_count": index.ntotal,
            "vectors_loaded": index.ntotal
        })
        
        logger.info(f"✅ 数据集加载完成: {total_loaded:,} 个向量")
        
        return {
            "success": True,
            "message": f"数据集加载成功: {case_id}",
            "dataset": dataset_info["path"],
            "vectors_loaded": total_loaded,
            "dimension": dim,
            "index_type": "HNSW"
        }
        
    except Exception as e:
        logger.error(f"❌ 数据集加载失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据集加载失败: {e}")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/create_index")
async def create_index_smart(request: LegacyCreateIndexRequest):
    """创建索引并预加载真实数据集"""
    try:
        dim = request.dim
        index_type = request.index_type

        if request.batch_limit:
            logger.info(f"🚦 分批创建索引: 维度={dim}, 类型={index_type}, 批次限制={request.batch_limit:,}")
        elif request.incremental:
            logger.info(f"🔄 增量创建索引: 维度={dim}, 类型={index_type}")
        else:
            logger.info(f"🏗️ 创建索引: 维度={dim}, 类型={index_type}")
        
        # 创建索引
        if index_type.upper() == "HNSW":
            m = request.m if request.m else 30  # 客户端兼容的默认值
            ef_construction = request.ef_construction if request.ef_construction else 360  # 客户端兼容的默认值

            index = faiss.IndexHNSWFlat(dim, m)
            index.hnsw.efConstruction = ef_construction
            logger.info(f"HNSW参数: M={m}, efConstruction={ef_construction}")
        elif index_type.upper() == "FLAT":
            index = faiss.IndexFlatL2(dim)
        else:
            index = faiss.IndexFlatL2(dim)
        
        # 🔑 优先使用预加载的索引 (避免重复加载)
        logger.info("🔍 检查预加载索引...")

        # 查找匹配的预加载索引
        suitable_preloaded = None
        for case_name, idx_info in PRELOADED_INDEXES.items():
            if idx_info["dimension"] == dim and idx_info["index_type"] == index_type.upper():
                suitable_preloaded = case_name
                break

        if suitable_preloaded:
            # 🎯 使用预加载索引 (共享内存，零拷贝)
            logger.info(f"✅ 使用预加载索引: {suitable_preloaded}")
            idx_info = PRELOADED_INDEXES[suitable_preloaded]

            # 直接使用预加载的索引 (共享内存)
            server_state["current_index"] = idx_info["index"]
            server_state["loaded_dataset"] = suitable_preloaded
            server_state["server_status"].update({
                "total_vectors": idx_info["total_vectors"],
                "vectors_count": idx_info["total_vectors"],
                "vectors_loaded": idx_info["total_vectors"],
                "index_type": idx_info["index_type"],
                "dimension": idx_info["dimension"]
            })

            logger.info(f"🚀 索引就绪: {idx_info['total_vectors']:,} 向量")
            return {"message": f"使用预加载索引: {suitable_preloaded}", "vectors_loaded": idx_info["total_vectors"]}

        # 🔄 回退到动态加载模式
        logger.info("🔄 未找到预加载索引，使用动态加载...")

        # 找到维度匹配的数据集，优先选择向量数量最多的
        suitable_dataset = None
        max_vectors = 0
        best_case_name = None

        for case_name, dataset_info in DATASET_MAPPING.items():
            if dataset_info["dimension"] == dim:
                dataset_path = Path(DATASET_BASE_PATH) / dataset_info["path"]
                if dataset_path.exists() and dataset_info["vectors"] > max_vectors:
                    suitable_dataset = dataset_info
                    suitable_dataset["full_path"] = dataset_path
                    max_vectors = dataset_info["vectors"]
                    best_case_name = case_name
                    logger.info(f"🔍 找到数据集: {case_name} ({dataset_info['vectors']:,} 向量)")

        if suitable_dataset:
            logger.info(f"✅ 选择最大数据集: {best_case_name} ({max_vectors:,} 向量)")

        if suitable_dataset:
            # 🔄 检查是否为增量模式
            if request.incremental and server_state["current_index"] is not None:
                logger.info("🔄 增量模式：向现有索引添加数据")
                index = server_state["current_index"]
                current_vectors = index.ntotal
                logger.info(f"📊 当前索引已有 {current_vectors:,} 个向量")

                # 🔧 计算需要跳过的向量数量
                skip_vectors = current_vectors
                logger.info(f"🔄 增量模式：跳过前 {skip_vectors:,} 个向量")
            else:
                # 🏗️ 创建新索引
                logger.info(f"🏗️ 创建新的 {index_type} 索引")
                if index_type.upper() == "HNSW":
                    index = faiss.IndexHNSWFlat(dim, m)
                    index.hnsw.efConstruction = ef_construction
                    logger.info(f"新索引HNSW参数: M={m}, efConstruction={ef_construction}")
                else:
                    index = faiss.IndexFlatL2(dim)
                skip_vectors = 0

            # 加载真实数据集
            logger.info(f"📁 加载数据集: {suitable_dataset['path']}")
            train_files = list(suitable_dataset["full_path"].glob("*train*.parquet"))

            if train_files:
                # 🚦 分批加载逻辑
                if request.batch_limit:
                    logger.info(f"🚦 分批模式：限制加载 {request.batch_limit:,} 个向量")
                    target_vectors = request.batch_limit
                else:
                    logger.info(f"📊 完整模式：加载全部 {max_vectors:,} 个向量")
                    target_vectors = max_vectors

                logger.info(f"📊 正在加载 {len(train_files)} 个训练文件...")

                total_loaded = 0
                file_count = len(train_files)
                vectors_to_skip = skip_vectors  # 需要跳过的向量数量

                for file_idx, train_file in enumerate(train_files):
                    logger.info(f"📄 读取文件 {file_idx+1}/{file_count}: {train_file.name}")

                    # 🚦 流量控制：分块读取大文件，避免内存峰值
                    try:
                        # 先读取文件大小信息
                        file_size_mb = train_file.stat().st_size / (1024 * 1024)
                        logger.info(f"📊 文件大小: {file_size_mb:.1f}MB")

                        # 🔧 强制使用内存优化读取策略，避免16GB内存分配
                        logger.info("🔄 启用强制内存优化读取模式")

                        # 强制使用PyArrow分批读取，无论文件大小
                        import pyarrow.parquet as pq
                        import gc

                        # 预先清理内存
                        gc.collect()

                        # 使用极小的批次大小，避免内存峰值
                        parquet_file = pq.ParquetFile(train_file)
                        total_rows = parquet_file.metadata.num_rows
                        batch_size = min(50000, total_rows // 20)  # 更小的批次

                        logger.info(f"📊 文件总行数: {total_rows:,}, 批次大小: {batch_size:,}")

                        all_data = []
                        processed_rows = 0

                        for batch_idx, batch in enumerate(parquet_file.iter_batches(batch_size=batch_size)):
                            try:
                                # 检查内存使用
                                memory_percent = psutil.virtual_memory().percent
                                if memory_percent > 70:  # 更保守的阈值
                                    logger.warning(f"⚠️ 内存使用 {memory_percent:.1f}%，执行垃圾回收")
                                    gc.collect()
                                    time.sleep(0.5)

                                batch_df = batch.to_pandas()
                                all_data.append(batch_df)
                                processed_rows += len(batch_df)

                                if batch_idx % 5 == 0:  # 每5个批次报告一次
                                    logger.info(f"   处理进度: {processed_rows:,}/{total_rows:,} ({processed_rows/total_rows*100:.1f}%)")

                            except Exception as batch_error:
                                logger.error(f"❌ 批次 {batch_idx} 处理失败: {batch_error}")
                                # 继续处理下一个批次
                                continue

                        if all_data:
                            logger.info("🔗 合并所有批次数据...")
                            df = pd.concat(all_data, ignore_index=True)
                            del all_data  # 立即释放内存
                            gc.collect()
                            logger.info(f"✅ 成功读取 {len(df):,} 行数据")
                        else:
                            logger.error("❌ 没有成功读取任何数据")
                            continue

                    except Exception as e:
                        logger.error(f"❌ 读取文件失败: {e}")
                        # 尝试强制垃圾回收
                        import gc
                        gc.collect()
                        continue

                    # 提取向量数据
                    if 'emb' in df.columns:
                        vectors = np.vstack(df['emb'].values).astype('float32')
                    elif 'embedding' in df.columns:
                        vectors = np.vstack(df['embedding'].values).astype('float32')
                    else:
                        # 假设数值列都是向量维度
                        numeric_cols = df.select_dtypes(include=[np.number]).columns
                        vectors = df[numeric_cols].values.astype('float32')

                    logger.info(f"📊 文件包含 {len(vectors):,} 个向量")

                    # 🔄 增量模式：跳过已处理的向量
                    if vectors_to_skip > 0:
                        if vectors_to_skip >= len(vectors):
                            # 整个文件都需要跳过
                            logger.info(f"⏭️ 跳过整个文件 ({len(vectors):,} 个向量)")
                            vectors_to_skip -= len(vectors)
                            continue
                        else:
                            # 跳过文件的一部分
                            logger.info(f"⏭️ 跳过文件前 {vectors_to_skip:,} 个向量")
                            vectors = vectors[vectors_to_skip:]
                            vectors_to_skip = 0
                            logger.info(f"📊 剩余处理 {len(vectors):,} 个向量")

                    # 🚦 流量控制：小批量添加 + 自适应延迟控制
                    batch_size = FLOW_CONTROL_CONFIG["batch_size"]
                    base_delay = FLOW_CONTROL_CONFIG["base_delay"]

                    logger.info(f"🚦 启用流量控制: 批次大小={batch_size}, 基础延迟={base_delay}s")

                    for i in range(0, len(vectors), batch_size):
                        # 🚦 检查是否达到批次限制
                        if request.batch_limit and total_loaded >= target_vectors:
                            logger.info(f"🎯 达到批次限制: {total_loaded:,}/{target_vectors:,} 向量")
                            break

                        batch = vectors[i:i+batch_size]

                        # 🚦 调整最后一个批次大小，避免超出限制
                        if request.batch_limit and total_loaded + len(batch) > target_vectors:
                            remaining = target_vectors - total_loaded
                            batch = batch[:remaining]
                            logger.info(f"🔧 调整最后批次大小: {len(batch)} 向量")

                        # 🔍 系统资源监控
                        resources = get_system_resources()

                        # 添加到索引
                        start_time = time.time()
                        index.add(batch)
                        add_time = time.time() - start_time

                        total_loaded += len(batch)

                        # 🚦 自适应延迟计算
                        adaptive_delay = calculate_adaptive_delay(resources, base_delay)

                        # 如果索引添加时间过长，额外增加延迟
                        if add_time > 0.5:
                            extra_delay = min(add_time * 0.3, 1.0)
                            adaptive_delay += extra_delay
                            logger.debug(f"⏱️ 索引添加耗时 {add_time:.2f}s，额外延迟 {extra_delay:.2f}s")

                        # 🔧 关键修复：减少延迟，让HTTP请求有机会被处理
                        # 将长延迟分解为多个短延迟，中间检查HTTP请求
                        total_delay = adaptive_delay
                        while total_delay > 0.1:
                            time.sleep(0.1)  # 短暂休息，让HTTP请求有机会被处理
                            total_delay -= 0.1
                        if total_delay > 0:
                            time.sleep(total_delay)

                        # 进度报告和资源监控
                        if total_loaded % 25000 == 0:
                            progress = (file_idx * 1000000 + i) / (file_count * 1000000) * 100
                            logger.info(f"📊 已加载 {total_loaded:,} 个向量 (进度: {progress:.1f}%)")
                            logger.info(f"🖥️ 系统状态: CPU={resources['cpu_percent']:.1f}%, "
                                      f"内存={resources['memory_percent']:.1f}%, "
                                      f"延迟={adaptive_delay:.2f}s")

                            # 🚨 资源压力过大时暂停
                            if resources["memory_percent"] > 90:
                                logger.warning(f"⚠️ 内存使用过高 ({resources['memory_percent']:.1f}%)，暂停5秒...")
                                time.sleep(5)
                                gc.collect()  # 强制垃圾回收

                    # 🧹 内存清理：处理完每个文件后清理内存
                    del vectors, df
                    import gc
                    gc.collect()

                    logger.info(f"✅ 文件 {train_file.name} 处理完成，累计: {total_loaded:,} 个向量")

                    # 🚦 检查是否达到批次限制
                    if request.batch_limit and total_loaded >= target_vectors:
                        logger.info(f"🎯 达到批次限制，停止加载: {total_loaded:,}/{target_vectors:,} 向量")
                        break
                
                logger.info(f"✅ 数据集加载完成: {total_loaded:,} 个向量")
                server_state["loaded_dataset"] = suitable_dataset["path"]
            else:
                logger.warning(f"⚠️ 未找到训练文件，使用随机数据")
                # 备用方案：生成随机数据
                vectors = np.random.random((100000, dim)).astype('float32')
                index.add(vectors)
                total_loaded = 100000
        else:
            logger.warning(f"⚠️ 未找到匹配的数据集 (维度={dim})，生成随机数据")
            # 备用方案：生成随机数据
            vectors = np.random.random((100000, dim)).astype('float32')
            index.add(vectors)
            total_loaded = 100000
        
        # 更新状态
        server_state["current_index"] = index
        server_state["server_status"].update({
            "status": "index_created",
            "dimension": dim,
            "index_type": index_type,
            "total_vectors": index.ntotal,
            "vectors_count": index.ntotal,
            "vectors_loaded": index.ntotal
        })
        
        logger.info(f"✅ 索引创建完成，包含 {index.ntotal:,} 个向量")
        return {"success": True, "message": f"索引创建成功: {index_type}"}
        
    except Exception as e:
        logger.error(f"创建索引失败: {e}")
        raise HTTPException(status_code=500, detail=f"索引创建失败: {e}")

@app.post("/insert_bulk")
async def insert_bulk_smart(request: InsertRequest):
    """批量插入向量 - 智能跳过"""
    current_index = server_state["current_index"]

    # 🔍 详细日志用于调试 - 改为DEBUG级别
    logger.debug(f"📝 插入请求: {len(request.vectors)} 个向量")
    logger.debug(f"📊 当前索引状态: {current_index is not None}")

    if current_index is None:
        # 🔄 尝试从预加载索引中恢复
        logger.warning("⚠️  当前索引为空，尝试从预加载索引恢复...")

        # 查找768维的预加载索引
        for case_name, idx_info in PRELOADED_INDEXES.items():
            if idx_info["dimension"] == 768:
                logger.info(f"🔄 恢复预加载索引: {case_name}")
                server_state["current_index"] = idx_info["index"]
                server_state["loaded_dataset"] = case_name
                server_state["server_status"].update({
                    "total_vectors": idx_info["total_vectors"],
                    "vectors_count": idx_info["total_vectors"],
                    "vectors_loaded": idx_info["total_vectors"],
                    "index_type": idx_info["index_type"],
                    "dimension": idx_info["dimension"]
                })
                current_index = idx_info["index"]
                break

        if current_index is None:
            logger.error("❌ 无法恢复索引")
            raise HTTPException(status_code=400, detail="索引未创建且无法恢复")

    # 智能跳过：如果已有大量数据，直接返回
    if current_index.ntotal >= 1000000:
        logger.info(f"🚀 智能跳过：索引已包含 {current_index.ntotal:,} 个向量，跳过插入")
        return {
            "success": True,
            "inserted": len(request.vectors),
            "total": current_index.ntotal,
            "message": "智能缓存生效，跳过插入"
        }
    
    # 正常插入
    try:
        vectors = np.array(request.vectors).astype('float32')
        current_index.add(vectors)
        
        # 更新状态
        total_vectors = current_index.ntotal
        server_state["server_status"]["total_vectors"] = total_vectors
        server_state["server_status"]["vectors_count"] = total_vectors
        server_state["server_status"]["vectors_loaded"] = total_vectors
        
        return {
            "success": True, 
            "inserted": len(vectors),
            "total": total_vectors
        }
        
    except Exception as e:
        logger.error(f"向量插入失败: {e}")
        raise HTTPException(status_code=500, detail=f"向量插入失败: {e}")

# 移除了线程池相关的同步函数，直接在端点中执行搜索

@app.post("/batch_search")
async def batch_search(request: Request):
    """异步批量搜索端点 - 高性能优化"""
    data = await request.json()
    queries = np.array(data["queries"], dtype="float32")
    topk = data.get("topk", 100)
    ef_search = data.get("ef_search", None)

    current_index = server_state["current_index"]
    if current_index is None:
        raise HTTPException(status_code=400, detail="索引未初始化")

    try:
        # 设置HNSW参数 - 更激进的参数
        if hasattr(current_index, 'hnsw'):
            ef_value = ef_search if ef_search is not None else 256  # 更激进的efSearch
            current_index.hnsw.efSearch = ef_value
            logger.debug(f"设置 HNSW efSearch = {ef_value}")
        
        # 优化：使用内部分批处理来提高内存访问局部性
        batch_size = 128  # 更小的内部批次，优化内存访问模式
        results_ids = []
        results_distances = []
        
        # 分批处理查询，优化内存访问模式
        for i in range(0, len(queries), batch_size):
            batch = queries[i:i+batch_size]
            D, I = current_index.search(batch, topk)
            results_ids.append(I)
            results_distances.append(D)
        
        # 合并结果
        if len(results_ids) > 1:
            all_ids = np.vstack(results_ids)
            all_distances = np.vstack(results_distances)
            return {"ids": all_ids.tolist(), "distances": all_distances.tolist()}
        else:
            return {"ids": results_ids[0].tolist(), "distances": results_distances[0].tolist()}

    except Exception as e:
        logger.error(f"批量搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量搜索失败: {e}")

def faiss_search_sync(query_array: np.ndarray, topk: int, ef_search: Optional[int] = None) -> Dict:
    """同步 FAISS 搜索函数 - 在线程池中执行"""
    current_index = server_state["current_index"]
    if current_index is None:
        raise ValueError("索引未初始化")

    # 如果是 HNSW 索引，设置搜索参数
    if hasattr(current_index, 'hnsw'):
        # 高计算强度配置，提高CPU利用率
        ef_value = ef_search if ef_search is not None else 256  # 更激进的efSearch值
        current_index.hnsw.efSearch = ef_value
        logger.debug(f"设置 HNSW efSearch = {ef_value}")

    # 执行搜索
    D, I = current_index.search(query_array, topk)
    return {"ids": I[0].tolist(), "distances": D[0].tolist()}

@app.post("/search")
async def search(request: Request):
    """异步搜索端点 - 高性能优化"""
    try:
        data = await request.json()
        query = np.array([data["query"]], dtype="float32")
        topk = data.get("topk", 100)
        ef_search = data.get("ef_search", None)

        # Optional batching for single query - 增强批处理能力
        if data.get("batch_single", False):
            query = np.repeat(query, 32, axis=0)  # 增加到32个副本，提高计算密度

        current_index = server_state["current_index"]

        if current_index is None:
            # 🔄 尝试从预加载索引中恢复
            for case_name, idx_info in PRELOADED_INDEXES.items():
                if idx_info["dimension"] == query.shape[1]:
                    server_state["current_index"] = idx_info["index"]
                    server_state["loaded_dataset"] = case_name
                    server_state["server_status"].update({
                        "total_vectors": idx_info["total_vectors"],
                        "vectors_count": idx_info["total_vectors"],
                        "vectors_loaded": idx_info["total_vectors"],
                        "index_type": idx_info["index_type"],
                        "dimension": idx_info["dimension"]
                    })
                    current_index = idx_info["index"]
                    break

            if current_index is None:
                raise HTTPException(status_code=400, detail="索引未初始化且无法恢复")

        # 🚀 异步执行FAISS搜索，避免阻塞事件循环
        # 设置HNSW参数 - 使用更激进的参数
        if hasattr(current_index, 'hnsw'):
            ef_value = ef_search if ef_search is not None else 256  # 更激进的参数
            current_index.hnsw.efSearch = ef_value

        # 直接执行搜索，FAISS内部会释放GIL并使用OpenMP并行
        D, I = current_index.search(query, topk)

        # 统计搜索次数
        if "total_searches" not in server_state:
            server_state["total_searches"] = 0
        server_state["total_searches"] += 1

        return {"ids": I[0].tolist(), "distances": D[0].tolist()}

    except Exception as e:
        logger.error(f"❌ 搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {e}")

@app.post("/search_legacy")
async def search_vectors_legacy(request: SearchRequestLegacy):
    """向量搜索 - 兼容接口"""
    current_index = server_state["current_index"]
    
    if current_index is None:
        raise HTTPException(status_code=400, detail="索引未初始化")
    
    try:
        query_vector = np.array([request.query]).astype('float32')
        distances, indices = current_index.search(query_vector, request.topk)
        
        return SearchResponseLegacy(
            ids=[indices[0].tolist()],
            distances=[distances[0].tolist()]
        )
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {e}")

@app.get("/status")
async def get_status_smart():
    """获取状态 - 智能缓存兼容"""
    current_index = server_state["current_index"]
    
    status_response = server_state["server_status"].copy()
    
    if current_index is not None:
        status_response.update({
            "total_vectors": current_index.ntotal,
            "vectors_count": current_index.ntotal,
            "vectors_loaded": current_index.ntotal,
            "dimension": status_response.get("dimension", 768),
            "index_type": status_response.get("index_type", "HNSW")
        })
    
    return status_response

@app.get("/info")
async def get_info_smart():
    """获取服务器信息"""
    current_index = server_state["current_index"]
    loaded_dataset = server_state.get("loaded_dataset", "无")
    
    available_datasets = {}
    for case_name, dataset_info in DATASET_MAPPING.items():
        dataset_path = Path(DATASET_BASE_PATH) / dataset_info["path"]
        available_datasets[case_name] = {
            "path": dataset_info["path"],
            "dimension": dataset_info["dimension"],
            "expected_vectors": dataset_info["vectors"],
            "available": dataset_path.exists()
        }
    
    return {
        "server": "Smart FAISS Server",
        "version": "3.0.0",
        "status": server_state["server_status"]["status"],
        "features": [
            "真实数据集预加载",
            "避免重复下载",
            "完整智能缓存支持",
            "动态HNSW参数",
            "高性能搜索"
        ],
        "当前状态": {
            "向量数量": server_state["server_status"]["total_vectors"],
            "索引类型": server_state["server_status"]["index_type"],
            "维度": server_state["server_status"]["dimension"],
            "已加载数据集": loaded_dataset
        },
        "可用数据集": available_datasets,
        "数据集根目录": DATASET_BASE_PATH
    }

def main():
    parser = argparse.ArgumentParser(
        description="内存优化FAISS服务器 - 单进程+OpenMP并行模式",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🔧 内存优化配置示例:
  # 推荐配置 (使用所有CPU核心)
  python smart_faiss_server.py

  # 指定线程数
  python smart_faiss_server.py --omp-threads 16

  # 高性能配置 (最大化CPU利用)
  python smart_faiss_server.py --omp-threads 32

  # 内存受限配置 (减少线程数)
  python smart_faiss_server.py --omp-threads 8

⚠️  注意: 已强制使用单进程模式，避免内存爆炸
💡 直接使用预构建索引文件，避免加载时间过长
        """
    )

    # 基础配置
    parser.add_argument("--port", type=int, default=8005,
                       help="服务器端口 (默认: 8005)")
    parser.add_argument("--host", type=str, default="0.0.0.0",
                       help="服务器地址 (默认: 0.0.0.0)")

    # OpenMP配置
    openmp_group = parser.add_argument_group('OpenMP并行配置')
    openmp_group.add_argument("--omp-threads", type=int, default=None,
                             help="OpenMP线程数 (默认: 使用所有CPU核心)")

    # 🚦 流量控制配置
    flow_control_group = parser.add_argument_group('流量控制配置 (防止系统过载)')
    flow_control_group.add_argument("--batch-size", type=int, default=1000,
                                   help="数据加载批次大小 (默认: 1000)")
    flow_control_group.add_argument("--base-delay", type=float, default=0.01,
                                   help="基础延迟时间(秒) (默认: 0.01)")
    flow_control_group.add_argument("--memory-threshold", type=int, default=75,
                                   help="内存使用阈值百分比 (默认: 75)")

    args = parser.parse_args()

    # 🚦 更新流量控制配置
    FLOW_CONTROL_CONFIG.update({
        "batch_size": args.batch_size,
        "base_delay": args.base_delay,
        "memory_threshold": args.memory_threshold,
    })

    # 🔧 OpenMP配置逻辑
    cpu_count = os.cpu_count() or 16
    args = configure_openmp_threads(args, cpu_count)

    print("🚀 启动内存优化FAISS服务器...")
    print("=" * 60)
    print("💡 特性：单进程+OpenMP并行，避免内存爆炸")
    print("💡 使用预构建索引，避免加载时间过长")
    print(f"🌐 服务器地址: http://{args.host}:{args.port}")
    print(f"📁 预构建索引: {PREBUILT_INDEX_PATH}")

    # 显示启动模式
    print("🔄 启动模式: 单进程 + OpenMP并行")
    print("💾 内存优化: 避免多进程重复加载索引")

    # 🚦 显示流量控制配置
    print("\n🚦 流量控制配置:")
    print(f"   批次大小: {FLOW_CONTROL_CONFIG['batch_size']:,} 向量/批次")
    print(f"   基础延迟: {FLOW_CONTROL_CONFIG['base_delay']}s")
    print(f"   内存阈值: {FLOW_CONTROL_CONFIG['memory_threshold']}%")

    # 检查预构建索引文件
    target_index = "faiss_hnsw_768d_10m.index"
    index_path = os.path.join(PREBUILT_INDEX_PATH, target_index)
    if os.path.exists(index_path):
        file_size = os.path.getsize(index_path) / (1024**3)
        print(f"✅ 预构建索引: {target_index} ({file_size:.2f} GB)")
    else:
        print(f"❌ 预构建索引不存在: {target_index}")
        print("💡 请确保索引文件存在")

    print("⚡ 智能缓存: 自动跳过重复插入")
    print()

    # 🚀 FAISS OpenMP优化配置
    try:
        import faiss

        # 使用配置化的线程数
        omp_threads = args.omp_threads
        os.environ['OMP_NUM_THREADS'] = str(omp_threads)
        os.environ['MKL_NUM_THREADS'] = str(omp_threads)
        os.environ['OPENBLAS_NUM_THREADS'] = str(omp_threads)
        os.environ['VECLIB_MAXIMUM_THREADS'] = str(omp_threads)
        faiss.omp_set_num_threads(omp_threads)

        print(f"⚡ FAISS OpenMP配置:")
        print(f"   模式: 单进程 + OpenMP并行")
        print(f"   OpenMP线程数: {omp_threads}")

        current_threads = faiss.omp_get_max_threads()
        logger.info(f"🧵 FAISS OpenMP线程数设置为: {current_threads}")

    except Exception as e:
        logger.warning(f"⚠️ FAISS OpenMP配置失败: {e}")
        # 确保至少设置了环境变量
        os.environ['OMP_NUM_THREADS'] = str(args.omp_threads)

    # 🔄 强制使用 Uvicorn 单进程模式，避免内存爆炸
    try:
        import uvicorn

        print(f"🔄 启动 Uvicorn 单进程FAISS服务器:")
        print(f"   监听地址: {args.host}:{args.port}")
        print(f"   OpenMP线程数: {args.omp_threads}")
        print(f"   内存优化: 单进程避免重复加载索引")
        print()

        # 🚀 高性能配置：单进程 + 高并发 + 异步优化
        uvicorn.run(
            app,
            host=args.host,
            port=args.port,
            log_level="info",
            workers=1,                      # 🚀 单进程避免内存重复
            limit_concurrency=10000,        # 🚀 提高并发限制到10K
            loop="asyncio",
            access_log=False,
            backlog=2048,                   # 🚀 增加连接队列
            timeout_keep_alive=30,          # 🚀 保持连接活跃
            limit_max_requests=1000000      # 🚀 增加最大请求数
        )
    except ImportError:
        print("❌ uvicorn未安装: pip install uvicorn")
        sys.exit(1)

if __name__ == "__main__":
    main()
