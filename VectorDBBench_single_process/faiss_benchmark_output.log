INFO:faiss.loader:Loading faiss with SVE support.
INFO:faiss.loader:Could not load library with SVE support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_sve'")
INFO:faiss.loader:Loading faiss.
INFO:faiss.loader:Successfully loaded faiss.
INFO:faiss:Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-17 16:56:20,159 | [92mINFO[0m: [92mTask:
TaskConfig(db=<DB.FaissLocal: 'FaissLocal'>, db_config=FaissLocalConfig(db_label='test_faiss_direct', version='', note='', index_type='HNSW'), db_case_config=HNSWConfig(m=16, ef_construction=200, ef_search=64, metric_type=<MetricType.COSINE: 'COSINE'>), case_config=CaseConfig(case_id=<CaseType.Performance1536D50K: 50>, custom_case={}, k=10, concurrency_search_config=ConcurrencySearchConfig(num_concurrency=[1], concurrency_duration=10, concurrency_timeout=3600)), stages=['drop_old', 'load', 'search_serial', 'search_concurrent'])
[0m (cli.py:574) (34198)
2025-07-17 16:56:20,159 | [92mINFO[0m: [92mgenerated uuid for the tasks: 04e8d86cb43246678461ccf7e16cb29e[0m (interface.py:72) (34198)
2025-07-17 16:56:20,159 | INFO | DB             | CaseType     Dataset               Filter | task_label (task_runner.py:379)
2025-07-17 16:56:20,159 | INFO | -----------    | ------------ -------------------- ------- | -------    (task_runner.py:379)
2025-07-17 16:56:20,159 | INFO | FaissLocal-test_faiss_direct | Performance  OpenAI-SMALL-50K         0.0 | 04e8d86cb43246678461ccf7e16cb29e (task_runner.py:379)
2025-07-17 16:56:20,159 | [92mINFO[0m: [92mtask submitted: id=04e8d86cb43246678461ccf7e16cb29e, 04e8d86cb43246678461ccf7e16cb29e, case number: 1[0m (interface.py:247) (34198)
Dataset metric type: MetricType.COSINE
Dataset path: Openai (Small)
开始运行 FAISS benchmark...
INFO:faiss.loader:Loading faiss with SVE support.
INFO:faiss.loader:Could not load library with SVE support due to:
ModuleNotFoundError("No module named 'faiss.swigfaiss_sve'")
INFO:faiss.loader:Loading faiss.
INFO:faiss.loader:Successfully loaded faiss.
INFO:faiss:Failed to load GPU Faiss: name 'GpuIndexIVFFlat' is not defined. Will not load constructor refs for GPU indexes. This is only an error if you're trying to use GPU Faiss.
2025-07-17 16:56:20,560 | [92mINFO[0m: [92m[1/1] start case: {'label': <CaseLabel.Performance: 2>, 'name': 'Search Performance Test (50K Dataset, 1536 Dim)', 'dataset': {'data': {'name': 'OpenAI', 'size': 50000, 'dim': 1536, 'metric_type': <MetricType.COSINE: 'COSINE'>}}, 'db': 'FaissLocal-test_faiss_direct'}, drop_old=True[0m (interface.py:177) (34264)
2025-07-17 16:56:20,561 | [92mINFO[0m: [92mStarting run[0m (task_runner.py:118) (34264)
2025-07-17 16:56:21,371 | [92mINFO[0m: [92mlocal file: /home/<USER>/VectorDBBench/dataset/openai/openai_small_50k/shuffle_train.parquet size[7276079] not match with remote size[449785510][0m (data_source.py:154) (34264)
2025-07-17 16:56:21,372 | [92mINFO[0m: [92mlocal file: /home/<USER>/VectorDBBench/dataset/openai/openai_small_50k/shuffle_train.parquet not match with remote: assets.zilliz.com/benchmark/openai_small_50k/shuffle_train.parquet; add to downloading list[0m (data_source.py:134) (34264)
2025-07-17 16:56:21,372 | [92mINFO[0m: [92mlocal file: /home/<USER>/VectorDBBench/dataset/openai/openai_small_50k/neighbors.parquet not match with remote: assets.zilliz.com/benchmark/openai_small_50k/neighbors.parquet; add to downloading list[0m (data_source.py:134) (34264)
2025-07-17 16:56:21,372 | [92mINFO[0m: [92mlocal file: /home/<USER>/VectorDBBench/dataset/openai/openai_small_50k/test.parquet not match with remote: assets.zilliz.com/benchmark/openai_small_50k/test.parquet; add to downloading list[0m (data_source.py:134) (34264)
2025-07-17 16:56:21,372 | [92mINFO[0m: [92mlocal file: /home/<USER>/VectorDBBench/dataset/openai/openai_small_50k/scalar_labels.parquet not match with remote: assets.zilliz.com/benchmark/openai_small_50k/scalar_labels.parquet; add to downloading list[0m (data_source.py:134) (34264)
2025-07-17 16:56:21,372 | [92mINFO[0m: [92mStart to downloading files, total count: 4[0m (data_source.py:140) (34264)

  0%|          | 0/4 [00:00<?, ?it/s]✅ FAISS benchmark 完成!
