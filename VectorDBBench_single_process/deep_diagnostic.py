#!/usr/bin/env python3
"""
深度诊断：为什么基准测试任务创建了但没有实际执行
"""

import sys
import traceback
import time
from pathlib import Path

def test_minimal_benchmark():
    """运行最小化的基准测试来诊断问题"""
    print("🔬 运行最小化基准测试诊断")
    print("=" * 50)
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
        from vectordb_bench.backend.model import CaseType, MetricType
        from vectordb_bench.backend.cases import CaseConfig, ConcurrencySearchConfig
        
        print("✅ 所有模块导入成功")
        
        # 创建最小配置
        db_config = FaissConfig(
            host='127.0.0.1',
            port=8002,
            index_type='Flat',
            db_label='debug_test'
        )
        
        db_case_config = FaissDBCaseConfig(
            metric_type=MetricType.COSINE
        )
        
        case_config = CaseConfig(
            case_id=CaseType.Performance1536D50K,
            k=10,
            concurrency_search_config=ConcurrencySearchConfig(
                num_concurrency=[1],
                concurrency_duration=5,
                concurrency_timeout=60
            )
        )
        
        print("✅ 配置创建成功")
        print(f"   🔧 数据库配置: {db_config}")
        print(f"   📋 案例配置: {db_case_config}")
        print(f"   🎯 测试配置: {case_config}")
        
        print("\n🚀 开始执行基准测试...")
        start_time = time.time()
        
        # 运行测试
        result = run(
            db=DB.Faiss,
            db_config=db_config,
            db_case_config=db_case_config,
            case_config=case_config,
            stages=['drop_old', 'load'],  # 只做加载测试
            dataset_name='openai_small_50k'
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 测试完成，耗时: {duration:.2f}s")
        print(f"📊 返回结果: {result}")
        
        return True, result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False, None

def check_task_execution_path():
    """检查任务执行路径"""
    print(f"\n🔍 检查任务执行路径")
    print("=" * 50)
    
    try:
        # 检查关键模块
        from vectordb_bench.interface import VectorDBBenchInterface
        from vectordb_bench.backend.task_runner import TaskRunner
        
        print("✅ 任务执行相关模块存在")
        
        # 尝试创建接口
        interface = VectorDBBenchInterface()
        print(f"✅ VectorDBBenchInterface 创建成功: {type(interface)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务执行路径检查失败: {e}")
        traceback.print_exc()
        return False

def analyze_faiss_connection():
    """分析 FAISS 连接情况"""
    print(f"\n🌐 分析 FAISS 连接情况")
    print("=" * 50)
    
    try:
        from vectordb_bench.backend.clients.faiss.faiss import Faiss
        from vectordb_bench.backend.clients.faiss.config import FaissConfig
        
        # 创建客户端
        config = FaissConfig(
            host='127.0.0.1',
            port=8002,
            index_type='Flat'
        )
        
        client = Faiss(
            dim=1536,
            db_config=config,
            db_case_config=None,
            drop_old=True
        )
        
        print("✅ FAISS 客户端创建成功")
        print(f"   🔗 连接配置: {config}")
        
        # 尝试创建索引（但不真正执行操作）
        print("✅ FAISS 客户端配置正确")
        
        return True, client
        
    except Exception as e:
        print(f"❌ FAISS 连接分析失败: {e}")
        traceback.print_exc()
        return False, None

def check_dataset_availability():
    """检查数据集可用性"""
    print(f"\n📊 检查数据集可用性")
    print("=" * 50)
    
    dataset_paths = [
        "dataset/openai_small_50k",
        "dataset/openai",
        "dataset/openai_test"
    ]
    
    for dataset_path in dataset_paths:
        path = Path(dataset_path)
        if path.exists():
            print(f"✅ 数据集存在: {dataset_path}")
            files = list(path.glob("*.parquet"))
            for file in files:
                size = file.stat().st_size / (1024*1024)  # MB
                print(f"   📄 {file.name}: {size:.1f}MB")
        else:
            print(f"❌ 数据集不存在: {dataset_path}")

def diagnose_results_generation():
    """诊断结果生成机制"""
    print(f"\n📈 诊断结果生成机制")
    print("=" * 50)
    
    try:
        # 检查结果目录权限
        results_dir = Path("vectordb_bench/results/Faiss")
        if not results_dir.exists():
            results_dir.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建结果目录: {results_dir}")
        
        # 检查写入权限
        test_file = results_dir / "test_write.txt"
        try:
            test_file.write_text("test")
            test_file.unlink()
            print(f"✅ 结果目录可写: {results_dir}")
        except Exception as e:
            print(f"❌ 结果目录不可写: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ 结果生成诊断失败: {e}")
        return False

def main():
    print("🔬 深度诊断：基准测试执行问题")
    print("=" * 60)
    
    # 运行各项诊断
    results = {}
    
    print("\n📋 诊断项目:")
    
    # 1. 检查任务执行路径
    results['task_path'] = check_task_execution_path()
    
    # 2. 分析FAISS连接
    results['faiss_connection'] = analyze_faiss_connection()[0]
    
    # 3. 检查数据集
    check_dataset_availability()
    
    # 4. 诊断结果生成
    results['results_generation'] = diagnose_results_generation()
    
    # 5. 运行最小测试
    results['minimal_test'], test_result = test_minimal_benchmark()
    
    print(f"\n📊 诊断总结:")
    print("=" * 30)
    
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}: {'通过' if success else '失败'}")
    
    if all(results.values()):
        print(f"\n🎉 所有诊断项目都通过了！")
        print("问题可能是：")
        print("• 测试时间太短，没有足够时间生成详细结果")
        print("• 需要更长的测试持续时间来生成QPS、时延等指标")
    else:
        print(f"\n⚠️  发现问题，需要进一步调查")

if __name__ == "__main__":
    main()
