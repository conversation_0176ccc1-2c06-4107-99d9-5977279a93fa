#!/usr/bin/env python3
"""
测试VectorDBBench的多进程路径一致性
"""

import sys
import os
sys.path.append('/home/<USER>/VectorDBBench')

def test_vectordb_paths():
    """测试VectorDBBench路径一致性"""
    
    print("🧪 VectorDBBench 路径一致性测试")
    print("=" * 50)
    
    # 使用相同的配置创建多个客户端实例
    from vectordb_bench.backend.clients.faiss_local.faiss_local import FaissLocalClient
    from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
    from vectordb_bench.backend.clients.api import MetricType
    
    # 模拟VectorDBBench的实际配置
    db_config = FaissLocalConfig(
        db_label="faiss_real_data_performance1536d50k",  # 使用相同的标签
        index_type="HNSW"
    )
    
    db_case_config = HNSWConfig(
        m=16,
        ef_construction=200,
        ef_search=64,
        metric_type=MetricType.COSINE
    )
    
    print("1️⃣ 创建第一个客户端（模拟插入进程）")
    client1 = FaissLocalClient(
        dim=1536,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="FaissLocalCollection"
    )
    
    print("\n2️⃣ 创建第二个客户端（模拟搜索进程）")
    client2 = FaissLocalClient(
        dim=1536,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="FaissLocalCollection"
    )
    
    print("\n3️⃣ 路径比较")
    print(f"客户端1临时目录: {client1.temp_dir}")
    print(f"客户端2临时目录: {client2.temp_dir}")
    print(f"路径是否一致: {client1.temp_dir == client2.temp_dir}")
    
    print("\n4️⃣ 检查现有索引文件")
    existing_index = "/tmp/faiss_vectordb_8e00836292b16d00/faiss.index"
    expected_index = client1.index_file
    
    print(f"现有索引文件: {existing_index}")
    print(f"客户端期望文件: {expected_index}")
    print(f"文件路径匹配: {existing_index == expected_index}")
    
    if existing_index == expected_index:
        print("✅ 路径匹配！客户端应该能找到现有索引")
        
        # 尝试加载搜索
        print("\n5️⃣ 尝试在第二个客户端中搜索")
        import numpy as np
        query_vector = np.random.random(1536).tolist()
        
        with client2.init():
            results = client2.search_embedding(query_vector, k=5)
            if len(results) > 0:
                print(f"   ✅ 搜索成功: {results}")
            else:
                print("   ❌ 搜索失败")
    else:
        print("❌ 路径不匹配！这解释了为什么搜索找不到索引")

if __name__ == "__main__":
    test_vectordb_paths()
