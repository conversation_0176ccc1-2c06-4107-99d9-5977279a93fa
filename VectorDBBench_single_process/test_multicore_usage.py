#!/usr/bin/env python3
"""
专门测试FAISS服务器多核CPU使用情况的脚本
"""

import time
import threading
import requests
import psutil
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
import os

def get_server_pid():
    """获取smart_faiss_server.py的进程ID"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'smart_faiss_server.py' in ' '.join(proc.info['cmdline']):
                return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def monitor_cpu_usage(duration=30, sample_interval=0.5):
    """监控CPU使用情况"""
    server_pid = get_server_pid()
    if not server_pid:
        print("❌ 找不到smart_faiss_server.py进程")
        return
    
    try:
        server_process = psutil.Process(server_pid)
        print(f"📊 监控服务器进程 PID: {server_pid}")
        print(f"📊 CPU亲和性设置: {server_process.cpu_affinity()}")
        print(f"📊 线程数: {server_process.num_threads()}")
        
        cpu_samples = []
        per_cpu_samples = []
        
        start_time = time.time()
        while time.time() - start_time < duration:
            # 总体CPU使用率
            cpu_percent = server_process.cpu_percent()
            
            # 每个CPU核心的使用率
            per_cpu_percent = psutil.cpu_percent(percpu=True, interval=None)
            
            cpu_samples.append(cpu_percent)
            per_cpu_samples.append(per_cpu_percent)
            
            # 实时显示
            active_cores = sum(1 for x in per_cpu_percent if x > 5.0)
            high_usage_cores = sum(1 for x in per_cpu_percent if x > 20.0)
            max_cpu = max(per_cpu_percent) if per_cpu_percent else 0
            
            print(f"⚡ 服务器CPU: {cpu_percent:6.1f}% | 活跃核心: {active_cores:2d} | 高负载核心: {high_usage_cores:2d} | 最大核心使用: {max_cpu:5.1f}%")
            
            time.sleep(sample_interval)
            
    except psutil.NoSuchProcess:
        print(f"❌ 进程 {server_pid} 不存在")
    except Exception as e:
        print(f"❌ 监控出错: {e}")

def send_search_request():
    """发送搜索请求"""
    url = "http://localhost:8001/search"
    
    # 生成随机查询向量
    query_vector = np.random.random(768).tolist()
    
    data = {
        "query_vector": query_vector,
        "top_k": 10
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        if response.status_code == 200:
            return True, response.elapsed.total_seconds()
        else:
            return False, f"HTTP {response.status_code}: {response.text[:100]}"
    except Exception as e:
        return False, str(e)

def load_dataset_if_needed():
    """如果需要的话加载数据集"""
    try:
        # 检查是否已加载数据集
        response = requests.get("http://localhost:8001/health")
        if response.status_code == 200:
            health_data = response.json()
            if 'vector_count' in health_data and health_data.get('vector_count', 0) > 0:
                print(f"✅ 数据集已加载，向量数量: {health_data['vector_count']}")
                return True
        
        print("📂 正在加载数据集 Performance768D1M...")
        load_data = {
            "dataset_name": "Performance768D1M", 
            "case_config": {
                "index_type": "HNSW",
                "index_param": {
                    "M": 16,
                    "efConstruction": 200
                }
            }
        }
        
        response = requests.post("http://localhost:8001/load", json=load_data, timeout=300)
        if response.status_code == 200:
            print("✅ 数据集加载成功")
            return True
        else:
            print(f"❌ 数据集加载失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 加载数据集时出错: {e}")
        return False

def run_concurrent_load_test(num_threads=16, requests_per_thread=10, test_name="默认测试"):
    """运行并发负载测试"""
    print(f"\n🚀 {test_name}")
    print("=" * 60)
    print(f"🎯 并发线程数: {num_threads}")
    print(f"🎯 每线程请求数: {requests_per_thread}")
    print(f"🎯 总请求数: {num_threads * requests_per_thread}")
    
    # 启动CPU监控线程
    monitor_duration = max(30, (num_threads * requests_per_thread) // 10)
    monitor_thread = threading.Thread(
        target=monitor_cpu_usage, 
        args=(monitor_duration, 0.2)
    )
    monitor_thread.daemon = True
    monitor_thread.start()
    
    time.sleep(1)  # 让监控线程先启动
    
    # 并发发送请求
    success_count = 0
    fail_count = 0
    response_times = []
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        # 提交所有任务
        futures = []
        for thread_id in range(num_threads):
            for req_id in range(requests_per_thread):
                future = executor.submit(send_search_request)
                futures.append(future)
        
        # 收集结果
        for i, future in enumerate(as_completed(futures)):
            success, result = future.result()
            if success:
                success_count += 1
                response_times.append(result)
            else:
                fail_count += 1
                if fail_count <= 5:  # 只打印前5个错误
                    print(f"❌ 请求失败: {result}")
            
            # 每10个请求报告一次进度
            if (i + 1) % 10 == 0:
                elapsed = time.time() - start_time
                print(f"🔄 完成 {i+1}/{len(futures)} 请求 ({elapsed:.1f}s)")
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # 等待监控线程结束
    time.sleep(2)
    
    # 打印结果
    print(f"\n📊 测试结果:")
    print(f"   总耗时: {total_duration:.2f}秒")
    print(f"   成功请求: {success_count}")
    print(f"   失败请求: {fail_count}")
    if success_count > 0:
        print(f"   平均QPS: {success_count / total_duration:.2f}")
        print(f"   平均延迟: {np.mean(response_times)*1000:.2f}ms")
        print(f"   最小延迟: {min(response_times)*1000:.2f}ms")
        print(f"   最大延迟: {max(response_times)*1000:.2f}ms")
    
    return success_count > 0

def main():
    print("🔧 FAISS服务器多核CPU使用情况测试")
    print("="*60)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未运行或响应异常")
            return
        print("✅ 服务器运行正常")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return
    
    # 加载数据集
    if not load_dataset_if_needed():
        print("❌ 数据集加载失败，退出测试")
        return
    
    # 运行不同强度的测试
    test_configs = [
        {"threads": 4, "requests": 5, "name": "轻负载测试 (4并发)"},
        {"threads": 8, "requests": 10, "name": "中等负载测试 (8并发)"},
        {"threads": 16, "requests": 15, "name": "高负载测试 (16并发)"},
        {"threads": 32, "requests": 10, "name": "极限负载测试 (32并发)"},
    ]
    
    for config in test_configs:
        success = run_concurrent_load_test(
            config["threads"], 
            config["requests"], 
            config["name"]
        )
        
        if not success:
            print(f"⚠️ {config['name']} 完全失败，跳过后续测试")
            break
        
        print("\n⏱️ 等待5秒后进行下一个测试...")
        time.sleep(5)
    
    print("\n🎉 所有测试完成!")
    print("💡 请查看上面的CPU监控输出，观察多核心使用情况")

if __name__ == "__main__":
    main()
