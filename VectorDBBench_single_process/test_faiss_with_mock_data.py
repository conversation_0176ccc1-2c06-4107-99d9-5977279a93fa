#!/usr/bin/env python3
"""
测试 FAISS 本地客户端与模拟数据
"""

import os
import numpy as np
import pandas as pd
from pathlib import Path

# 设置环境变量
os.environ['DATASET_LOCAL_DIR'] = '/home/<USER>/VectorDBBench/dataset'

def create_mock_openai_dataset():
    """创建模拟的 OpenAI 数据集"""
    dataset_dir = Path('/home/<USER>/VectorDBBench/dataset')
    dataset_dir.mkdir(exist_ok=True)
    
    # 创建 OpenAI 目录
    openai_dir = dataset_dir / 'openai'
    openai_dir.mkdir(exist_ok=True)
    
    # 生成模拟训练数据
    print("创建模拟 OpenAI 训练数据...")
    num_vectors = 1000  # 小数据集便于测试
    dim = 1536
    
    # 生成随机向量并归一化（OpenAI 是 COSINE 距离）
    vectors = np.random.random((num_vectors, dim)).astype(np.float32)
    vectors = vectors / np.linalg.norm(vectors, axis=1, keepdims=True)
    
    # 创建训练数据
    train_data = pd.DataFrame({
        'id': range(num_vectors),
        'vector': [v.tolist() for v in vectors]
    })
    
    train_file = openai_dir / 'train.parquet'
    train_data.to_parquet(train_file, index=False)
    print(f"训练数据保存到: {train_file}")
    
    # 创建测试数据
    print("创建模拟 OpenAI 测试数据...")
    num_test = 100
    test_vectors = np.random.random((num_test, dim)).astype(np.float32)
    test_vectors = test_vectors / np.linalg.norm(test_vectors, axis=1, keepdims=True)
    
    test_data = pd.DataFrame({
        'vector': [v.tolist() for v in test_vectors]
    })
    
    test_file = openai_dir / 'test.parquet'
    test_data.to_parquet(test_file, index=False)
    print(f"测试数据保存到: {test_file}")
    
    # 创建 ground truth 数据
    print("创建模拟 ground truth 数据...")
    # 简单的模拟 - 每个测试向量对应前 10 个训练向量
    gt_data = pd.DataFrame({
        'neighbors': [[i % num_vectors for i in range(j*10, (j+1)*10)] for j in range(num_test)]
    })
    
    gt_file = openai_dir / 'gt.parquet'
    gt_data.to_parquet(gt_file, index=False)
    print(f"Ground truth 数据保存到: {gt_file}")
    
    return openai_dir

def test_faiss_with_mock_data():
    """测试 FAISS 客户端"""
    # 创建模拟数据
    dataset_dir = create_mock_openai_dataset()
    
    # 导入必要的模块
    from vectordb_bench.backend.clients.faiss_local.faiss_local import FaissLocalClient
    from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
    from vectordb_bench.backend.clients.api import MetricType
    
    # 创建客户端配置
    db_config = FaissLocalConfig(index_type="HNSW")
    db_case_config = HNSWConfig(
        m=16,
        ef_construction=200,
        ef_search=64,
        metric_type=MetricType.COSINE
    )
    
    # 创建客户端
    client = FaissLocalClient(
        dim=1536,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="test_collection"
    )
    
    # 加载训练数据
    train_file = dataset_dir / 'train.parquet'
    train_df = pd.read_parquet(train_file)
    
    print(f"加载训练数据: {len(train_df)} 个向量")
    
    # 插入数据
    vectors = train_df['vector'].tolist()
    ids = train_df['id'].tolist()
    
    print("插入向量到 FAISS...")
    result, error = client.insert_embeddings(vectors, ids)
    if error:
        print(f"插入失败: {error}")
        return
    
    print(f"成功插入 {result} 个向量")
    
    # 优化索引
    print("优化索引...")
    client.optimize()
    
    # 测试搜索
    print("测试搜索...")
    test_file = dataset_dir / 'test.parquet'
    test_df = pd.read_parquet(test_file)
    
    query_vector = test_df['vector'].iloc[0]
    results = client.search_embedding(query_vector, k=10)
    
    print(f"搜索结果: {results}")
    
    if len(results) > 0:
        print("✅ FAISS 客户端测试成功！")
        return True
    else:
        print("❌ FAISS 客户端搜索无结果")
        return False

if __name__ == "__main__":
    success = test_faiss_with_mock_data()
    if success:
        print("\n🎉 FAISS 本地客户端已成功修复并可以正常工作！")
    else:
        print("\n❌ FAISS 本地客户端仍有问题需要进一步修复")
