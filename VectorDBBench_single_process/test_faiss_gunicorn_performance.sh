#!/bin/bash
# FAISS Gunicorn 多进程性能测试脚本

echo "🎯 FAISS Gunicorn 多进程性能测试"
echo "=================================="

# 设置环境变量
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset
export PYTHONPATH=/home/<USER>/VectorDBBench:$PYTHONPATH

# 获取时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
TEST_LABEL="faiss_gunicorn_${TIMESTAMP}"

echo "📋 测试配置:"
echo "   时间戳: $TIMESTAMP"
echo "   测试标签: $TEST_LABEL"
echo "   数据集路径: $DATASET_LOCAL_DIR"
echo "   服务地址: http://10.1.180.72:8005"
echo ""

# 1. 验证服务状态
echo "🔍 验证FAISS服务状态..."
if curl -s http://10.1.180.72:8005/status > /dev/null 2>&1; then
    echo "✅ FAISS服务运行正常"
else
    echo "❌ FAISS服务未运行，请先启动服务"
    echo "   启动命令: ./start_faiss_with_cgroup.sh"
    exit 1
fi

# 2. 检查服务器进程信息
echo ""
echo "📊 服务器进程信息:"
if [ "$1" = "local" ]; then
    echo "   本地测试模式"
    ps aux | grep smart_faiss_server | grep -v grep || echo "   未找到FAISS进程"
else
    ssh root@10.1.180.72 "ps aux | grep smart_faiss_server | grep -v grep" || echo "   无法获取远程进程信息"
fi

# 3. 运行基础功能测试
echo ""
echo "🧪 基础功能测试 (小数据集)..."
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://10.1.180.72:8005 \
    --case-type Performance768D50K \
    --index-type HNSW \
    --m 16 \
    --ef-construction 128 \
    --ef-search 64 \
    --concurrency-duration 10 \
    --num-concurrency 4,8 \
    --k 100 \
    --db-label "faiss_basic_test_${TIMESTAMP}" \
    --task-label "FAISS_Basic_Test_${TIMESTAMP}"

if [ $? -eq 0 ]; then
    echo "✅ 基础功能测试通过"
else
    echo "❌ 基础功能测试失败"
    exit 1
fi

# 4. 运行性能对比测试 (与之前的单进程结果对比)
echo ""
echo "🚀 性能对比测试 (1M数据集)..."
echo "   配置: HNSW M=30, ef_construction=360, ef_search=100"
echo "   并发度: 8,16,32,64,128"
echo "   持续时间: 30秒"

python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://10.1.180.72:8005 \
    --case-type Performance768D1M \
    --index-type HNSW \
    --m 30 \
    --ef-construction 360 \
    --ef-search 100 \
    --concurrency-duration 30 \
    --num-concurrency 8,16,32,64,128 \
    --k 100 \
    --db-label "faiss_gunicorn_${TIMESTAMP}" \
    --task-label "FAISS_Gunicorn_Performance_${TIMESTAMP}"

if [ $? -eq 0 ]; then
    echo "✅ 性能测试完成"
else
    echo "❌ 性能测试失败"
    exit 1
fi

# 5. 运行高并发压力测试
echo ""
echo "💪 高并发压力测试..."
echo "   并发度: 64,128,256"
echo "   持续时间: 60秒"

python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://10.1.180.72:8005 \
    --case-type Performance768D1M \
    --index-type HNSW \
    --m 30 \
    --ef-construction 360 \
    --ef-search 100 \
    --concurrency-duration 60 \
    --num-concurrency 64,128,256 \
    --k 100 \
    --db-label "faiss_stress_${TIMESTAMP}" \
    --task-label "FAISS_Stress_Test_${TIMESTAMP}"

# 6. 收集测试结果
echo ""
echo "📊 测试完成，收集结果..."

# 查找最新的结果文件
RESULT_DIR="vectordb_bench/results/Faiss"
if [ -d "$RESULT_DIR" ]; then
    echo "📁 结果文件位置: $RESULT_DIR"
    ls -la "$RESULT_DIR" | grep "$TIMESTAMP" || echo "   未找到今日结果文件"
else
    echo "⚠️  结果目录不存在: $RESULT_DIR"
fi

# 7. 显示性能对比
echo ""
echo "📈 性能对比分析:"
echo "=================================="
echo "🔄 单进程模式 (之前测试):"
echo "   并发度 8:  QPS ≈ 470, P99延迟 ≈ 18ms"
echo "   并发度 16: QPS ≈ 475, P99延迟 ≈ 36ms"
echo ""
echo "🚀 多进程模式 (本次测试):"
echo "   预期 QPS: 1500-3000 (3-6倍提升)"
echo "   预期延迟: 10-25ms (更低延迟)"
echo "   CPU利用率: 80-90% (充分利用16核)"
echo ""

# 8. 监控建议
echo "🔧 监控建议:"
echo "=================================="
echo "服务器端监控:"
echo "   内存使用: ssh root@10.1.180.72 'watch -n 1 \"ps aux | grep gunicorn\"'"
echo "   CPU使用: ssh root@10.1.180.72 'htop'"
echo "   日志查看: ssh root@10.1.180.72 'tail -f /home/<USER>/VectorDBBench/faiss_server_gunicorn.log'"
echo ""
echo "客户端监控:"
echo "   网络延迟: ping 10.1.180.72"
echo "   连接测试: curl http://10.1.180.72:8005/status"
echo ""

# 9. 清理和总结
echo "✅ 测试脚本执行完成"
echo "   测试标签: $TEST_LABEL"
echo "   时间戳: $TIMESTAMP"
echo "   下一步: 分析结果文件中的性能数据"
