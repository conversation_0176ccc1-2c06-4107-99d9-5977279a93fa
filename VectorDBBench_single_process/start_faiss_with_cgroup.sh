#!/bin/bash
# filepath: /home/<USER>/VectorDBBench/start_faiss_with_cgroup.sh

echo "🚀 在Cgroup环境中启动FAISS服务 (16C64G) - Gunicorn多进程模式"

# 1. 设置环境变量
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset
export PYTHONPATH=/home/<USER>/VectorDBBench:$PYTHONPATH

# 🧵 FAISS 多线程配置 (总线程数控制)
# 8个Worker × 2个OpenMP线程 = 16个总线程 (匹配16核CPU)
export OMP_NUM_THREADS=2
export MKL_NUM_THREADS=2
export OPENBLAS_NUM_THREADS=2
export VECLIB_MAXIMUM_THREADS=2

echo "🔧 环境配置:"
echo "   数据集路径: $DATASET_LOCAL_DIR"
echo "   每Worker线程数: $OMP_NUM_THREADS"
echo "   预期总线程数: 8 workers × 2 threads = 16 threads"

# 2. 应用Cgroup配置
if [ -f "./setup_cgroup_faiss.sh" ]; then
    echo "📊 应用Cgroup资源限制..."
    sh ./setup_cgroup_faiss.sh
else
    echo "⚠️  Cgroup配置文件不存在，跳过资源限制"
fi

# 3. 检查依赖
echo "🔍 检查依赖..."
if ! command -v gunicorn &> /dev/null; then
    echo "❌ Gunicorn未安装，正在安装..."
    pip install gunicorn
fi

# 4. 验证配置文件
if [ ! -f "gunicorn_faiss_config.py" ]; then
    echo "❌ Gunicorn配置文件不存在: gunicorn_faiss_config.py"
    exit 1
fi

# 5. 启动服务 - 使用Gunicorn + preload模式
echo "🚀 启动FAISS服务进程 (Gunicorn多进程 + preload)..."
echo "   配置: 8 workers, preload_app=True"
echo "   地址: http://0.0.0.0:8005"
echo "   日志: faiss_server_gunicorn.log"

nohup gunicorn smart_faiss_server:app \
    --config gunicorn_faiss_config.py \
    --log-level info \
    --capture-output \
    > faiss_server_gunicorn.log 2>&1 &

FAISS_PID=$!
echo "✅ FAISS服务启动完成"
echo "   主进程PID: $FAISS_PID"

# 6. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 3

# 7. 将主进程和所有Worker加入Cgroup
if [ -d "/sys/fs/cgroup/faiss-service" ]; then
    echo "📊 将进程加入Cgroup限制..."

    # 加入主进程
    echo $FAISS_PID | sudo tee /sys/fs/cgroup/faiss-service/cgroup.procs > /dev/null
    echo "   主进程 $FAISS_PID 已加入Cgroup"

    # 等待Worker进程启动并加入Cgroup
    sleep 2
    pgrep -P $FAISS_PID | while read child_pid; do
        echo $child_pid | sudo tee -a /sys/fs/cgroup/faiss-service/cgroup.procs > /dev/null 2>&1 || true
        echo "   Worker $child_pid 已加入Cgroup"
    done
else
    echo "⚠️  Cgroup未配置，跳过资源限制"
fi

# 8. 验证服务状态
echo "🔍 验证服务状态..."
if curl -s http://localhost:8005/status > /dev/null 2>&1; then
    echo "✅ FAISS服务运行正常"
else
    echo "⚠️  服务可能还在启动中，请稍后检查"
fi

# 9. 显示进程信息
echo ""
echo "📋 服务信息:"
echo "   服务地址: http://0.0.0.0:8005"
echo "   主进程PID: $FAISS_PID"
echo "   配置文件: gunicorn_faiss_config.py"
echo "   日志文件: faiss_server_gunicorn.log"
echo ""
echo "🔧 管理命令:"
echo "   查看日志: tail -f faiss_server_gunicorn.log"
echo "   查看进程: ps aux | grep gunicorn"
echo "   停止服务: kill $FAISS_PID"
echo "   重启服务: kill $FAISS_PID && ./start_faiss_with_cgroup.sh"
echo ""
echo "📊 监控命令:"
echo "   内存使用: watch -n 1 'ps aux | grep gunicorn'"
echo "   API测试: curl http://localhost:8005/status"
