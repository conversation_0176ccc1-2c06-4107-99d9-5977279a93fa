#!/usr/bin/env python3
"""
🎯 远程FAISS基准测试部署指南
分析客户机和服务器端的文件需求和环境准备
"""

import os
import sys
from pathlib import Path

def analyze_client_requirements():
    """分析客户机端需要的文件和依赖"""
    print("📱 客户机端需求分析")
    print("=" * 40)
    
    print("🗂️ 必需的核心文件:")
    
    # 核心VectorDBBench框架文件
    essential_files = [
        # 核心模块
        ("vectordb_bench/", "整个VectorDBBench核心框架"),
        ("vectordb_bench/__init__.py", "模块初始化"),
        ("vectordb_bench/__main__.py", "模块入口点"),
        
        # CLI接口
        ("vectordb_bench/cli/", "命令行接口模块"),
        ("vectordb_bench/cli/cli.py", "核心CLI函数"),
        
        # 后端核心
        ("vectordb_bench/backend/", "后端核心模块"),
        ("vectordb_bench/backend/clients/__init__.py", "DB枚举定义"),
        ("vectordb_bench/backend/clients/api.py", "抽象接口定义"),
        
        # FAISS远程客户端
        ("vectordb_bench/backend/clients/faiss/", "FAISS客户端模块"),
        ("vectordb_bench/backend/clients/faiss/faiss.py", "FAISS远程客户端"),
        ("vectordb_bench/backend/clients/faiss/config.py", "FAISS配置类"),
        
        # 执行引擎
        ("vectordb_bench/interface.py", "基准测试执行引擎"),
        ("vectordb_bench/backend/task_runner.py", "任务执行器"),
        ("vectordb_bench/backend/assembler.py", "任务组装器"),
        
        # 数据和结果处理
        ("vectordb_bench/backend/data_source.py", "数据源管理"),
        ("vectordb_bench/backend/result_collector.py", "结果收集器"),
        ("vectordb_bench/metric.py", "性能指标计算"),
        ("vectordb_bench/models.py", "数据模型定义"),
        
        # 配置文件
        ("vectordb_bench/config.py", "框架配置"),
        
        # 用户脚本
        ("run_real_vectordb_benchmark.py", "便捷测试脚本"),
        
        # 依赖文件
        ("pyproject.toml", "Python项目配置"),
        ("requirements.txt", "Python依赖列表（如果存在）")
    ]
    
    for file_path, description in essential_files:
        print(f"   📄 {file_path}")
        print(f"      {description}")
    
    print("\n🚫 客户机端不需要的文件:")
    unnecessary_files = [
        "vectordb_bench/backend/clients/faiss/server.py (服务器端)",
        "dataset/ (大型数据集文件)",
        "results/ (历史测试结果)",
        "logs/ (历史日志)",
        "build/ (构建产物)",
        "其他数据库的客户端模块 (如果只测试FAISS)"
    ]
    
    for file in unnecessary_files:
        print(f"   ❌ {file}")

def analyze_server_requirements():
    """分析服务器端需要的文件和依赖"""
    print("\n🖥️ 服务器端需求分析")
    print("=" * 40)
    
    print("🗂️ 必需的文件:")
    server_files = [
        # FAISS服务器
        ("vectordb_bench/backend/clients/faiss/server.py", "FastAPI FAISS服务器"),
        ("vectordb_bench/backend/clients/faiss/config.py", "配置类（服务器也需要）"),
        
        # 基础模块（服务器可能需要用到的基础类）
        ("vectordb_bench/backend/clients/api.py", "基础接口定义"),
        ("vectordb_bench/models.py", "数据模型（如果服务器用到）"),
        
        # 依赖文件
        ("pyproject.toml", "Python项目配置"),
        ("requirements.txt", "Python依赖列表")
    ]
    
    for file_path, description in server_files:
        print(f"   📄 {file_path}")
        print(f"      {description}")
    
    print("\n📦 服务器端Python依赖:")
    server_deps = [
        "fastapi - Web框架",
        "uvicorn - ASGI服务器", 
        "faiss-cpu 或 faiss-gpu - FAISS核心库",
        "numpy - 数值计算",
        "pydantic - 数据验证"
    ]
    
    for dep in server_deps:
        print(f"   🔸 {dep}")

def create_deployment_guide():
    """创建完整的部署指南"""
    print("\n🚀 完整部署指南")
    print("=" * 30)
    
    print("📋 步骤1: 准备服务器端")
    print("-" * 25)
    
    server_steps = [
        "1. 在服务器上安装Python 3.8+",
        "2. 安装FAISS库: pip install faiss-cpu (或 faiss-gpu)",
        "3. 安装FastAPI: pip install fastapi uvicorn",
        "4. 复制必需的服务器端文件",
        "5. 启动FAISS服务器: uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002"
    ]
    
    for step in server_steps:
        print(f"   {step}")
    
    print("\n📋 步骤2: 准备客户机端")
    print("-" * 25)
    
    client_steps = [
        "1. 在客户机上安装Python 3.8+",
        "2. 复制完整的VectorDBBench框架文件",
        "3. 安装客户端依赖: pip install -e .",
        "4. 准备测试数据集（或配置远程数据源）",
        "5. 修改配置指向远程FAISS服务器",
        "6. 运行基准测试"
    ]
    
    for step in client_steps:
        print(f"   {step}")
    
    print("\n📋 步骤3: 配置远程连接")
    print("-" * 28)
    
    config_example = '''
    # 在客户机上创建配置
    from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
    from vectordb_bench.backend.clients.api import MetricType
    
    db_config = FaissConfig(
        host='服务器IP地址',      # 例如: '*************'
        port=8002,               # FAISS服务器端口
        index_type='Flat',       # 或者 'IVF1024' 等
        db_label='remote_faiss_test'
    )
    
    db_case_config = FaissDBCaseConfig(
        metric_type=MetricType.COSINE
    )
    '''
    
    print(config_example)

def create_minimal_client_package():
    """创建最小化客户端包文件列表"""
    print("\n📦 最小化客户端包")
    print("=" * 30)
    
    print("🗂️ 如果只进行远程FAISS测试，最小文件集合:")
    
    minimal_files = [
        # 核心框架（必需）
        "vectordb_bench/__init__.py",
        "vectordb_bench/__main__.py", 
        "vectordb_bench/config.py",
        "vectordb_bench/models.py",
        "vectordb_bench/metric.py",
        "vectordb_bench/interface.py",
        
        # CLI模块
        "vectordb_bench/cli/__init__.py",
        "vectordb_bench/cli/cli.py",
        
        # 后端核心
        "vectordb_bench/backend/__init__.py",
        "vectordb_bench/backend/task_runner.py",
        "vectordb_bench/backend/assembler.py", 
        "vectordb_bench/backend/data_source.py",
        "vectordb_bench/backend/result_collector.py",
        
        # 客户端基础
        "vectordb_bench/backend/clients/__init__.py",
        "vectordb_bench/backend/clients/api.py",
        
        # FAISS远程客户端
        "vectordb_bench/backend/clients/faiss/__init__.py",
        "vectordb_bench/backend/clients/faiss/faiss.py",
        "vectordb_bench/backend/clients/faiss/config.py",
        
        # 用户脚本
        "run_real_vectordb_benchmark.py",
        
        # 配置文件
        "pyproject.toml"
    ]
    
    print(f"📊 总计: {len(minimal_files)} 个文件")
    print("\n详细列表:")
    for file in minimal_files:
        print(f"   📄 {file}")

def create_environment_setup_script():
    """创建环境设置脚本"""
    print("\n🔧 环境设置脚本")
    print("=" * 25)
    
    # 客户端环境设置
    client_setup = '''
    #!/bin/bash
    # 客户端环境设置脚本 (setup_client.sh)
    
    echo "🔧 设置VectorDBBench客户端环境"
    
    # 检查Python版本
    python3 --version
    
    # 创建虚拟环境
    python3 -m venv vdbench-client-env
    source vdbench-client-env/bin/activate
    
    # 安装基础依赖
    pip install --upgrade pip
    
    # 安装VectorDBBench依赖
    pip install requests
    pip install pandas
    pip install numpy
    pip install pydantic
    pip install click
    pip install pyyaml
    pip install tqdm
    
    # 如果有pyproject.toml，使用editable install
    if [ -f "pyproject.toml" ]; then
        pip install -e .
    fi
    
    echo "✅ 客户端环境设置完成"
    '''
    
    # 服务器环境设置
    server_setup = '''
    #!/bin/bash
    # 服务器端环境设置脚本 (setup_server.sh)
    
    echo "🔧 设置FAISS服务器环境"
    
    # 检查Python版本
    python3 --version
    
    # 创建虚拟环境
    python3 -m venv faiss-server-env
    source faiss-server-env/bin/activate
    
    # 安装基础依赖
    pip install --upgrade pip
    
    # 安装FAISS服务器依赖
    pip install fastapi
    pip install uvicorn[standard]
    pip install faiss-cpu  # 或者 faiss-gpu
    pip install numpy
    pip install pydantic
    
    echo "✅ 服务器环境设置完成"
    echo "启动服务器:"
    echo "uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002"
    '''
    
    print("📄 客户端设置脚本 (setup_client.sh):")
    print(client_setup)
    
    print("\n📄 服务器设置脚本 (setup_server.sh):")
    print(server_setup)

def create_network_considerations():
    """网络和安全考虑"""
    print("\n🌐 网络和安全考虑")
    print("=" * 30)
    
    network_considerations = [
        ("🔥 防火墙配置", [
            "服务器端开放8002端口（或自定义端口）",
            "确保客户机可以访问服务器的指定端口",
            "考虑使用iptables或云服务商的安全组规则"
        ]),
        ("🔒 安全建议", [
            "在生产环境中添加认证机制",
            "使用HTTPS而非HTTP（需要修改服务器代码）",
            "限制访问源IP范围",
            "监控服务器资源使用情况"
        ]),
        ("📊 性能优化", [
            "服务器端使用GPU版本的FAISS（如果有GPU）",
            "调整uvicorn的worker数量",
            "考虑使用nginx作为反向代理",
            "监控网络延迟对测试结果的影响"
        ]),
        ("🔍 故障排除", [
            "检查防火墙和网络连通性",
            "验证服务器端口是否正确监听",
            "检查客户端和服务器的依赖版本",
            "查看服务器日志排除FAISS相关错误"
        ])
    ]
    
    for category, items in network_considerations:
        print(f"\n{category}:")
        for item in items:
            print(f"   • {item}")

def create_testing_workflow():
    """创建测试工作流程"""
    print("\n🔄 测试工作流程")
    print("=" * 25)
    
    workflow_steps = [
        ("1️⃣ 服务器启动", [
            "ssh到服务器",
            "激活Python环境",
            "启动FAISS服务: uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002",
            "验证服务启动: curl http://服务器IP:8002/docs"
        ]),
        ("2️⃣ 客户端准备", [
            "在客户机上准备VectorDBBench环境",
            "配置远程服务器地址",
            "准备测试数据集（或配置数据源）",
            "验证网络连通性"
        ]),
        ("3️⃣ 连接测试", [
            "运行简单的连接测试",
            "验证FAISS服务器响应",
            "测试基本的插入和搜索功能",
            "确认配置正确"
        ]),
        ("4️⃣ 基准测试", [
            "运行完整的基准测试",
            "监控服务器资源使用",
            "收集性能数据",
            "生成测试报告"
        ]),
        ("5️⃣ 结果分析", [
            "分析QPS、时延、召回率等指标",
            "比较本地和远程性能差异",
            "识别性能瓶颈",
            "生成测试报告"
        ])
    ]
    
    for phase, steps in workflow_steps:
        print(f"\n{phase}:")
        for step in steps:
            print(f"   • {step}")

if __name__ == "__main__":
    print("🎯 远程FAISS基准测试完整部署指南")
    print("=" * 55)
    print()
    
    analyze_client_requirements()
    analyze_server_requirements()
    create_deployment_guide()
    create_minimal_client_package()
    create_environment_setup_script()
    create_network_considerations()
    create_testing_workflow()
    
    print("\n🎊 部署指南总结:")
    print("=" * 25)
    print("✅ 服务器端: 最小化安装，只需FAISS服务器组件")
    print("✅ 客户端: 完整VectorDBBench框架，但无需本地FAISS")
    print("✅ 网络配置: 开放端口，确保连通性")
    print("✅ 测试流程: 分步验证，确保每个环节正常")
    print("✅ 这种架构可以有效分离计算资源和测试控制")
