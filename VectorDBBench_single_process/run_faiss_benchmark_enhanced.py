#!/usr/bin/env python3
"""
使用真实数据集运行 VectorDBBench FAISS 基准测试
支持本地和远程 FAISS 服务器连接
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime
import argparse

# 设置数据集路径
os.environ['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='VectorDBBench FAISS 基准测试')
    parser.add_argument('--mode', choices=['local', 'remote'], default='local',
                       help='运行模式: local (本地FAISS) 或 remote (远程FAISS服务器)')
    parser.add_argument('--uri', type=str, default='http://127.0.0.1:8002',
                       help='远程FAISS服务器URI (仅在remote模式下使用)')
    parser.add_argument('--host', type=str, default='127.0.0.1',
                       help='远程FAISS服务器主机 (仅在remote模式下使用)')
    parser.add_argument('--port', type=int, default=8002,
                       help='远程FAISS服务器端口 (仅在remote模式下使用)')
    parser.add_argument('--index-type', type=str, default='Flat',
                       choices=['Flat', 'IVF1024', 'IVF2048', 'IVF4096'],
                       help='远程FAISS索引类型 (仅在remote模式下使用)')
    return parser.parse_args()

def parse_uri(uri: str) -> tuple[str, int]:
    """解析 URI 获取 host 和 port"""
    import re
    # 解析类似 'http://***********:8002' 的 URI
    match = re.match(r'https?://([^:]+):(\d+)', uri)
    if match:
        host = match.group(1)
        port = int(match.group(2))
        return host, port
    else:
        # 如果没有协议前缀，尝试解析 host:port
        match = re.match(r'([^:]+):(\d+)', uri)
        if match:
            host = match.group(1)
            port = int(match.group(2))
            return host, port
        else:
            raise ValueError(f"无法解析 URI: {uri}。格式应为 'http://host:port' 或 'host:port'")

def run_local_faiss_benchmark():
    """使用本地 FAISS 运行基准测试"""
    print("🏠 VectorDBBench 本地 FAISS 基准测试")
    print("=" * 60)
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
        from vectordb_bench.backend.cases import CaseType
        from vectordb_bench.backend.clients.api import MetricType
        
        print("✅ VectorDBBench 本地 FAISS 模块加载成功")
        
        # 本地测试配置
        test_configs = [
            {
                "name": "本地 FAISS HNSW 小规模测试 (50K)",
                "case_type": "Performance1536D50K",
                "ef_search": 64,
                "description": "50K 向量，1536维，COSINE距离，本地HNSW索引"
            },
            {
                "name": "本地 FAISS HNSW 中规模测试 (500K)", 
                "case_type": "Performance1536D500K",
                "ef_search": 128,
                "description": "500K 向量，1536维，COSINE距离，本地HNSW索引"
            }
        ]
        
        return run_tests_with_configs(test_configs, 'local')
        
    except Exception as e:
        print(f"❌ 本地 FAISS 系统错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_remote_faiss_benchmark(host: str, port: int, index_type: str):
    """使用远程 FAISS 服务器运行基准测试"""
    print("🌐 VectorDBBench 远程 FAISS 基准测试")
    print("=" * 60)
    print(f"🔗 连接目标: {host}:{port}")
    print(f"📊 索引类型: {index_type}")
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
        from vectordb_bench.backend.cases import CaseType
        from vectordb_bench.backend.clients.api import MetricType
        
        print("✅ VectorDBBench 远程 FAISS 模块加载成功")
        
        # 远程测试配置
        test_configs = [
            {
                "name": f"远程 FAISS {index_type} 小规模测试 (50K)",
                "case_type": "Performance1536D50K",
                "description": f"50K 向量，1536维，COSINE距离，远程{index_type}索引",
                "host": host,
                "port": port,
                "index_type": index_type
            },
            {
                "name": f"远程 FAISS {index_type} 中规模测试 (500K)", 
                "case_type": "Performance1536D500K",
                "description": f"500K 向量，1536维，COSINE距离，远程{index_type}索引",
                "host": host,
                "port": port,
                "index_type": index_type
            }
        ]
        
        return run_tests_with_configs(test_configs, 'remote')
        
    except Exception as e:
        print(f"❌ 远程 FAISS 系统错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_tests_with_configs(test_configs: list, mode: str):
    """运行测试配置"""
    from vectordb_bench.cli.cli import run
    from vectordb_bench.backend.clients import DB
    from vectordb_bench.backend.clients.api import MetricType
    
    results = []
    
    for config in test_configs:
        print(f"\n🎯 执行测试: {config['name']}")
        print(f"   📋 描述: {config['description']}")
        
        if mode == 'local':
            # 本地 FAISS 配置
            from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
            
            db_config = FaissLocalConfig(
                db_label=f"faiss_local_{config['case_type'].lower()}_{int(time.time())}",
                index_type="HNSW"
            )
            
            db_case_config = HNSWConfig(
                m=16,
                ef_construction=200,
                ef_search=config['ef_search'],
                metric_type=MetricType.COSINE,
            )
            
            db_type = DB.FaissLocal
            
            print(f"   🔧 本地 FAISS 配置:")
            print(f"      索引类型: {db_config.index_type}")
            print(f"      HNSW-m: {db_case_config.m}")
            print(f"      ef_construction: {db_case_config.ef_construction}")
            print(f"      ef_search: {db_case_config.ef_search}")
            print(f"      距离度量: {db_case_config.metric_type}")
            
        else:  # remote
            # 远程 FAISS 配置
            from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
            
            db_config = FaissConfig(
                host=config['host'],
                port=config['port'],
                index_type=config['index_type']
            )
            
            db_case_config = FaissDBCaseConfig(
                metric_type=MetricType.COSINE
            )
            
            db_type = DB.Faiss
            
            print(f"   🔧 远程 FAISS 配置:")
            print(f"      服务器地址: {db_config.host}:{db_config.port}")
            print(f"      索引类型: {db_config.index_type}")
            print(f"      距离度量: {db_case_config.metric_type}")
        
        # 测试参数 - 添加正确的数据集配置
        test_params = {
            'case_type': config['case_type'],
            'dataset_name': 'openai_small_50k',  # 使用有完整数据的数据集
            'k': 100,
            'num_concurrency': [1, 4, 8],  # 适度的并发度
            'concurrency_duration': 60,    # 较短的测试时间
            'concurrency_timeout': 3600,
            'db_label': db_config.db_label if hasattr(db_config, 'db_label') else f"faiss_{mode}_{int(time.time())}",
            'load': True,
            'search_serial': True,
            'search_concurrent': True,
            'drop_old': True,
            'dry_run': False,
            'task_label': f'FAISS_{mode.title()}_Data_{config["case_type"]}',
            'custom_case': {}
        }
        
        print(f"\n   🚀 开始基准测试...")
        print(f"      运行模式: {mode}")
        print(f"      案例类型: {test_params['case_type']}")
        print(f"      k值: {test_params['k']}")
        print(f"      并发度: {test_params['num_concurrency']}")
        print(f"      测试时长: {test_params['concurrency_duration']}s")
        print("   " + "="*50)
        
        start_time = time.time()
        
        try:
            # 运行测试
            run(
                db=db_type,
                db_config=db_config,
                db_case_config=db_case_config,
                **test_params
            )
            
            test_time = time.time() - start_time
            print(f"\n   ✅ 测试完成，耗时: {test_time:.2f}s")
            
            # 记录结果
            results.append({
                "config": config,
                "test_time": test_time,
                "status": "success",
                "mode": mode
            })
            
        except Exception as e:
            test_time = time.time() - start_time
            print(f"\n   ❌ 测试失败: {e}")
            
            results.append({
                "config": config,
                "test_time": test_time,
                "status": "failed",
                "error": str(e),
                "mode": mode
            })
            
            # 打印更详细的错误信息
            import traceback
            print("   详细错误信息:")
            traceback.print_exc()
            
            # 继续下一个测试
            continue
    
    # 生成测试摘要
    print_test_summary(results, mode)
    
    return len([r for r in results if r["status"] == "success"]) > 0

def print_test_summary(results: list, mode: str):
    """打印测试摘要"""
    print(f"\n📊 {mode.title()} FAISS 测试摘要:")
    print("="*50)
    
    successful_tests = [r for r in results if r["status"] == "success"]
    failed_tests = [r for r in results if r["status"] == "failed"]
    
    print(f"✅ 成功测试: {len(successful_tests)}")
    print(f"❌ 失败测试: {len(failed_tests)}")
    
    if successful_tests:
        total_time = sum(r["test_time"] for r in successful_tests)
        print(f"⏱️  总测试时间: {total_time:.2f}s")
        
        for result in successful_tests:
            config = result["config"]
            print(f"   🎯 {config['name']}: {result['test_time']:.2f}s")
    
    if failed_tests:
        print(f"\n❌ 失败的测试:")
        for result in failed_tests:
            config = result["config"]
            print(f"   💥 {config['name']}: {result.get('error', 'Unknown error')}")

def check_dataset_files():
    """检查数据集文件的详细信息"""
    print("\n🔍 检查数据集文件详情...")
    
    dataset_path = Path(os.environ['DATASET_LOCAL_DIR'])
    
    if not dataset_path.exists():
        print(f"⚠️  数据集路径不存在: {dataset_path}")
        return
    
    # 检查 OpenAI 数据集
    openai_small = dataset_path / 'openai' / 'openai_small_50k'
    if openai_small.exists():
        print(f"\n📁 {openai_small.name}:")
        for file in openai_small.glob('*.parquet'):
            size_mb = file.stat().st_size / 1024 / 1024
            print(f"   📄 {file.name}: {size_mb:.1f}MB")
    
    # 检查 OpenAI 中规模数据集
    openai_medium = dataset_path / 'openai' / 'openai_medium_500k'
    if openai_medium.exists():
        print(f"\n📁 {openai_medium.name}:")
        for file in openai_medium.glob('*.parquet'):
            size_mb = file.stat().st_size / 1024 / 1024
            print(f"   📄 {file.name}: {size_mb:.1f}MB")

def check_remote_server(host: str, port: int):
    """检查远程服务器连接"""
    print(f"\n🔍 检查远程 FAISS 服务器连接...")
    
    try:
        import requests
        url = f"http://{host}:{port}/docs"
        
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ 远程 FAISS 服务器连接成功: {host}:{port}")
                print(f"   📖 API 文档: {url}")
                return True
            else:
                print(f"⚠️  远程服务器响应异常: HTTP {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到远程 FAISS 服务器: {host}:{port}")
            print(f"   错误: {e}")
            print(f"\n💡 请确保在 {host} 上启动了 FAISS 服务器:")
            print(f"   python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port {port}")
            return False
            
    except ImportError:
        print("❌ 缺少 requests 依赖，无法检查服务器状态")
        print("   安装: pip install requests")
        return False

if __name__ == "__main__":
    print("🎯 VectorDBBench FAISS 基准测试 (支持本地和远程)")
    print("=" * 65)
    
    # 解析命令行参数
    args = parse_args()
    
    print(f"🔧 运行模式: {args.mode}")
    
    # 检查数据集
    check_dataset_files()
    
    success = False
    
    if args.mode == 'local':
        # 本地 FAISS 模式
        success = run_local_faiss_benchmark()
        
    else:  # remote
        # 远程 FAISS 模式
        if args.uri:
            try:
                host, port = parse_uri(args.uri)
            except ValueError as e:
                print(f"❌ URI 解析错误: {e}")
                sys.exit(1)
        else:
            host, port = args.host, args.port
        
        print(f"🌐 目标服务器: {host}:{port}")
        print(f"📊 索引类型: {args.index_type}")
        
        # 检查远程服务器连接
        if check_remote_server(host, port):
            success = run_remote_faiss_benchmark(host, port, args.index_type)
        else:
            print("❌ 远程服务器检查失败，无法继续测试")
            success = False
    
    print(f"\n{'🎉 测试完成!' if success else '💥 测试失败!'}")
    
    if success:
        print(f"\n📊 查看详细结果:")
        print(f"   结果文件通常保存在 results/ 目录下")
        print(f"   使用命令查看: ls -la results/ | head -10")
    
    sys.exit(0 if success else 1)
