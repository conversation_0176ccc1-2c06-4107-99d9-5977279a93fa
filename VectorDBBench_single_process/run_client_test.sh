#!/bin/bash
echo "准备运行客户端测试..."
echo "设置环境变量..."
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset

echo "开始性能测试："
echo "1. 启动256并发测试..."
python3.11 -m vectordb_bench.cli.vectordbbench faissremote --uri http://***********:8005 --case-type Performance768D10M --index-type HNSW --m 30 --ef-construction 360 --concurrency-duration 30 --ef-search 100 --num-concurrency 128,256,384,512 --skip-load --skip-search-serial

echo "2. 启动512并发测试..."
python3.11 -m vectordb_bench.cli.vectordbbench faissremote --uri http://***********:8005 --case-type Performance768D10M --index-type HNSW --m 30 --ef-construction 360 --concurrency-duration 30 --ef-search 256 --num-concurrency 128,256,384,512 --skip-load --skip-search-serial

echo "测试完成！" 