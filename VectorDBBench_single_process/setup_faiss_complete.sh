#!/bin/bash
"""
一键设置FAISS环境 - 解决所有配置问题
"""

echo "🎯 一键设置FAISS环境"
echo "===================="

# 1. 设置AWS配置文件（避免配置错误）
echo "🔧 设置AWS配置..."
mkdir -p ~/.aws

cat > ~/.aws/config << 'EOF'
[default]
region = us-east-1
output = json
EOF

cat > ~/.aws/credentials << 'EOF' 
[default]
aws_access_key_id = fake_key_for_vectordb_bench
aws_secret_access_key = fake_secret_for_vectordb_bench
EOF

echo "✅ AWS配置创建完成"

# 2. 设置环境变量
export DATASET_LOCAL_DIR="/home/<USER>/VectorDBBench/dataset"
export AWS_ACCESS_KEY_ID="fake_key_for_vectordb_bench"
export AWS_SECRET_ACCESS_KEY="fake_secret_for_vectordb_bench"
export AWS_DEFAULT_REGION="us-east-1"
export AWS_PROFILE="default"

echo "✅ 环境变量设置完成"
echo "   📂 数据集: $DATASET_LOCAL_DIR"
echo "   🔧 AWS配置: 模拟模式"

# 3. 验证数据集
echo ""
echo "🔍 验证数据集..."
if [ -d "$DATASET_LOCAL_DIR/openai" ]; then
    echo "✅ OpenAI数据集存在"
    ls -la "$DATASET_LOCAL_DIR/openai/" | head -5
else
    echo "❌ OpenAI数据集不存在: $DATASET_LOCAL_DIR/openai"
    echo "💡 请确保数据集已下载到正确位置"
    exit 1
fi

# 4. 验证FAISS命令
echo ""
echo "🔍 验证FAISS命令..."
if command -v vectordbbench >/dev/null 2>&1; then
    echo "✅ vectordbbench命令可用"
    echo "📋 可用的FAISS命令:"
    vectordbbench --help | grep faiss || echo "   faisslocalhnsw, faisslocalivfflat, faisslocalivfpq, faissremote"
else
    echo "❌ vectordbbench命令不可用"
    echo "💡 请确保已正确安装VectorDBBench"
    exit 1
fi

# 5. 创建永久环境脚本
echo ""
echo "📝 创建永久环境脚本..."

cat > /home/<USER>/VectorDBBench/setup_faiss_env.sh << 'EOF'
#!/bin/bash
# FAISS VectorDBBench 环境设置

# 设置数据集路径
export DATASET_LOCAL_DIR="/home/<USER>/VectorDBBench/dataset"

# 设置AWS模拟配置（避免真实AWS访问）
export AWS_ACCESS_KEY_ID="fake_key_for_vectordb_bench"
export AWS_SECRET_ACCESS_KEY="fake_secret_for_vectordb_bench"
export AWS_DEFAULT_REGION="us-east-1"
export AWS_PROFILE="default"

echo "✅ FAISS环境已设置"
echo "   📂 数据集: $DATASET_LOCAL_DIR"
echo "   🔧 AWS: 模拟模式"

echo ""
echo "🚀 现在可以运行FAISS测试:"
echo "   # FAISS Local (推荐)"
echo "   vectordbbench faisslocalhnsw --case-type Performance1536D50K"
echo ""
echo "   # FAISS Remote (需要服务器)"
echo "   python -m vectordb_bench.cli.vectordbbench faissremote --uri localhost:8011 --case-type Performance1536D50K"
EOF

chmod +x /home/<USER>/VectorDBBench/setup_faiss_env.sh

echo "✅ 永久环境脚本: /home/<USER>/VectorDBBench/setup_faiss_env.sh"

echo ""
echo "🎊 环境设置完成！"
echo "=================="
echo ""
echo "🚀 快速测试命令:"
echo "   vectordbbench faisslocalhnsw \\"
echo "       --case-type Performance1536D50K \\"
echo "       --m 16 --ef-construction 200 --ef-search 100 \\"
echo "       --concurrency-duration 30 \\"
echo "       --num-concurrency 1"
echo ""
echo "💡 下次使用，只需运行:"
echo "   source /home/<USER>/VectorDBBench/setup_faiss_env.sh"
