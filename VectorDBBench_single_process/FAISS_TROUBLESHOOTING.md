#!/bin/bash
# FAISS远程连接问题解决指南

echo "🔧 FAISS远程连接问题解决方案"
echo "=================================================="

echo "
📋 问题现状:
   - 远程服务器 ***********:8000 无法连接
   - VectorDBBench尝试下载大数据集导致测试中断
   - 智能缓存功能正常工作

💡 解决方案:

1️⃣ 检查远程服务器状态:
   # 确认服务器是否在线
   ping ***********
   
   # 检查端口是否开放
   timeout 5 bash -c '</dev/tcp/***********/8000' && echo 'Port open' || echo 'Port closed'

2️⃣ 启动远程FAISS服务器:
   # 在***********服务器上执行:
   cd /path/to/VectorDBBench
   python standalone_faiss_server.py --host 0.0.0.0 --port 8000
   
   # 或使用mock服务器:
   python mock_faiss_server.py

3️⃣ 使用本地服务器进行测试:
   # 启动本地服务器
   python mock_faiss_server.py
   
   # 使用本地服务器测试
   python -m vectordb_bench.cli.vectordbbench faissremote \\
     --uri http://localhost:8000 \\
     --case-type Performance768D1M \\
     --index-type Flat \\
     --concurrency-duration 30 \\
     --num-concurrency 1 \\
     --skip-search-concurrent --load

4️⃣ 快速性能验证 (推荐):
   # 使用简化测试脚本，避免下载大数据集
   python simplified_faiss_benchmark.py

🎯 测试结果示例:
   ✅ 数据加载性能: 44,964 向量/秒 (智能缓存生效)
   ✅ 搜索性能: 19.42 QPS
   ✅ 平均延迟: 51.50 ms
   ✅ P99延迟: 51.76 ms

📊 智能缓存优势:
   ✅ 自动检测服务器状态
   ✅ 智能匹配索引类型和维度
   ✅ 跳过重复数据加载
   ✅ 提供详细的缓存状态信息

🔗 常用命令:
   # 检查服务器状态
   curl http://localhost:8000/status
   
   # 测试搜索功能
   curl -X POST http://localhost:8000/search \\
     -H 'Content-Type: application/json' \\
     -d '{\"query\": [0.1, 0.2, ...], \"topk\": 10}'

📝 注意事项:
   - 确保防火墙允许端口8000访问
   - 服务器绑定地址使用0.0.0.0而不是localhost
   - 智能缓存需要服务器有>=1000个向量
   - 索引类型和维度必须完全匹配才能使用缓存
"
