#!/bin/bash

set -e  # 遇到错误时退出

# 设置环境变量
export DATASET_LOCAL_DIR="/nas/yvan.chen/milvus/dataset"
export PYTHONUNBUFFERED=1

echo "=== 开始 FAISS 基准测试 ==="
echo "时间: $(date)"
echo "数据集路径: $DATASET_LOCAL_DIR"
echo "工作目录: $(pwd)"
echo ""

# 创建日志目录
mkdir -p logs results

echo "运行 FAISS HNSW 基准测试..."
echo "参数: Performance1536D50K, m=16, ef_construction=200, ef_search=64"
echo ""

# 运行测试并捕获输出
vectordbbench faisslocalhnsw \
    --case-type Performance1536D50K \
    --m 16 \
    --ef-construction 200 \
    --ef-search 64 \
    --k 10 \
    --num-concurrency "1" \
    --concurrency-duration 10 \
    --db-label "faiss_test_$(date +%H%M%S)" 2>&1 | tee logs/faiss_test_$(date +%H%M%S).log

echo ""
echo "=== 测试完成 ==="

# 检查结果
echo "检查结果文件..."
if [ -d "results" ] && [ "$(ls -A results 2>/dev/null)" ]; then
    echo "结果文件:"
    ls -la results/
else
    echo "没有找到结果文件"
fi

echo ""
echo "检查日志文件..."
if [ -f "logs/vectordb_bench.log" ] && [ -s "logs/vectordb_bench.log" ]; then
    echo "主日志文件内容:"
    tail -20 logs/vectordb_bench.log
else
    echo "主日志文件为空或不存在"
fi
