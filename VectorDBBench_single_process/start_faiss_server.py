#!/usr/bin/env python3
"""
独立的增强版 FAISS 服务器启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🚀 启动 FAISS 增强服务器...")
    
    # 设置环境变量
    env = os.environ.copy()
    env["DATASET_LOCAL_DIR"] = "/nas/yvan.chen/milvus/dataset"
    
    # 寻找 enhanced_server.py 文件
    possible_paths = [
        "/nas/brian.mao/VectorDBBench/vectordb_bench/backend/clients/faiss/enhanced_server.py",
        "/home/<USER>/VectorDBBench/enhanced_server.py",
        "/home/<USER>/VectorDBBench/vectordb_bench/backend/clients/faiss/enhanced_server.py"
    ]
    
    server_script = None
    for path in possible_paths:
        if os.path.exists(path):
            server_script = path
            break
    
    if not server_script:
        print("❌ 找不到 enhanced_server.py 文件")
        print("请确保文件存在于以下位置之一:")
        for path in possible_paths:
            print(f"   {path}")
        return 1
    
    print(f"📁 使用服务器脚本: {server_script}")
    print(f"📂 数据集路径: {env['DATASET_LOCAL_DIR']}")
    
    # 启动服务器
    try:
        # 切换到脚本所在目录
        script_dir = os.path.dirname(server_script)
        os.chdir(script_dir)
        
        # 启动服务器
        cmd = [sys.executable, server_script]
        print(f"🔧 执行命令: {' '.join(cmd)}")
        print(f"📍 工作目录: {script_dir}")
        
        subprocess.run(cmd, env=env)
        
    except KeyboardInterrupt:
        print("\n⏹️  服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
