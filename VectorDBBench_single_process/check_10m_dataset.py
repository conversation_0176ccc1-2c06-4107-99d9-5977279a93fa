#!/usr/bin/env python3
"""
检查服务器上的数据集配置和真实的10M数据集加载
"""

import json
import time

# 兼容Python 2/3的urllib
try:
    from urllib.request import urlopen, Request
    from urllib.error import URLError, HTTPError
except ImportError:
    from urllib2 import urlopen, Request, URLError, HTTPError

def http_request(url, data=None, timeout=30):
    """发送HTTP请求"""
    try:
        if data:
            data_json = json.dumps(data).encode('utf-8')
            req = Request(url, data=data_json)
            req.add_header('Content-Type', 'application/json')
        else:
            req = Request(url)
        
        response = urlopen(req, timeout=timeout)
        response_data = response.read().decode('utf-8')
        return json.loads(response_data), response.getcode()
    except Exception as e:
        print(f"请求错误: {e}")
        return None, 0

def check_server_datasets():
    """检查服务器上的数据集配置"""
    base_url = "http://10.1.180.71:8001"
    
    print("🔍 检查服务器数据集配置")
    print("=" * 60)
    
    # 获取服务器信息
    data, code = http_request(f"{base_url}/info")
    
    if code == 200:
        print("📊 当前服务器配置:")
        
        available_datasets = data.get('可用数据集', {})
        if not available_datasets:
            available_datasets = data.get('available_datasets', {})
        
        print(f"\n可用数据集配置:")
        for case_name, dataset_info in available_datasets.items():
            status = "✅" if dataset_info.get('available', False) else "❌"
            print(f"   {status} {case_name}:")
            print(f"      路径: {dataset_info.get('path', 'N/A')}")
            print(f"      维度: {dataset_info.get('dimension', 'N/A')}")
            print(f"      预期向量数: {dataset_info.get('expected_vectors', 'N/A'):,}")
            print()
        
        # 检查是否有Performance768D10M配置
        has_10m = any('768D10M' in case_name for case_name in available_datasets.keys())
        if has_10m:
            print("✅ 发现 Performance768D10M 配置")
        else:
            print("❌ 缺少 Performance768D10M 配置")
            print("💡 需要在服务器中添加 cohere_large_10m 数据集映射")
        
        return available_datasets
    else:
        print(f"❌ 无法获取服务器信息，状态码: {code}")
        return {}

def test_10m_dataset_loading():
    """测试真正的10M数据集加载"""
    base_url = "http://10.1.180.71:8001"
    
    print("\n🚀 尝试加载真正的 Cohere 10M 数据集")
    print("=" * 60)
    
    # 创建768维索引，应该自动匹配到10M数据集
    create_data = {
        "dim": 768,
        "index_type": "HNSW", 
        "m": 16,
        "ef_construction": 200
    }
    
    print("📋 重新创建索引以触发10M数据集加载...")
    print(f"索引参数: {create_data}")
    
    start_time = time.time()
    data, code = http_request(f"{base_url}/create_index", create_data, timeout=3600)  # 1小时超时
    end_time = time.time()
    
    if code == 200:
        duration = end_time - start_time
        print(f"✅ 索引重新创建成功!")
        print(f"⏱️ 创建耗时: {duration:.2f} 秒 ({duration/60:.1f} 分钟)")
        
        # 立即检查状态
        data, code = http_request(f"{base_url}/status")
        if code == 200:
            total_vectors = data.get('total_vectors', 0)
            print(f"📊 加载的向量数: {total_vectors:,}")
            
            if total_vectors >= 10000000:
                print("🎉 成功加载 10M+ 向量！")
                return True
            elif total_vectors >= 1000000:
                print("✅ 加载了 1M+ 向量 (可能是 cohere_medium_1m)")
            else:
                print(f"⚠️ 向量数量较少: {total_vectors:,}")
        
        return False
    else:
        print(f"❌ 索引创建失败，状态码: {code}")
        return False

def main():
    # 1. 检查服务器数据集配置
    datasets = check_server_datasets()
    
    # 2. 尝试加载10M数据集
    success = test_10m_dataset_loading()
    
    print("\n" + "=" * 60)
    print("📝 总结:")
    
    if '768D10M' in str(datasets):
        print("✅ 服务器配置包含 10M 数据集映射")
    else:
        print("❌ 服务器配置缺少 10M 数据集映射")
        print("💡 建议: 在 smart_faiss_server.py 中添加:")
        print('   "Performance768D10M": {')
        print('       "path": "cohere/cohere_large_10m",')
        print('       "dimension": 768,')
        print('       "vectors": 10000000')
        print('   }')
    
    if success:
        print("✅ 成功加载 10M 向量数据集")
    else:
        print("⚠️ 未能加载完整的 10M 数据集")

if __name__ == "__main__":
    main()
