#!/usr/bin/env python3
"""
简单的FAISS搜索性能测试，使用远程服务器
"""

import sys
import os
import time
import numpy as np

sys.path.append('/home/<USER>/VectorDBBench')

from vectordb_bench.backend.clients.faiss.faiss import FaissClient
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig

def search_performance_test():
    """搜索性能测试"""
    
    print("🎯 FAISS搜索性能测试")
    print("=" * 50)
    
    # 使用远程服务器配置
    db_config = FaissConfig(
        host='***********',
        port=8000,
        index_type='Flat'
    )
    
    db_case_config = FaissDBCaseConfig()
    
    try:
        client = FaissClient(
            dim=768,
            db_config=db_config,
            db_case_config=db_case_config,
            collection_name="perf_test"
        )
        
        print("📊 开始搜索性能测试...")
        
        with client.init():
            # 生成测试查询向量
            num_queries = 100
            queries = np.random.random((num_queries, 768)).tolist()
            
            print(f"🔍 执行 {num_queries} 次搜索测试...")
            
            # 预热
            warmup_query = queries[0]
            client.search_embedding(warmup_query, k=10)
            print("🔥 预热完成")
            
            # 性能测试
            start_time = time.time()
            results = []
            
            for i, query in enumerate(queries):
                try:
                    result = client.search_embedding(query, k=10)
                    results.append(result)
                    
                    if (i + 1) % 20 == 0:
                        elapsed = time.time() - start_time
                        qps = (i + 1) / elapsed
                        print(f"   进度: {i+1}/{num_queries}, QPS: {qps:.2f}")
                        
                except Exception as e:
                    print(f"   ❌ 搜索 {i+1} 失败: {e}")
                    continue
            
            total_time = time.time() - start_time
            successful_queries = len(results)
            avg_qps = successful_queries / total_time
            avg_latency = (total_time / successful_queries) * 1000  # ms
            
            print(f"\n📈 性能测试结果:")
            print(f"   ✅ 成功查询: {successful_queries}/{num_queries}")
            print(f"   ⚡ 平均QPS: {avg_qps:.2f}")
            print(f"   ⏱️  平均延迟: {avg_latency:.2f} ms")
            print(f"   🕒 总耗时: {total_time:.2f} 秒")
            
            # 检查结果质量
            if results:
                result_lens = [len(r) for r in results if r]
                if result_lens:
                    avg_results = sum(result_lens) / len(result_lens)
                    print(f"   📋 平均返回结果数: {avg_results:.1f}")
                
                # 显示一些示例结果
                print(f"\n🔍 示例搜索结果:")
                for i, result in enumerate(results[:3]):
                    if result:
                        print(f"   查询 {i+1}: 找到 {len(result)} 个相似向量, 最相似ID: {result[:3]}")
            
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return
    
    print("\n🎉 搜索性能测试完成!")

if __name__ == "__main__":
    search_performance_test()
