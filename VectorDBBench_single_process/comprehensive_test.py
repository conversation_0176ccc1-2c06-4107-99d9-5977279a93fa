#!/usr/bin/env python3
"""
VectorDBBench FAISS 缓存功能完整测试
模拟实际使用场景：多种索引类型和参数测试
"""

import sys
import numpy as np
import time
from pathlib import Path

# 添加项目路径
sys.path.append('/home/<USER>/VectorDBBench')

from vectordb_bench.backend.clients.faiss.faiss import FaissClient
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig

def test_scenario(name, index_type, m=16, ef_construction=200):
    """测试单个场景"""
    print(f"\n{'='*60}")
    print(f"🧪 测试场景: {name}")
    print(f"{'='*60}")
    
    # 创建配置
    db_config = FaissConfig(
        host="***********",
        port=8000,
        index_type=index_type
    )
    
    db_case_config = FaissDBCaseConfig(
        m=m,
        ef_construction=ef_construction
    )
    
    start_time = time.time()
    
    try:
        # 创建客户端
        print(f"🔧 创建 {index_type} 客户端...")
        client = FaissClient(
            dim=1536,
            db_config=db_config,
            db_case_config=db_case_config,
            collection_name=f"test_{index_type.lower()}",
            drop_old=False
        )
        
        # 模拟插入大量数据
        print(f"📝 模拟插入10,000个向量...")
        test_embeddings = np.random.random((10000, 1536)).tolist()
        metadata = list(range(10000))
        
        insert_start = time.time()
        result, error = client.insert_embeddings(test_embeddings, metadata)
        insert_time = time.time() - insert_start
        
        if error:
            print(f"❌ 插入失败: {error}")
            return False
        
        print(f"✅ 插入完成: {result} 个向量，耗时: {insert_time:.2f}秒")
        
        # 测试搜索
        print(f"🔍 测试向量搜索...")
        query_vector = np.random.random(1536).tolist()
        search_start = time.time()
        search_results = client.search_embedding(query_vector, k=10)
        search_time = time.time() - search_start
        
        print(f"✅ 搜索完成: 找到 {len(search_results)} 个结果，耗时: {search_time:.3f}秒")
        
        total_time = time.time() - start_time
        print(f"📊 场景总耗时: {total_time:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 场景测试失败: {e}")
        return False

def main():
    print("🚀 VectorDBBench FAISS 缓存功能完整测试")
    print("=" * 80)
    
    # 测试场景列表
    scenarios = [
        ("HNSW索引 - 默认参数", "HNSW", 16, 200),
        ("HNSW索引 - 相同参数(应该使用缓存)", "HNSW", 16, 200),
        ("HNSW索引 - 不同参数", "HNSW", 32, 400),
        ("Flat索引", "Flat", 16, 200),
    ]
    
    results = []
    
    for name, index_type, m, ef_construction in scenarios:
        success = test_scenario(name, index_type, m, ef_construction)
        results.append((name, success))
        
        # 稍等片刻，避免连接过于频繁
        time.sleep(2)
    
    # 测试结果汇总
    print(f"\n{'='*80}")
    print("📋 测试结果汇总")
    print(f"{'='*80}")
    
    for name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} {name}")
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 个场景成功")
    
    if success_count == total_count:
        print("🎉 所有测试通过！缓存功能工作正常！")
    else:
        print("⚠️ 部分测试失败，请检查日志")

if __name__ == "__main__":
    main()
