#!/usr/bin/env python3
"""
对比测试：运行本地FAISS和远程FAISS，找出差异
"""

import time
import sys
from pathlib import Path

def test_local_faiss():
    """测试本地FAISS是否能生成结果"""
    print("🔍 测试本地FAISS")
    print("=" * 30)
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, FaissLocalDBCaseConfig
        from vectordb_bench.backend.clients.api import MetricType
        
        db_config = FaissLocalConfig(
            index_type='Flat',
            db_label=f'local_test_{int(time.time())}'
        )
        
        db_case_config = FaissLocalDBCaseConfig(
            metric_type=MetricType.COSINE
        )
        
        print("✅ 本地FAISS配置创建成功")
        print(f"🚀 开始本地FAISS测试...")
        
        start_time = time.time()
        
        result = run(
            db=DB.FaissLocal,
            db_config=db_config,
            db_case_config=db_case_config,
            case_type='Performance1536D50K',
            dataset_name='openai_small_50k',
            k=10,
            num_concurrency=[1],
            concurrency_duration=10,
            concurrency_timeout=60,
            task_label='LocalFaissTest',
            dry_run=False,
            load=True,
            search_serial=True,
            search_concurrent=False,
            drop_old=True
        )
        
        test_time = time.time() - start_time
        print(f"✅ 本地FAISS测试完成，耗时: {test_time:.2f}s")
        
        # 检查结果
        results_path = Path("vectordb_bench/results/FaissLocal")
        if results_path.exists():
            result_files = list(results_path.glob("*.json"))
            if result_files:
                latest_file = max(result_files, key=lambda x: x.stat().st_mtime)
                print(f"✅ 本地FAISS生成了结果文件: {latest_file}")
                
                # 简单检查内容
                import json
                with open(latest_file, 'r') as f:
                    data = json.load(f)
                
                if 'results' in data and data['results']:
                    metrics = data['results'][0].get('metrics', {})
                    print(f"   QPS: {metrics.get('qps', 'N/A')}")
                    print(f"   召回率: {metrics.get('recall', 'N/A')}")
                    print(f"   时延: {metrics.get('serial_latency_p99', 'N/A')}")
                    return True
            else:
                print("❌ 本地FAISS也没有生成结果文件")
        
        return False
        
    except Exception as e:
        print(f"❌ 本地FAISS测试失败: {e}")
        return False

def test_with_process_monitoring():
    """监控进程执行"""
    print(f"\n🔍 监控远程FAISS进程执行")
    print("=" * 40)
    
    try:
        from vectordb_bench.interface import benchmark_runner
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
        from vectordb_bench.backend.cases import CaseType
        from vectordb_bench.backend.clients.api import MetricType
        from vectordb_bench.models import TaskConfig, CaseConfig, ConcurrencySearchConfig
        
        # 清理之前的运行
        if benchmark_runner.has_running():
            benchmark_runner.stop_running()
            time.sleep(1)
        
        db_config = FaissConfig(
            host='127.0.0.1',
            port=8002,
            index_type='Flat',
            db_label=f'monitor_test_{int(time.time())}'
        )
        
        db_case_config = FaissDBCaseConfig(
            metric_type=MetricType.COSINE
        )
        
        case_config = CaseConfig(
            case_id=CaseType.Performance1536D50K,
            k=10,
            concurrency_search_config=ConcurrencySearchConfig(
                num_concurrency=[1],
                concurrency_duration=10,
                concurrency_timeout=60
            )
        )
        
        task_config = TaskConfig(
            db=DB.Faiss,
            db_config=db_config,
            db_case_config=db_case_config,
            case_config=case_config,
            stages=['drop_old', 'load', 'search_serial']
        )
        
        print("🚀 提交任务...")
        success = benchmark_runner.run([task_config], task_label='MonitorTest')
        
        if not success:
            print(f"❌ 任务提交失败: {benchmark_runner.latest_error}")
            return False
        
        print("✅ 任务提交成功，开始详细监控...")
        
        # 详细监控
        start_time = time.time()
        last_task_id = -1
        
        while benchmark_runner.has_running() and (time.time() - start_time) < 30:
            current_task = benchmark_runner.get_current_task_id()
            total_tasks = benchmark_runner.get_tasks_count()
            
            if current_task != last_task_id:
                print(f"   📊 任务进度变化: {current_task}/{total_tasks}")
                last_task_id = current_task
            
            if benchmark_runner.latest_error:
                print(f"   ⚠️  发现错误: {benchmark_runner.latest_error}")
                break
            
            time.sleep(0.5)
        
        end_time = time.time()
        print(f"✅ 监控结束，总时间: {end_time - start_time:.2f}s")
        
        if benchmark_runner.latest_error:
            print(f"❌ 最终错误: {benchmark_runner.latest_error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 进程监控失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔬 对比测试：本地vs远程FAISS")
    print("=" * 50)
    
    # 测试本地FAISS
    local_success = test_local_faiss()
    
    # 测试远程FAISS并监控
    remote_success = test_with_process_monitoring()
    
    print(f"\n📋 对比结果:")
    print("=" * 20)
    print(f"本地FAISS: {'✅' if local_success else '❌'}")
    print(f"远程FAISS: {'✅' if remote_success else '❌'}")
    
    if local_success and not remote_success:
        print(f"\n🎯 结论: 本地FAISS正常，远程FAISS有问题")
        print("可能原因:")
        print("• 远程客户端实现有bug")
        print("• 网络通信超时")
        print("• 多进程执行问题")
    elif not local_success and not remote_success:
        print(f"\n🎯 结论: 整个框架可能有问题")
        print("可能原因:")
        print("• 数据集问题")
        print("• 配置问题")
        print("• 环境问题")
    else:
        print(f"\n🎯 结论: 远程FAISS基本正常")
    
    return local_success or remote_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
