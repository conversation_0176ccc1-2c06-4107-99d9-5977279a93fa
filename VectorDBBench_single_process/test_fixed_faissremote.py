#!/usr/bin/env python3
"""
测试修复后的远程 FAISS CLI 命令
"""

import subprocess
import sys

def test_fixed_faissremote():
    """测试修复后的 faissremote 命令"""
    print("🔧 测试修复后的远程 FAISS 命令")
    print("=" * 40)
    
    # 测试命令（干运行模式）
    cmd = [
        "python", "-m", "vectordb_bench.cli.vectordbbench", "faissremote",
        "--uri", "http://***********:8002",
        "--case-type", "Performance1536D50K",
        "--index-type", "Flat",
        "--concurrency-duration", "300",
        "--num-concurrency", "8,16,32,64,128",
        "--k", "100",
        "--dry-run"
    ]
    
    print("执行命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 设置环境变量
        import os
        env = os.environ.copy()
        env['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'
        
        result = subprocess.run(cmd, capture_output=True, text=True, env=env, timeout=30)
        
        if result.returncode == 0:
            print("✅ 命令执行成功!")
            print("输出:")
            print(result.stdout)
            return True
        else:
            print("❌ 命令执行失败!")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 命令执行超时")
        return False
    except Exception as e:
        print(f"💥 执行异常: {e}")
        return False

def show_working_command():
    """显示可以工作的命令"""
    print("\n🎯 现在可以使用的完整命令:")
    print("=" * 35)
    
    print("\n📋 短时间测试（推荐先试用）:")
    print("```bash")
    print("export DATASET_LOCAL_DIR='/nas/yvan.chen/milvus/dataset'")
    print("numactl -N 0 python -m vectordb_bench.cli.vectordbbench faissremote \\")
    print("    --uri 'http://***********:8002' \\")
    print("    --case-type Performance1536D50K \\")
    print("    --index-type Flat \\")
    print("    --concurrency-duration 30 \\")
    print("    --num-concurrency 8,16 \\")
    print("    --k 100")
    print("```")
    
    print("\n🚀 完整性能测试（你原来想要的）:")
    print("```bash")
    print("export DATASET_LOCAL_DIR='/nas/yvan.chen/milvus/dataset'")
    print("numactl -N 0 python -m vectordb_bench.cli.vectordbbench faissremote \\")
    print("    --uri 'http://***********:8002' \\")
    print("    --case-type Performance1536D50K \\")
    print("    --index-type Flat \\")
    print("    --concurrency-duration 300 \\")
    print("    --num-concurrency 8,16,32,64,128 \\")
    print("    --k 100")
    print("```")

def show_server_check():
    """显示如何检查服务器"""
    print("\n🔍 检查 FAISS 服务器状态:")
    print("=" * 30)
    
    print("在运行测试前，请确保远程 FAISS 服务器正在运行:")
    print("```bash")
    print("# 检查服务器是否可达")
    print("curl -f http://***********:8002/docs")
    print("")
    print("# 或者简单的连接测试")
    print("telnet *********** 8002")
    print("```")
    
    print("\n如果服务器未运行，在目标机器上启动:")
    print("```bash")
    print("# 在 *********** 服务器上运行")
    print("cd /path/to/VectorDBBench")
    print("pip install fastapi uvicorn")
    print("python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002")
    print("```")

def show_fix_summary():
    """显示修复总结"""
    print("\n🔧 修复总结:")
    print("=" * 15)
    
    print("问题: FaissDBCaseConfig 缺少 metric_type 字段")
    print("解决: 添加了 metric_type 字段到 FaissDBCaseConfig 类")
    print("结果: 现在可以正常使用 --uri 参数连接远程 FAISS")
    
    print("\n修改的文件:")
    print("• vectordb_bench/backend/clients/faiss/config.py")
    print("• vectordb_bench/backend/clients/faiss/cli.py (新增)")
    print("• vectordb_bench/cli/vectordbbench.py (添加 faissremote 命令)")

if __name__ == "__main__":
    print("🎯 测试修复后的远程 FAISS 支持")
    print("=" * 45)
    
    # 运行测试
    success = test_fixed_faissremote()
    
    # 显示使用指南
    show_working_command()
    show_server_check()
    show_fix_summary()
    
    if success:
        print("\n🎉 修复成功! 现在可以使用 --uri 连接远程 FAISS 了!")
    else:
        print("\n⚠️  还有问题需要解决")
    
    sys.exit(0 if success else 1)
