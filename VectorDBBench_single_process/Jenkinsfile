pipeline {
  agent any
  stages {
    stage('Prepare')     { steps { sh 'bash scripts/setup_env.sh' } }
    stage('Benchmark')   { steps { sh 'bash scripts/run_faiss_with_limits.sh' } }
    stage('Archive')     { steps { archiveArtifacts artifacts: 'results/**' } }
  }
  post {
    always {
      publishHTML(target: [
        reportDir: 'results',
        reportFiles: 'faiss_hnsw_m32.html',
        reportName: 'FAISS Benchmark'])
    }
  }
} 