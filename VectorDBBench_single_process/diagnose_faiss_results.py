#!/usr/bin/env python3
"""
诊断为什么远程 FAISS 基准测试没有生成结果文件
"""

import time
import sys
import traceback
from pathlib import Path
from vectordb_bench.cli.cli import run
from vectordb_bench.backend.clients import DB
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
from vectordb_bench.backend.task_runner import MetricType
from vectordb_bench import config

def diagnose_faiss_test_issue():
    """诊断 FAISS 测试问题"""
    print("🔍 诊断远程 FAISS 基准测试问题")
    print("=" * 60)
    
    # 1. 检查结果目录配置
    print("1️⃣ 检查结果目录配置:")
    results_dir = config.RESULTS_LOCAL_DIR
    print(f"   配置的结果目录: {results_dir}")
    print(f"   目录是否存在: {results_dir.exists()}")
    
    if results_dir.exists():
        subdirs = [d for d in results_dir.iterdir() if d.is_dir()]
        print(f"   子目录: {[d.name for d in subdirs]}")
        
        # 检查是否有 Faiss 目录
        faiss_dir = results_dir / "Faiss"
        print(f"   Faiss 结果目录: {faiss_dir.exists()}")
        if not faiss_dir.exists():
            print("   💡 Faiss 结果目录不存在，可能需要创建")
    
    # 2. 测试简单的远程 FAISS 连接
    print("\n2️⃣ 测试简单的远程 FAISS 连接:")
    try:
        import requests
        response = requests.get("http://127.0.0.1:8002/docs", timeout=5)
        print(f"   服务器连接: ✅ (状态码: {response.status_code})")
    except Exception as e:
        print(f"   服务器连接: ❌ ({e})")
        return False
    
    # 3. 尝试最小化的基准测试
    print("\n3️⃣ 尝试最小化的基准测试:")
    try:
        db_config = FaissConfig(
            host="127.0.0.1",
            port=8002,
            index_type="Flat",  # 使用简单的 Flat 索引
            db_label="diagnostic_test"
        )
        
        db_case_config = FaissDBCaseConfig(
            metric_type=MetricType.COSINE
        )
        
        print(f"   配置创建: ✅")
        print(f"   数据库: {DB.Faiss}")
        print(f"   配置: {db_config}")
        
        # 设置最短的测试时间
        print(f"\n   🚀 开始最小化测试 (仅加载阶段)...")
        
        start_time = time.time()
        
        # 只测试加载阶段，不测试并发
        result = run(
            db=DB.Faiss,
            db_config=db_config,
            db_case_config=db_case_config,
            case_type="Performance1536D50K",
            k=10,  # 减少返回数量
            concurrency_duration=5,  # 最短时间
            num_concurrency=[1],  # 最少并发
            concurrency_timeout=60,
            task_label="diagnostic_minimal_test",
            load=True,
            search_serial=False,  # 跳过串行搜索
            search_concurrent=False,  # 跳过并发搜索
            drop_old=True,
            dry_run=False
        )
        
        end_time = time.time()
        print(f"   测试完成，耗时: {end_time - start_time:.2f}s")
        
        # 立即检查结果文件
        print(f"\n   📊 检查结果文件:")
        time.sleep(1)  # 给文件系统一点时间
        
        # 检查 Faiss 目录
        faiss_result_dir = results_dir / "Faiss"
        if faiss_result_dir.exists():
            print(f"   ✅ 发现 Faiss 结果目录")
            
            # 列出最新文件
            result_files = list(faiss_result_dir.glob("*.json"))
            if result_files:
                result_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                latest_file = result_files[0]
                print(f"   📄 最新结果文件: {latest_file.name}")
                print(f"      大小: {latest_file.stat().st_size} bytes")
                print(f"      修改时间: {time.ctime(latest_file.stat().st_mtime)}")
            else:
                print(f"   ❌ Faiss 结果目录为空")
        else:
            print(f"   ❌ 仍然没有 Faiss 结果目录")
            
            # 检查所有最近的结果文件
            print(f"   🔍 检查所有最近的结果文件:")
            recent_files = []
            for subdir in results_dir.iterdir():
                if subdir.is_dir():
                    for json_file in subdir.glob("*.json"):
                        if json_file.stat().st_mtime > start_time - 60:  # 1分钟内
                            recent_files.append(json_file)
            
            if recent_files:
                recent_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                print(f"   📄 最近的结果文件:")
                for file in recent_files[:3]:
                    rel_path = file.relative_to(results_dir)
                    print(f"      {rel_path} ({time.ctime(file.stat().st_mtime)})")
            else:
                print(f"   ❌ 没有发现任何最近的结果文件")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 最小化测试失败: {e}")
        traceback.print_exc()
        return False

def check_task_execution_logs():
    """检查任务执行日志"""
    print("\n4️⃣ 检查任务执行日志:")
    
    log_file = Path("logs/vectordb_bench.log")
    if log_file.exists():
        print(f"   日志文件: {log_file}")
        
        # 读取最后50行
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
                recent_lines = lines[-50:]
                
            print(f"   最近的日志条目:")
            for line in recent_lines[-10:]:  # 显示最后10行
                if "INFO" in line or "ERROR" in line or "WARNING" in line:
                    print(f"      {line.strip()}")
                    
        except Exception as e:
            print(f"   ❌ 读取日志文件失败: {e}")
    else:
        print(f"   ❌ 日志文件不存在: {log_file}")

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 建议解决方案:")
    print("=" * 30)
    
    print("1️⃣ 结果文件问题可能的原因:")
    print("   • 测试运行时间太短，未达到生成结果的条件")
    print("   • 任务在某个阶段提前退出")
    print("   • 结果保存路径配置问题")
    print("   • 权限问题导致文件无法创建")
    
    print("\n2️⃣ 解决方案:")
    print("   a) 增加测试时间和数据量:")
    print("      --concurrency-duration 60  # 增加到60秒")
    print("      --concurrency 1 2 4        # 测试多个并发级别")
    
    print("\n   b) 手动创建结果目录:")
    print("      mkdir -p vectordb_bench/results/Faiss")
    
    print("\n   c) 使用 dry-run 模式测试配置:")
    print("      python -m vectordb_bench.cli.vectordbbench faissremote \\")
    print("          --uri 'http://127.0.0.1:8002' --dry-run")
    
    print("\n   d) 检查权限和磁盘空间:")
    print("      ls -la vectordb_bench/results/")
    print("      df -h")

if __name__ == "__main__":
    print("🎯 远程 FAISS 基准测试诊断工具")
    print("=" * 70)
    
    # 执行诊断
    success = diagnose_faiss_test_issue()
    
    # 检查日志
    check_task_execution_logs()
    
    # 提供建议
    suggest_solutions()
    
    if success:
        print(f"\n✅ 诊断完成，请根据上述信息排查问题")
    else:
        print(f"\n❌ 诊断过程中发现严重问题")
    
    sys.exit(0)
