# VectorDBBench 智能FAISS服务器使用指南

## 概述

本项目扩展了VectorDBBench，添加了智能FAISS服务器支持，包括120秒测试配置和16C64G资源限制。

## 主要特性

### 1. 120秒性能测试配置 ⏱️
- **测试时长**: 默认30秒已改为120秒
- **配置方式**: 多种配置方法支持
- **适用场景**: 长时间性能评估和稳定性测试

### 2. 16核64GB资源限制 🔧
- **CPU限制**: 限制使用16个CPU核心
- **内存限制**: 软限制64GB内存使用
- **监控功能**: 实时资源使用监控

### 3. 智能FAISS服务器 🚀
- **真实数据集**: 支持Cohere、OpenAI等真实数据集
- **智能缓存**: 避免重复数据加载
- **多维度支持**: 768D、1536D等多种向量维度

## 服务端使用方法

### 1. 启动智能FAISS服务器

```bash
# 方法1: 直接启动
cd /home/<USER>/VectorDBBench
python smart_faiss_server.py

# 方法2: 后台启动
nohup python smart_faiss_server.py > faiss_server.log 2>&1 &

# 方法3: 带资源限制启动
python smart_faiss_server.py  # 已集成16C64G限制
```

### 2. 服务器配置

```python
# 数据集配置 (smart_faiss_server.py)
DATASET_MAPPING = {
    "Performance768D1M": {
        "path": "cohere/cohere_medium_1m",
        "dimension": 768,
        "vectors": 1000000
    },
    "Performance768D10M": {
        "path": "cohere/cohere_large_10m", 
        "dimension": 768,
        "vectors": 10000000
    },
    # ... 更多数据集
}

# 资源限制配置 (resource_manager.py)
resource_manager = ResourceManager(max_cpu_cores=16, max_memory_gb=64)
```

### 3. 服务器API接口

```bash
# 健康检查
curl http://localhost:8001/health

# 加载数据集
curl -X POST http://localhost:8001/load \
  -H "Content-Type: application/json" \
  -d '{
    "case_config": {
      "custom_case": {
        "case_id": "Performance768D1M",
        "dataset": "cohere/cohere_medium_1m"
      }
    }
  }'

# 搜索向量
curl -X POST http://localhost:8001/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": [0.1, 0.2, ..., 0.768],
    "topk": 100
  }'

# 查看状态
curl http://localhost:8001/status
```

## 客户端使用方法

### 1. 120秒性能测试

```bash
# 方法1: 环境变量配置
export CONCURRENCY_DURATION=120
python -m vectordb_bench --db Faiss --case-type Performance768D1M

# 方法2: 命令行参数配置
python -m vectordb_bench \
  --host localhost \
  --port 8001 \
  --db Faiss \
  --case-type Performance768D1M \
  --concurrency-duration 120 \
  --timeout 600

# 方法3: 配置文件修改 (已完成)
# vectordb_bench/config.py: CONCURRENCY_DURATION = 120
```

### 2. 支持的数据集

| 数据集名称 | 维度 | 向量数量 | 描述 |
|-----------|------|----------|------|
| Performance768D1M | 768 | 1,000,000 | Cohere 1M数据集 |
| Performance768D10M | 768 | 10,000,000 | Cohere 10M数据集 |
| Performance1536D50K | 1536 | 50,000 | OpenAI 50K数据集 |
| Performance1536D500K | 1536 | 500,000 | OpenAI 500K数据集 |
| Performance1536D5M | 1536 | 5,000,000 | OpenAI 5M数据集 |

### 3. 测试脚本示例

```python
# test_cohere_10m_120s.py - 120秒性能测试
benchmark = Cohere10MBenchmark120s()
results = benchmark.run_benchmark()

# test_server_readiness.py - 服务器就绪性检查
wait_for_load_completion()
test_search()
```

## 配置文件说明

### 1. 核心配置修改

```python
# vectordb_bench/config.py
CONCURRENCY_DURATION = 120  # 120秒测试时长

# vectordb_bench_client/vectordb_bench/config.py  
CONCURRENCY_DURATION = 120  # 客户端同步配置
```

### 2. 资源管理配置

```python
# resource_manager.py
class ResourceManager:
    def __init__(self, max_cpu_cores=16, max_memory_gb=64):
        self.max_cpu_cores = max_cpu_cores
        self.max_memory_bytes = max_memory_gb * 1024 * 1024 * 1024
        
    def set_cpu_affinity(self):
        # 设置CPU亲和性
        available_cpus = list(range(min(self.max_cpu_cores, psutil.cpu_count())))
        psutil.Process(self.pid).cpu_affinity(available_cpus)
        
    def set_memory_limit(self):
        # 设置内存软限制
        resource.setrlimit(resource.RLIMIT_AS, (self.max_memory_bytes, self.max_memory_bytes))
```

## 完整测试工作流

### 1. 启动服务器

```bash
cd /home/<USER>/VectorDBBench
python smart_faiss_server.py
```

### 2. 加载数据集

```bash
curl -X POST http://localhost:8001/load \
  -H "Content-Type: application/json" \
  -d '{
    "case_config": {
      "custom_case": {
        "case_id": "Performance768D1M",
        "dataset": "cohere/cohere_medium_1m"
      }
    }
  }'
```

### 3. 运行120秒性能测试

```bash
# VectorDBBench CLI测试
CONCURRENCY_DURATION=120 python -m vectordb_bench \
  --host localhost \
  --port 8001 \
  --db Faiss \
  --case-type Performance768D1M

# 或使用自定义测试脚本
python test_cohere_10m_120s.py
```

### 4. 查看结果

```bash
# 检查测试结果文件
ls -la vectordb_bench/results/

# 查看性能指标
curl http://localhost:8001/status
```

## 性能优化建议

### 1. 内存优化
- 对于10M数据集，建议增加内存限制到128GB
- 使用分批加载减少内存峰值

### 2. CPU优化
- 16核心配置适合中等规模测试
- 大规模测试可调整到24-32核心

### 3. 数据集选择
- 开发测试: Performance768D1M (1M向量)
- 生产评估: Performance768D10M (10M向量)
- 快速验证: Performance1536D50K (50K向量)

## 故障排除

### 1. 内存不足错误
```
ERROR: realloc of size 17179869184 failed
```
**解决方案**: 增加内存限制或使用较小数据集

### 2. 连接超时
```
HTTPConnectionPool: Read timed out
```
**解决方案**: 增加timeout参数或检查服务器状态

### 3. 端口占用
```
Address already in use
```
**解决方案**: 
```bash
sudo netstat -tulpn | grep :8001
sudo kill -9 <PID>
```

## 文件结构

```
VectorDBBench/
├── smart_faiss_server.py          # 智能FAISS服务器主文件
├── resource_manager.py            # 16C64G资源管理器
├── vectordb_bench/config.py       # 核心配置文件(120s)
├── test_cohere_10m_120s.py       # 120秒性能测试脚本
├── test_server_readiness.py      # 服务器就绪性检查
├── validate_120s_config.py       # 配置验证脚本
├── faiss_process_analyzer.py     # FAISS处理流程分析工具
├── FAISS_PROCESS_DETAILED.md     # 详细技术文档 (服务端处理流程)
└── README_USAGE.md               # 本使用说明文档
```

## 技术文档

### 📖 详细技术资料
- **[FAISS_PROCESS_DETAILED.md](./FAISS_PROCESS_DETAILED.md)** - 服务端请求处理流程详解
  - 完整的请求处理时序图
  - FAISS内部机制深度分析
  - 多线程并行处理详解
  - 性能优化技术说明
  - CPU利用率分析报告

### 🔧 分析工具
- **[faiss_process_analyzer.py](./faiss_process_analyzer.py)** - 处理流程分析工具
  - 运行 `python faiss_process_analyzer.py` 获取详细分析报告
  - 包含时序图、性能特征、CPU利用率解释等

## 验证命令

```bash
# 1. 验证120秒配置
python -c "from vectordb_bench import config; print(f'测试时长: {config.CONCURRENCY_DURATION}秒')"

# 2. 验证服务器状态
curl -s http://localhost:8001/health | python -m json.tool

# 3. 验证资源限制
python -c "from resource_manager import ResourceManager; rm = ResourceManager(); rm.monitor_resources()"

# 4. 完整功能测试
python test_server_readiness.py
```

## 服务端请求处理流程详解 🔍

### 1. 搜索请求处理流程

当客户端发送搜索请求到 `/search` 接口时，服务器的完整处理流程如下：

#### 第一阶段：请求接收与验证
```python
@app.post("/search")
async def search_vectors_smart(request: Request):
    # 1. 检查FAISS索引状态
    current_index = server_state["current_index"]
    if current_index is None:
        raise HTTPException(status_code=400, detail="索引未初始化")
    
    # 2. 解析JSON请求数据
    request_data = await request.json()
    
    # 3. 提取查询向量 (支持多种格式)
    # - vectors: [[...]]  # VectorDBBench标准格式
    # - query: [...]      # 简化格式
    # - query_vector: [...] # 通用格式
    # - vector/embedding: [...] # 兼容格式
```

#### 第二阶段：参数解析与验证
```python
    # 4. 提取topK参数 (支持多种字段名)
    topk = (request_data.get('topk') or 
           request_data.get('top_k') or 
           request_data.get('k') or 
           100)  # 默认100
    
    # 5. 验证向量维度匹配
    if len(query) != server_state["server_status"]["dimension"]:
        raise HTTPException(status_code=422, detail="向量维度不匹配")
```

#### 第三阶段：FAISS搜索执行
```python
    # 6. 向量格式转换
    query_vector = np.array([query]).astype('float32')
    
    # 7. 执行FAISS搜索 (多线程并行)
    distances, indices = current_index.search(query_vector, topk)
    #   ↑ 这里是关键：FAISS内部使用16个OpenMP线程并行搜索
    
    # 8. 格式化返回结果
    result = {
        "ids": [indices[0].tolist()],
        "distances": [distances[0].tolist()]
    }
```

### 2. 数据加载处理流程

#### `/load` 接口 - VectorDBBench兼容
```python
@app.post("/load")
async def load_dataset(request: LoadRequest):
    # 1. 解析数据集配置
    case_config = request.case_config
    custom_case = case_config.get("custom_case", {})
    case_id = custom_case.get("case_id", "")  # 如: Performance768D10M
    
    # 2. 映射到真实数据集路径
    dataset_info = DATASET_MAPPING[case_id]
    dataset_path = Path(DATASET_BASE_PATH) / dataset_info["path"]
    #   例如: /nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m
    
    # 3. 创建FAISS索引
    dim = dataset_info["dimension"]  # 768维
    index = faiss.IndexHNSWFlat(dim, 16)  # M=16
    index.hnsw.ef_construction = 200       # 构建参数
    
    # 4. 分批加载parquet文件
    train_files = list(dataset_path.glob("*train*.parquet"))
    for train_file in train_files:
        df = pd.read_parquet(train_file)
        vectors = np.vstack(df['emb'].values).astype('float32')
        
        # 5. 批量添加到FAISS索引 (每批10000个向量)
        batch_size = 10000
        for j in range(0, len(vectors), batch_size):
            batch = vectors[j:j+batch_size]
            index.add(batch)  # ← FAISS多线程并行索引构建
```

#### `/create_index` 接口 - 智能预加载
```python
@app.post("/create_index")
async def create_index_smart(request: LegacyCreateIndexRequest):
    # 1. 创建指定类型的FAISS索引
    if index_type.upper() == "HNSW":
        index = faiss.IndexHNSWFlat(dim, m)
        index.hnsw.ef_construction = ef_construction
    elif index_type.upper() == "FLAT":
        index = faiss.IndexFlatL2(dim)
    
    # 2. 智能数据集匹配
    suitable_dataset = None
    for case_name, dataset_info in DATASET_MAPPING.items():
        if dataset_info["dimension"] == dim:  # 维度匹配
            dataset_path = Path(DATASET_BASE_PATH) / dataset_info["path"]
            if dataset_path.exists():  # 路径存在
                suitable_dataset = dataset_info
                break
    
    # 3. 自动加载匹配的真实数据集
    if suitable_dataset:
        # 加载真实parquet数据
        # 同批量索引构建流程...
    else:
        # 备用方案：生成随机数据
        vectors = np.random.random((100000, dim)).astype('float32')
        index.add(vectors)
```

### 3. FAISS多线程处理机制

#### OpenMP线程配置
```python
# 服务器启动时的配置
import faiss
faiss.omp_set_num_threads(16)  # 设置16个OpenMP线程

# 环境变量优化
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['MKL_NUM_THREADS'] = '16'
os.environ['OPENBLAS_NUM_THREADS'] = '16'
```

#### 并行搜索执行
```python
# 当执行 index.search(query_vector, topk) 时：
# 1. FAISS自动将搜索任务分配给16个线程
# 2. 每个线程处理索引的不同部分
# 3. 并行计算向量相似度
# 4. 合并结果并返回top-k
```

#### CPU核心使用验证
```bash
# 实际验证结果显示多线程确实在工作：
# 线程 391351: 4.2% CPU ⭐
# 线程 391353: 4.4% CPU ⭐  
# 线程 391352: 2.2% CPU
# 等其他工作线程...
```

### 4. 内存管理与资源控制

#### 资源管理器集成
```python
# 服务器启动时应用16C64G限制
from resource_manager import ResourceManager
resource_manager = ResourceManager(max_cpu_cores=16, max_memory_gb=64)
resource_manager.apply_limits()

# CPU亲和性设置到前16个核心
# 内存软限制64GB
# 实时资源监控
```

#### 数据集内存优化
```python
# 分批处理大数据集避免内存溢出
batch_size = 10000  # 每批处理1万个向量
for i in range(0, len(vectors), batch_size):
    batch = vectors[i:i+batch_size]
    index.add(batch)  # 增量添加，不需要一次性加载全部数据
```

### 5. 并发处理架构

#### uvicorn异步优化
```python
# 服务器启动配置
uvicorn.run(
    app, 
    host="0.0.0.0", 
    port=8001,
    workers=1,        # 单进程（避免索引共享问题）
    loop="asyncio",   # 异步事件循环
    limit_concurrency=1000,  # 支持1000并发连接
    limit_max_requests=10000  # 最大请求数限制
)
```

#### 请求处理流水线
```
客户端请求 → FastAPI路由 → 异步处理函数 → FAISS多线程搜索 → 结果返回
     ↓             ↓              ↓              ↓           ↓
  HTTP连接    JSON解析     参数验证     并行计算     响应格式化
```

### 6. 性能优化要点

#### FAISS索引优化
- **HNSW参数**: M=16, ef_construction=200 (平衡精度与速度)
- **批量处理**: 10000向量/批，减少索引重建开销
- **内存预分配**: 避免频繁内存分配

#### 多线程优化
- **OpenMP线程池**: 16个线程持续运行，避免线程创建开销
- **CPU亲和性**: 绑定前16个核心，减少缓存失效
- **NUMA优化**: 利用本地内存访问

#### 异步处理优化  
- **非阻塞I/O**: FastAPI异步处理HTTP请求
- **内存复用**: 向量数据就地转换，减少拷贝
- **流水线处理**: 请求解析与FAISS计算并行

## 总结

通过本次升级，VectorDBBench现在支持：
- ✅ 120秒长时间性能测试
- ✅ 16核64GB资源限制
- ✅ 智能FAISS服务器
- ✅ 真实大规模数据集支持
- ✅ 完整的API接口
- ✅ 资源监控和管理
- ✅ 多线程并行FAISS处理
- ✅ 智能数据集预加载
- ✅ 异步高并发架构

所有配置均已完成并可直接使用。
