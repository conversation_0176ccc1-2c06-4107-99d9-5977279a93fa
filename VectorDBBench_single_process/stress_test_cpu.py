#!/usr/bin/env python3
"""
FAISS服务器多核压力测试
验证在高并发下是否使用多个CPU核心
"""

import requests
import threading
import time
import random
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import os

def monitor_cpu_usage(duration=60):
    """监控CPU使用情况"""
    print(f"🔍 开始监控CPU使用情况 ({duration}秒)...")
    
    cpu_data = []
    start_time = time.time()
    
    while time.time() - start_time < duration:
        # 获取各核心使用率
        cpu_percent = psutil.cpu_percent(interval=1, percpu=True)
        
        # 统计活跃核心（使用率>5%）
        active_cores = [i for i, usage in enumerate(cpu_percent) if usage > 5.0]
        high_usage_cores = [i for i, usage in enumerate(cpu_percent) if usage > 20.0]
        
        timestamp = time.time() - start_time
        cpu_data.append({
            'time': timestamp,
            'active_cores': len(active_cores),
            'high_usage_cores': len(high_usage_cores),
            'max_usage': max(cpu_percent),
            'avg_usage': sum(cpu_percent) / len(cpu_percent)
        })
        
        print(f"   {timestamp:.0f}s: 活跃核心数: {len(active_cores)}, 高负载核心数: {len(high_usage_cores)}, 最大使用率: {max(cpu_percent):.1f}%")
    
    return cpu_data

def generate_random_vector(dim=768):
    """生成随机查询向量"""
    return [random.uniform(-1, 1) for _ in range(dim)]

def send_search_request(url, vector, topk=100, request_id=None):
    """发送搜索请求"""
    try:
        start_time = time.time()
        response = requests.post(
            f"{url}/search",
            json={"query": vector, "topk": topk},
            timeout=30
        )
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            return {
                'request_id': request_id,
                'status': 'success',
                'latency': end_time - start_time,
                'results_count': len(result.get('ids', [{}])[0] if result.get('ids') else [])
            }
        else:
            return {
                'request_id': request_id,
                'status': 'error',
                'error': f"HTTP {response.status_code}",
                'latency': end_time - start_time
            }
    except Exception as e:
        return {
            'request_id': request_id,
            'status': 'error',
            'error': str(e),
            'latency': 0
        }

def stress_test_concurrent(url, num_threads=16, requests_per_thread=10, vector_dim=768):
    """并发压力测试"""
    print(f"🚀 开始并发压力测试:")
    print(f"   并发线程数: {num_threads}")
    print(f"   每线程请求数: {requests_per_thread}")
    print(f"   总请求数: {num_threads * requests_per_thread}")
    print(f"   向量维度: {vector_dim}")
    print()
    
    all_results = []
    start_time = time.time()
    
    # 启动CPU监控线程
    cpu_monitor = threading.Thread(
        target=lambda: monitor_cpu_usage(duration=max(60, num_threads * requests_per_thread // 2)),
        daemon=True
    )
    cpu_monitor.start()
    
    # 并发执行搜索请求
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = []
        
        for thread_id in range(num_threads):
            for req_id in range(requests_per_thread):
                vector = generate_random_vector(vector_dim)
                future = executor.submit(
                    send_search_request, 
                    url, 
                    vector, 
                    100, 
                    f"T{thread_id}-R{req_id}"
                )
                futures.append(future)
        
        # 收集结果
        completed = 0
        for future in as_completed(futures):
            result = future.result()
            all_results.append(result)
            completed += 1
            
            if completed % 10 == 0:
                elapsed = time.time() - start_time
                print(f"   完成 {completed}/{len(futures)} 请求 ({elapsed:.1f}s)")
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    successful = [r for r in all_results if r['status'] == 'success']
    failed = [r for r in all_results if r['status'] == 'error']
    
    if successful:
        latencies = [r['latency'] for r in successful]
        avg_latency = sum(latencies) / len(latencies)
        min_latency = min(latencies)
        max_latency = max(latencies)
        qps = len(successful) / total_time
    else:
        avg_latency = min_latency = max_latency = qps = 0
    
    print(f"\n📊 压力测试结果:")
    print(f"   总耗时: {total_time:.2f}秒")
    print(f"   成功请求: {len(successful)}")
    print(f"   失败请求: {len(failed)}")
    print(f"   QPS: {qps:.2f}")
    print(f"   平均延迟: {avg_latency*1000:.2f}ms")
    print(f"   最小延迟: {min_latency*1000:.2f}ms")
    print(f"   最大延迟: {max_latency*1000:.2f}ms")
    
    if failed:
        print(f"\n❌ 失败请求详情:")
        error_types = {}
        for f in failed:
            error = f.get('error', 'unknown')
            error_types[error] = error_types.get(error, 0) + 1
        
        for error, count in error_types.items():
            print(f"   {error}: {count}次")
    
    return all_results

def check_server_status(url):
    """检查服务器状态"""
    try:
        print(f"🔍 检查服务器状态: {url}")
        
        # 健康检查
        health_resp = requests.get(f"{url}/health", timeout=5)
        print(f"   健康状态: {'✅ 正常' if health_resp.status_code == 200 else '❌ 异常'}")
        
        # 状态详情
        status_resp = requests.get(f"{url}/status", timeout=5)
        if status_resp.status_code == 200:
            status = status_resp.json()
            vectors_count = status.get('vectors_count', 0) or status.get('total_vectors', 0)
            dimension = status.get('dimension', 'N/A')
            index_type = status.get('index_type', 'N/A')
            
            print(f"   向量数量: {vectors_count:,}")
            print(f"   维度: {dimension}")
            print(f"   索引类型: {index_type}")
            return True
        else:
            print(f"   ❌ 无法获取状态: {status_resp.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 FAISS服务器多核压力测试")
    print("=" * 50)
    
    # 测试服务器URL
    url = "http://localhost:8001"
    
    # 检查服务器状态
    if not check_server_status(url):
        print("❌ 服务器不可用，退出测试")
        return
    
    print()
    
    # 进行不同级别的压力测试
    test_configs = [
        {"name": "轻负载测试", "threads": 4, "requests": 5},
        {"name": "中等负载测试", "threads": 8, "requests": 10},
        {"name": "高负载测试", "threads": 16, "requests": 20},
        {"name": "极限负载测试", "threads": 32, "requests": 10},
    ]
    
    for config in test_configs:
        print(f"\n🎯 {config['name']}")
        print("=" * 30)
        
        results = stress_test_concurrent(
            url, 
            num_threads=config['threads'],
            requests_per_thread=config['requests']
        )
        
        print("\n⏱️ 等待5秒后进行下一个测试...")
        time.sleep(5)
    
    print("\n🎉 所有测试完成!")
    print("💡 检查上面的CPU监控输出，看是否有多个核心被激活")

if __name__ == "__main__":
    main()
