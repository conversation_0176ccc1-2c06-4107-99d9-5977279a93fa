#!/usr/bin/env python3
"""
模拟VectorDBBench 512并发的完整流程
"""

import sys
import os
import time
import multiprocessing as mp
import logging
import random
import requests
from concurrent.futures import ProcessPoolExecutor
from multiprocessing.queues import Queue

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s: %(message)s')
log = logging.getLogger(__name__)

class ConcurrencySlotTimeoutError(Exception):
    pass

class MockVectorDBBench:
    def __init__(self, concurrency_timeout=300):
        self.concurrency_timeout = concurrency_timeout
        self.duration = 30  # 30秒测试
        self.k = 100
        
    def _wait_for_queue_fill(self, q: Queue, size: int):
        """优化的队列填充等待函数 - 修复512并发问题"""
        wait_t = 0
        check_interval = 0.1  # 初始检查间隔：100ms
        max_interval = 2.0    # 最大检查间隔：2秒
        
        log.info(f"等待 {size} 个进程启动并加入队列...")
        
        while q.qsize() < size:
            current_size = q.qsize()
            
            # 动态调整检查间隔
            if current_size == 0:
                sleep_t = check_interval  # 开始时快速检查
            elif current_size < size // 2:
                sleep_t = min(0.5, check_interval * 2)  # 进程启动中，中等频率
            else:
                sleep_t = min(max_interval, check_interval * 5)  # 大部分进程已启动，降低频率
            
            wait_t += sleep_t
            
            # 超时检查
            if wait_t > self.concurrency_timeout > 0:
                log.error(f"进程启动超时: 已等待 {wait_t:.1f}s, 当前队列大小: {current_size}/{size}")
                raise ConcurrencySlotTimeoutError
            
            # 进度日志
            if int(wait_t) % 10 == 0 and wait_t > 10:  # 每10秒打印一次进度
                progress = (current_size / size) * 100
                log.info(f"进程启动进度: {current_size}/{size} ({progress:.1f}%), 已等待: {wait_t:.1f}s")
            
            time.sleep(sleep_t)
        
        log.info(f"✅ 所有 {size} 个进程已启动完成，总耗时: {wait_t:.2f}s")
    
    def search_worker(self, test_data, q, cond):
        """模拟搜索工作进程"""
        try:
            # 1. 同步阶段：加入队列
            q.put(1)
            
            # 2. 等待同步信号
            with cond:
                cond.wait()
            
            # 3. 模拟数据库连接初始化
            time.sleep(0.1)  # 模拟连接建立时间
            
            # 4. 执行搜索循环
            start_time = time.perf_counter()
            count = 0
            failed_count = 0
            
            while time.perf_counter() < start_time + self.duration:
                try:
                    # 模拟向FAISS服务器发送请求
                    query_vector = [random.random() for _ in range(768)]
                    
                    # 实际发送HTTP请求到FAISS服务器
                    response = requests.post(
                        "http://localhost:8005/search",
                        json={"query": query_vector, "topk": self.k},
                        timeout=5
                    )
                    
                    if response.status_code == 200:
                        count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    failed_count += 1
                    if failed_count <= 3:
                        log.warning(f"搜索请求失败: {e}")
            
            total_dur = time.perf_counter() - start_time
            log.debug(f"进程 {mp.current_process().name} 完成: count={count}, failed={failed_count}, dur={total_dur:.2f}s")
            
            return count, failed_count, []
            
        except Exception as e:
            log.error(f"搜索工作进程异常: {e}")
            return 0, 1, []
    
    def test_512_concurrency(self):
        """测试512并发"""
        concurrency = 512
        test_data = [[random.random() for _ in range(768)] for _ in range(100)]
        
        print(f"🚀 开始VectorDBBench风格的512并发测试")
        print("=" * 60)
        
        try:
            with mp.Manager() as m:
                q, cond = m.Queue(), m.Condition()
                
                log.info(f"Start search {self.duration}s in concurrency {concurrency}")
                
                with ProcessPoolExecutor(
                    mp_context=mp.get_context("spawn"),
                    max_workers=concurrency,
                ) as executor:
                    
                    # 提交所有任务
                    print("📤 提交512个搜索任务...")
                    submit_start = time.time()
                    
                    future_iter = [
                        executor.submit(self.search_worker, test_data, q, cond) 
                        for i in range(concurrency)
                    ]
                    
                    submit_time = time.time() - submit_start
                    print(f"✅ 任务提交完成，耗时: {submit_time:.2f}s")
                    
                    # 等待所有进程启动
                    print("⏳ 等待所有进程启动...")
                    sync_start = time.time()
                    
                    self._wait_for_queue_fill(q, size=concurrency)
                    
                    sync_time = time.time() - sync_start
                    print(f"✅ 进程同步完成，耗时: {sync_time:.2f}s")
                    
                    # 发送同步信号，开始测试
                    with cond:
                        cond.notify_all()
                        log.info(f"Syncing all process and start concurrency search, concurrency={concurrency}")
                    
                    print(f"🏃 开始{self.duration}秒并发搜索测试...")
                    test_start = time.time()
                    
                    # 等待所有任务完成
                    results = [f.result() for f in future_iter]
                    
                    test_time = time.time() - test_start
                    
                    # 统计结果
                    total_count = sum(r[0] for r in results)
                    total_failed = sum(r[1] for r in results)
                    qps = total_count / test_time if test_time > 0 else 0
                    
                    print(f"✅ 512并发测试完成!")
                    print(f"   测试时长: {test_time:.2f}s")
                    print(f"   总请求数: {total_count}")
                    print(f"   失败请求: {total_failed}")
                    print(f"   QPS: {qps:.2f}")
                    
                    log.info(f"End search in concurrency {concurrency}: dur={test_time}s, total_count={total_count}, qps={qps}")
                    
                    return True
                    
        except ConcurrencySlotTimeoutError:
            print("❌ 512并发测试超时 - 进程启动阶段")
            return False
        except Exception as e:
            print(f"❌ 512并发测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_progressive_concurrency(self):
        """渐进式并发测试"""
        print("\n🔄 渐进式并发测试")
        print("=" * 50)
        
        concurrencies = [128, 256, 384, 512]
        
        for conc in concurrencies:
            print(f"\n📊 测试并发度: {conc}")
            print("-" * 30)
            
            try:
                # 简化测试：只测试进程启动和同步
                with mp.Manager() as m:
                    q, cond = m.Queue(), m.Condition()
                    
                    with ProcessPoolExecutor(
                        mp_context=mp.get_context("spawn"),
                        max_workers=conc,
                    ) as executor:
                        
                        start_time = time.time()
                        
                        # 提交简化任务
                        futures = [
                            executor.submit(self.simple_worker, q, cond, i) 
                            for i in range(conc)
                        ]
                        
                        # 等待同步
                        self._wait_for_queue_fill(q, size=conc)
                        
                        sync_time = time.time() - start_time
                        
                        # 发送信号
                        with cond:
                            cond.notify_all()
                        
                        # 等待完成
                        results = [f.result() for f in futures]
                        
                        total_time = time.time() - start_time
                        
                        print(f"✅ {conc}并发测试成功")
                        print(f"   同步时间: {sync_time:.2f}s")
                        print(f"   总时间: {total_time:.2f}s")
                        
            except Exception as e:
                print(f"❌ {conc}并发测试失败: {e}")
                break
    
    def simple_worker(self, q, cond, worker_id):
        """简化工作进程"""
        try:
            q.put(1)
            with cond:
                cond.wait()
            time.sleep(0.1)  # 模拟少量工作
            return worker_id
        except Exception as e:
            log.error(f"简化工作进程 {worker_id} 异常: {e}")
            return -1

def main():
    """主函数"""
    print("🧪 VectorDBBench 512并发模拟测试")
    print("=" * 60)
    
    # 检查FAISS服务器
    try:
        response = requests.get("http://localhost:8005/status", timeout=5)
        if response.status_code == 200:
            print("✅ FAISS服务器连接正常")
        else:
            print("❌ FAISS服务器响应异常")
            return
    except:
        print("❌ 无法连接FAISS服务器，请确保服务器运行在 localhost:8005")
        return
    
    bench = MockVectorDBBench(concurrency_timeout=300)
    
    # 1. 渐进式测试
    bench.test_progressive_concurrency()
    
    # 2. 完整512并发测试
    success = bench.test_512_concurrency()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 512并发模拟测试成功!")
        print("🎯 VectorDBBench的512并发应该能正常工作")
    else:
        print("❌ 512并发模拟测试失败")
        print("🔧 需要进一步调查问题")

if __name__ == "__main__":
    main()
