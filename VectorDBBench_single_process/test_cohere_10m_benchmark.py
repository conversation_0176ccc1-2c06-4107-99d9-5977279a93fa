#!/usr/bin/env python3
"""
测试 Cohere 768维 10M 数据集的 FAISS 基准测试
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def check_dataset_exists():
    """检查数据集是否存在"""
    dataset_path = Path("/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m")
    
    if not dataset_path.exists():
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return False
    
    # 检查必要的文件
    required_files = ["shuffle_train-00-of-10.parquet", "test.parquet", "neighbors.parquet"]
    missing_files = []
    
    for file_name in required_files:
        file_path = dataset_path / file_name
        if not file_path.exists():
            missing_files.append(file_name)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print(f"✅ 数据集验证通过: {dataset_path}")
    print(f"📁 可用文件:")
    for file_path in dataset_path.glob("*.parquet"):
        file_size = file_path.stat().st_size / (1024 * 1024)  # MB
        print(f"   - {file_path.name}: {file_size:.1f} MB")
    
    return True

def run_benchmark_test():
    """运行 Cohere 10M 基准测试"""
    print("\n" + "="*60)
    print("🚀 开始 Cohere 768维 10M 数据集基准测试")
    print("="*60)
    
    # 构建测试命令
    cmd = [
        "python3", "-m", "vectordb_bench.cli.vectordbbench",
        "faissremote",
        "--uri", "http://***********:8001",
        "--case-type", "Performance768D10M",
        "--index-type", "HNSW", 
        "--m", "16",
        "--ef-construction", "200",
        "--load"
    ]
    
    print(f"📋 执行命令: {' '.join(cmd)}")
    print(f"🎯 测试目标: Performance768D10M (Cohere 768维 10M向量)")
    print(f"🔧 索引参数: HNSW, M=16, ef_construction=200")
    print(f"🌐 服务器地址: http://***********:8001")
    print()
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 执行基准测试
        result = subprocess.run(
            cmd,
            cwd="/home/<USER>/VectorDBBench",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            timeout=3600  # 1小时超时
        )
        
        # 记录结束时间
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ 测试耗时: {duration:.2f} 秒 ({duration/60:.1f} 分钟)")
        print(f"🔄 返回码: {result.returncode}")
        print()
        
        # 显示输出
        if result.stdout:
            print("📊 标准输出:")
            print("-" * 40)
            print(result.stdout)
            print("-" * 40)
        
        if result.stderr:
            print("⚠️ 错误输出:")
            print("-" * 40)
            print(result.stderr)
            print("-" * 40)
        
        # 分析结果
        if result.returncode == 0:
            print("✅ 基准测试完成成功!")
            
            # 尝试从输出中提取性能指标
            output_text = result.stdout + result.stderr
            if "QPS" in output_text:
                print("📈 发现性能指标:")
                for line in output_text.split('\n'):
                    if 'QPS' in line or 'Recall' in line or 'Latency' in line:
                        print(f"   {line.strip()}")
        else:
            print(f"❌ 基准测试失败，返回码: {result.returncode}")
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时 (1小时)")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def main():
    print("🧪 Cohere 768维 10M 数据集 FAISS 基准测试")
    print("=" * 60)
    
    # 检查数据集
    if not check_dataset_exists():
        print("❌ 数据集检查失败，无法继续测试")
        sys.exit(1)
    
    # 检查环境
    vectordb_bench_path = Path("/home/<USER>/VectorDBBench/vectordb_bench")
    if not vectordb_bench_path.exists():
        print(f"❌ VectorDBBench 路径不存在: {vectordb_bench_path}")
        sys.exit(1)
    
    print("✅ 环境检查通过")
    
    # 运行基准测试
    run_benchmark_test()

if __name__ == "__main__":
    main()
