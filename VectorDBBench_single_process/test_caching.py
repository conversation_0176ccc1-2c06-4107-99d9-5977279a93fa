#!/usr/bin/env python3
"""测试FAISS客户端的缓存功能"""

import sys
import numpy as np
import requests
from pathlib import Path

# 添加项目路径
sys.path.append('/home/<USER>/VectorDBBench')

def test_server_status():
    """测试服务器状态"""
    try:
        resp = requests.get("http://***********:8000/status", timeout=5)
        print(f"📊 服务器状态: {resp.json()}")
        return resp.json()
    except Exception as e:
        print(f"❌ 无法连接服务器: {e}")
        return None

def insert_test_data():
    """向服务器插入测试数据"""
    print("📝 插入测试数据...")
    
    # 生成1000个测试向量
    test_vectors = np.random.random((1000, 1536)).tolist()
    
    try:
        resp = requests.post(
            "http://***********:8000/insert_bulk",
            json={"vectors": test_vectors},
            timeout=30
        )
        print(f"✅ 插入完成: {resp.status_code}")
        return True
    except Exception as e:
        print(f"❌ 插入失败: {e}")
        return False

def test_faiss_client():
    """测试FAISS客户端的缓存逻辑"""
    print("🔧 测试FAISS客户端缓存...")
    
    from vectordb_bench.backend.clients.faiss.faiss import FaissClient
    from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
    
    # 创建数据库配置
    db_config = FaissConfig(
        host="***********",
        port=8000,
        index_type="HNSW"
    )
    
    # 创建案例配置  
    db_case_config = FaissDBCaseConfig(
        m=16,
        ef_construction=200
    )
    
    # 创建客户端实例
    client = FaissClient(
        dim=1536,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="test_collection",
        drop_old=False
    )
    
    print(f"🔍 客户端初始化完成")
    print(f"   - 服务端有数据: {client._server_has_data}")
    print(f"   - 缓存已验证: {client._cache_validated}")
    
    # 测试插入（应该被缓存跳过）
    test_embeddings = np.random.random((100, 1536)).tolist()
    metadata = list(range(100))
    
    result, error = client.insert_embeddings(test_embeddings, metadata)
    print(f"📊 插入结果: {result} 个向量，错误: {error}")
    
    return client

def main():
    print("🚀 开始缓存功能测试")
    print("=" * 50)
    
    # 1. 检查服务器状态
    status = test_server_status()
    if not status:
        return
    
    # 2. 如果服务器没有数据，先插入一些
    if status.get('total_vectors', 0) < 100:
        print("📝 服务器数据不足，先插入测试数据...")
        if not insert_test_data():
            return
        
        # 重新检查状态
        status = test_server_status()
    
    # 3. 测试客户端缓存
    client = test_faiss_client()
    
    print("\n✅ 缓存测试完成")

if __name__ == "__main__":
    main()
