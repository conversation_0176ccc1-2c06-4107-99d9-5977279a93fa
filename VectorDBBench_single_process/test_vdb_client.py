#!/usr/bin/env python3
"""
测试VectorDBBench客户端与服务器的连接
"""

import sys
import time
sys.path.append('/home/<USER>/VectorDBBench')

from vectordb_bench.backend.clients.faiss.faiss import FaissClient
from vectordb_bench.backend.clients.faiss.config import FaissConfig

def test_faiss_client():
    print("🧪 测试VectorDBBench Faiss客户端...")
    
    # 创建配置
    config = FaissConfig(
        host="***********",
        port=8001,
        index_type="HNSW"
    )
    
    # 创建客户端
    client = FaissClient(
        dim=1536,
        db_config=config,
        db_case_config=None,
        collection_name="test_collection"
    )
    
    # 测试初始化
    print("� 初始化客户端...")
    client.init()
    
    # 生成测试向量
    print("🔍 测试搜索...")
    test_query = [0.1] * 1536
    
    # 执行搜索
    try:
        results = client.search_embedding(test_query, k=5)
        print(f"✅ 搜索成功! 返回 {len(results)} 个结果")
        print(f"🎯 结果IDs: {results}")
        return True
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        return False

if __name__ == "__main__":
    success = test_faiss_client()
    exit(0 if success else 1)
