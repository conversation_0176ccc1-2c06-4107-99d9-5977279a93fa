#!/bin/bash

# 设置环境变量
export DATASET_LOCAL_DIR=/home/<USER>/VectorDBBench/dataset

echo "=== FAISS 基准测试开始 ==="
echo "时间: $(date)"
echo "数据集路径: $DATASET_LOCAL_DIR"

# 运行 FAISS 基准测试
echo
echo "运行 FAISS 基准测试..."
cd /home/<USER>/VectorDBBench
python -m vectordb_bench.cli.batch_cli --config faiss_simple_test.yml 2>&1 | tee /tmp/faiss_test_output.log

echo
echo "=== 检查结果 ==="
echo "最新日志内容:"
tail -n 50 logs/vectordb_bench.log 2>/dev/null || echo "没有找到日志文件"

echo
echo "结果文件:"
find results/ -name "*.json" -type f -exec ls -la {} \; 2>/dev/null || echo "没有找到结果文件"

echo
echo "=== 测试完成 ==="
