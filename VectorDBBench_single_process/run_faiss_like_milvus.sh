#!/bin/bash
# FAISS 基准测试脚本 - 类似于你的 Milvus 命令

# 设置环境
export DATASET_LOCAL_DIR="/nas/yvan.chen/milvus/dataset"

# 获取时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "🎯 开始 FAISS 基准测试 - $TIMESTAMP"

# 使用 numactl 绑定 NUMA 节点 (类似你的 Milvus 命令)
numactl -N 0 python -m vectordb_bench.cli.vectordbbench faisslocalhnsw \
    --case-type Performance1536D50K \
    --m 30 \
    --ef-construction 360 \
    --ef-search 100 \
    --concurrency-duration 300 \
    --num-concurrency 8,16,32,64,128 \
    --k 100 \
    --db-label "faiss_hnsw_$TIMESTAMP" \
    --task-label "FAISS_HNSW_Performance_$TIMESTAMP"

echo "✅ FAISS 基准测试完成"
