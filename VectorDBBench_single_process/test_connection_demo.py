#!/usr/bin/env python3
"""
演示FAISS远程连接和智能缓存功能
"""

import requests
import sys
import time

def test_faiss_connection(host="localhost", port=8000):
    """测试FAISS服务器连接和智能缓存功能"""
    
    base_url = f"http://{host}:{port}"
    
    print(f"🔍 测试FAISS服务器连接: {base_url}")
    print("=" * 50)
    
    try:
        # 1. 检查服务器状态
        print("1️⃣ 检查服务器状态...")
        response = requests.get(f"{base_url}/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 服务器在线!")
            print(f"   📊 索引类型: {status.get('index_type', 'unknown')}")
            print(f"   📈 向量数量: {status.get('total_vectors', 0):,}")
            print(f"   📏 维度: {status.get('dimension', 0)}")
            print(f"   🔧 状态: {status.get('status', 'unknown')}")
        else:
            print(f"❌ 服务器返回错误: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到服务器 {base_url}")
        print(f"💡 请确保FAISS服务器正在运行:")
        print(f"   python mock_faiss_server.py")
        return False
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False
    
    try:
        # 2. 测试搜索功能
        print("\n2️⃣ 测试搜索功能...")
        # 创建一个768维的测试向量
        test_vector = [0.1] * 768
        search_request = {
            "query": test_vector,
            "topk": 5
        }
        
        response = requests.post(f"{base_url}/search", json=search_request, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 搜索成功!")
            print(f"   🎯 返回结果数: {len(result.get('ids', [[]])[0])}")
            print(f"   📍 最相似ID: {result.get('ids', [[]])[0][:3] if result.get('ids') and result['ids'][0] else 'None'}")
        else:
            print(f"⚠️ 搜索失败: {response.status_code}")
            print(f"   响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
    
    try:
        # 3. 测试智能缓存判断逻辑
        print("\n3️⃣ 演示智能缓存判断...")
        status = requests.get(f"{base_url}/status").json()
        
        # 模拟不同的索引配置
        test_configs = [
            {"index_type": "Flat", "dim": 768, "desc": "匹配配置 (应该使用缓存)"},
            {"index_type": "HNSW", "dim": 768, "desc": "索引类型不匹配 (不使用缓存)"},
            {"index_type": "Flat", "dim": 1536, "desc": "维度不匹配 (不使用缓存)"},
        ]
        
        server_vectors = status.get('total_vectors', 0)
        server_index = status.get('index_type', '')
        server_dim = status.get('dimension', 0)
        
        for config in test_configs:
            cache_usable = (
                server_vectors >= 1000 and
                server_index == config["index_type"] and 
                server_dim == config["dim"]
            )
            
            cache_status = "✅ 使用缓存" if cache_usable else "❌ 重新加载"
            print(f"   📋 {config['desc']}: {cache_status}")
            
            if not cache_usable:
                reasons = []
                if server_vectors < 1000:
                    reasons.append(f"向量不足({server_vectors}<1000)")
                if server_index != config["index_type"]:
                    reasons.append(f"索引不匹配({server_index}≠{config['index_type']})")
                if server_dim != config["dim"]:
                    reasons.append(f"维度不匹配({server_dim}≠{config['dim']})")
                print(f"      原因: {', '.join(reasons)}")
        
        print("\n🎉 连接测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 缓存测试异常: {e}")
        return False

def test_remote_server(host, port):
    """测试远程服务器连接"""
    print(f"\n🌐 测试远程服务器连接: {host}:{port}")
    print("=" * 50)
    
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ 端口 {port} 可访问")
            return test_faiss_connection(host, port)
        else:
            print(f"❌ 端口 {port} 无法访问")
            print(f"💡 请确保远程服务器上的FAISS服务正在运行")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 FAISS连接测试工具")
    print("=" * 50)
    
    # 测试本地服务器
    local_success = test_faiss_connection("localhost", 8000)
    
    # 测试远程服务器
    remote_success = test_remote_server("***********", 8000)
    
    print("\n📋 测试总结:")
    print(f"   本地服务器 (localhost:8000): {'✅ 成功' if local_success else '❌ 失败'}")
    print(f"   远程服务器 (***********:8000): {'✅ 成功' if remote_success else '❌ 失败'}")
    
    if not remote_success:
        print("\n💡 解决方案:")
        print("   1. 确保远程服务器***********上的FAISS服务正在运行")
        print("   2. 检查防火墙设置，确保端口8000开放")
        print("   3. 验证服务器绑定地址 (0.0.0.0 vs localhost)")
        print("   4. 考虑使用本地服务器进行测试")
