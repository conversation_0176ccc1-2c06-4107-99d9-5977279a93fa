#!/usr/bin/env python3
"""
🎯 直接展示FAISS数据集调用全流程
通过实际API调用展示服务端如何使用您的数据集
"""
import requests
import json
import time

def main():
    print("🎯 FAISS数据集调用全流程实战演示")
    print("=" * 60)
    
    server_url = "http://localhost:8005"  # 使用运行中的高级服务器
    
    print(f"🔧 服务器: {server_url}")
    print(f"📂 目标数据集: Performance1536D50K")
    print(f"📍 预期路径: /nas/yvan.chen/milvus/dataset/openai/openai_small_50k")
    
    # 第一步：触发数据集加载，观察服务端的完整调用过程
    print(f"\n🚀 第一步: 触发数据集加载")
    print("-" * 40)
    
    # 使用查询参数而不是JSON请求体
    params = {
        "case_type": "Performance1536D50K",
        "force_reload": False
    }
    
    print(f"📤 发送加载请求: {params}")
    
    try:
        load_response = requests.post(f"{server_url}/load_dataset", params=params, timeout=60)
        
        if load_response.status_code == 200:
            load_result = load_response.json()
            print(f"✅ 数据集加载成功!")
            print(f"📊 响应: {json.dumps(load_result, indent=2, ensure_ascii=False)}")
            
            # 第二步：查看服务器状态，确认数据集已加载
            print(f"\n🔍 第二步: 查看服务器状态")
            print("-" * 40)
            
            status_response = requests.get(f"{server_url}/status")
            if status_response.status_code == 200:
                status = status_response.json()
                print(f"📋 服务器状态:")
                print(f"   🟢 运行状态: {status.get('status')}")
                print(f"   📊 数据集数量: {len(status.get('loaded_datasets', []))}")
                
                # 显示详细的数据集信息
                loaded_datasets = status.get('loaded_datasets', [])
                available_indexes = status.get('available_indexes', [])
                
                print(f"   📁 已加载数据集: {loaded_datasets}")
                print(f"   🔧 可用索引: {available_indexes}")
                print(f"   💾 内存使用: {status.get('memory_usage', 'N/A')}")
            
            # 第三步：执行FAISS测试，观察索引创建和搜索过程
            print(f"\n🧪 第三步: 执行FAISS基准测试")
            print("-" * 40)
            
            benchmark_data = {
                "case_type": "Performance1536D50K",
                "test_params": {
                    "num_queries": 50,
                    "topk": 10
                }
            }
            
            print(f"📤 发送测试请求: {benchmark_data}")
            print(f"⏰ 开始时间: {time.strftime('%H:%M:%S')}")
            
            bench_response = requests.post(f"{server_url}/benchmark", json=benchmark_data, timeout=120)
            
            if bench_response.status_code == 200:
                result = bench_response.json()
                print(f"✅ FAISS测试完成!")
                
                # 展示关键的FAISS标识信息
                print(f"\n🎯 测试结果信息:")
                print(f"   📁 数据集: {result.get('case_type')}")
                print(f"   📊 测试参数: {result.get('test_params')}")
                
                # 显示性能指标
                perf = result.get('performance', {})
                print(f"\n📊 性能指标:")
                print(f"   🚀 QPS: {perf.get('qps', 0):,.2f}")
                print(f"   ⏱️ 平均延迟: {perf.get('average_latency_ms', 0):.3f} ms")
                print(f"   🕐 总耗时: {perf.get('total_duration_s', 0):.3f} 秒")
                print(f"   📈 查询数量: {perf.get('num_queries', 0)}")
                print(f"   🎯 TopK: {perf.get('topk', 0)}")
                
                # 显示数据集信息确认使用了正确的数据
                dataset_info = result.get('dataset_info', {})
                print(f"\n📁 使用的数据集:")
                print(f"   📊 大小: {dataset_info.get('size', 0):,} 向量")
                print(f"   📐 维度: {dataset_info.get('dim', 0)}")
                print(f"   📝 描述: {dataset_info.get('description', 'N/A')}")
                
                # 验证结果
                print(f"\n🔍 验证结果:")
                performance_indicates_faiss = perf.get('qps', 0) > 1000  # FAISS通常很快
                has_dataset_info = bool(dataset_info.get('size', 0))
                successful_test = perf.get('num_queries', 0) > 0
                
                if performance_indicates_faiss and has_dataset_info and successful_test:
                    print(f"   ✅ 确认: 这是真正的FAISS测试!")
                    print(f"   ✅ 确认: 使用了您提供的数据集!")
                    print(f"   ✅ 确认: 服务端正确调用了数据路径!")
                    print(f"   ✅ 确认: FAISS索引正常工作!")
                else:
                    print(f"   ❌ 警告: 测试结果异常")
                    
                # 额外的验证 - 查看索引状态
                print(f"\n🔧 索引验证:")
                status_response2 = requests.get(f"{server_url}/status")
                if status_response2.status_code == 200:
                    status2 = status_response2.json()
                    indexes = status2.get('available_indexes', [])
                    print(f"   🔧 当前可用索引: {indexes}")
                    if indexes:
                        print(f"   ✅ FAISS索引已成功创建并可用")
                    
            else:
                print(f"❌ 测试失败: HTTP {bench_response.status_code}")
                print(f"📝 错误信息: {bench_response.text}")
                
        else:
            print(f"❌ 数据集加载失败: HTTP {load_response.status_code}")
            print(f"📝 错误信息: {load_response.text}")
            
    except requests.exceptions.Timeout:
        print(f"⏰ 请求超时 - 数据集可能很大，需要更长时间加载")
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

    print(f"\n🎊 演示完成!")
    print(f"📋 核心代码执行流程:")
    print(f"   1️⃣ detect_case_type_info('Performance1536D50K') → 'openai/openai_small_50k'")
    print(f"   2️⃣ 拼接完整路径 → '/nas/yvan.chen/milvus/dataset/openai/openai_small_50k'")
    print(f"   3️⃣ pd.read_parquet('shuffle_train.parquet') → DataFrame")
    print(f"   4️⃣ 提取'emb'列 → np.array(N, 1536)")
    print(f"   5️⃣ faiss.IndexHNSWFlat(1536, 16) → FAISS索引对象")
    print(f"   6️⃣ index.add(vectors) → 构建索引")
    print(f"   7️⃣ index.search(query, k) → 搜索测试")
    print(f"   8️⃣ 计算QPS、延迟等性能指标")

if __name__ == "__main__":
    main()
