#!/usr/bin/env python3
"""
独立运行的FAISS服务器 - 无numpy依赖版本
"""

import os
import sys
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

import faiss
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="FAISS Server", version="1.0.0")

class SearchRequest(BaseModel):
    query: List[float]
    topk: int = 10

class SearchResponse(BaseModel):
    distances: List[float]
    indices: List[int]

class StatusResponse(BaseModel):
    status: str
    index_type: str
    total_vectors: int
    dimension: int
    cache_info: Optional[Dict[str, Any]] = None

# 全局状态
faiss_index = None
total_vectors = 0
dimension = 0
index_type = "unknown"
cache_status = {"initialized": False, "last_insert_count": 0}

@app.post("/insert", response_model=dict)
async def insert_vectors(request: dict):
    """插入向量"""
    global faiss_index, total_vectors, dimension, index_type, cache_status
    
    try:
        embeddings = request.get("embeddings", [])
        if not embeddings:
            raise HTTPException(status_code=400, detail="No embeddings provided")
        
        # 确保所有向量维度一致
        first_dim = len(embeddings[0])
        if not all(len(emb) == first_dim for emb in embeddings):
            raise HTTPException(status_code=400, detail="Inconsistent vector dimensions")
        
        # 初始化索引（如果需要）
        if faiss_index is None:
            dimension = first_dim
            index_type = "IndexFlatL2"
            faiss_index = faiss.IndexFlatL2(dimension)
            logger.info(f"Initialized {index_type} with dimension {dimension}")
        
        # 准备数据
        vectors_array = []
        for emb in embeddings:
            vectors_array.extend(emb)
        
        # 转换为FAISS需要的格式
        import array
        vectors_c = array.array('f', vectors_array)
        
        # 添加向量
        faiss_index.add(vectors_c)
        
        old_total = total_vectors
        total_vectors = faiss_index.ntotal
        cache_status["last_insert_count"] = len(embeddings)
        
        logger.info(f"Inserted {len(embeddings)} vectors, total now: {total_vectors}")
        
        return {
            "message": f"Successfully inserted {len(embeddings)} vectors",
            "total_vectors": total_vectors,
            "previous_total": old_total,
            "dimension": dimension
        }
        
    except Exception as e:
        logger.error(f"Error inserting vectors: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search", response_model=SearchResponse)
async def search_vectors(request: SearchRequest):
    """搜索向量"""
    global faiss_index
    
    if faiss_index is None:
        raise HTTPException(status_code=400, detail="Index not initialized")
    
    try:
        query_vector = request.query
        if len(query_vector) != dimension:
            raise HTTPException(
                status_code=400, 
                detail=f"Query dimension {len(query_vector)} doesn't match index dimension {dimension}"
            )
        
        # 转换查询向量
        import array
        query_c = array.array('f', query_vector)
        
        # 执行搜索
        distances, indices = faiss_index.search(query_c, request.topk)
        
        return SearchResponse(
            distances=distances[0].tolist(),
            indices=indices[0].tolist()
        )
        
    except Exception as e:
        logger.error(f"Error searching vectors: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status", response_model=StatusResponse)
async def get_status():
    """获取服务状态"""
    global faiss_index, total_vectors, dimension, index_type, cache_status
    
    return StatusResponse(
        status="ready" if faiss_index is not None else "not_initialized",
        index_type=index_type,
        total_vectors=total_vectors,
        dimension=dimension,
        cache_info=cache_status
    )

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": time.time()}

@app.delete("/reset")
async def reset_index():
    """重置索引"""
    global faiss_index, total_vectors, dimension, index_type, cache_status
    
    faiss_index = None
    total_vectors = 0
    dimension = 0
    index_type = "unknown"
    cache_status = {"initialized": False, "last_insert_count": 0}
    
    logger.info("Index reset")
    return {"message": "Index reset successfully"}

if __name__ == "__main__":
    print("🚀 启动简化版FAISS服务器...")
    print("📍 服务地址: http://0.0.0.0:8000")
    print("📖 API文档: http://0.0.0.0:8000/docs")
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
