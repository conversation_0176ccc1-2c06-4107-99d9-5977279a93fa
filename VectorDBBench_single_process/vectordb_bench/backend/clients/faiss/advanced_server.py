#!/usr/bin/env python3
"""
高级配置版 FAISS 服务器 - 支持启动时预指定数据集

改进功能：
1. 支持启动时通过环境变量指定具体数据集
2. 支持预加载指定的数据集
3. 更灵活的配置选项
4. 保持向后兼容性
"""

import os
import sys
import asyncio
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
import faiss
import logging
from datetime import datetime
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# 配置读取 - 支持多种配置方式
# ============================================================================

def get_server_config():
    """获取服务器配置"""
    config = {
        "dataset_base_path": os.environ.get('DATASET_LOCAL_DIR', '/nas/yvan.chen/milvus/dataset'),
        "default_case_type": os.environ.get('DATASET_CASE_TYPE', None),
        "specific_dataset_path": os.environ.get('SPECIFIC_DATASET_PATH', None),
        "preload_datasets": os.environ.get('PRELOAD_DATASETS', '').split(',') if os.environ.get('PRELOAD_DATASETS') else [],
        "auto_create_index": os.environ.get('AUTO_CREATE_INDEX', 'true').lower() == 'true'
    }
    
    logger.info(f"服务器配置: {json.dumps(config, indent=2, ensure_ascii=False)}")
    return config

# 获取配置
SERVER_CONFIG = get_server_config()

app = FastAPI(
    title="Advanced FAISS Server with Dataset Pre-configuration",
    description="支持启动时预指定数据集的高级FAISS服务器",
    version="3.0.0"
)

# 全局状态
server_state = {
    "datasets": {},  # 已加载的数据集
    "indexes": {},   # 创建的索引
    "current_index": None,
    "config": SERVER_CONFIG
}

# ============================================================================
# 数据集管理相关的模型
# ============================================================================

class DatasetInfo(BaseModel):
    """数据集信息"""
    name: str
    case_type: str
    path: str
    size: int
    dim: int
    description: str
    vectors: Optional[List[List[float]]] = None
    
class ServerStatus(BaseModel):
    """服务器状态"""
    status: str
    loaded_datasets: List[str]
    available_indexes: List[str]
    config: Dict
    memory_usage: str

class BenchmarkRequest(BaseModel):
    """基准测试请求"""
    case_type: str
    test_params: Dict
    index_config: Optional[Dict] = None

class SearchRequest(BaseModel):
    """搜索请求"""
    query: List[float]
    topk: int
    index_name: Optional[str] = None

# ============================================================================
# 增强的数据集管理函数
# ============================================================================

def detect_case_type_info(case_type: str) -> Dict:
    """根据case_type检测数据集信息"""
    case_mapping = {
        "Performance1536D50K": {
            "path": "openai/openai_small_50k",
            "dim": 1536,
            "size": "50K",
            "description": "OpenAI小规模数据集"
        },
        "Performance1536D500K": {
            "path": "openai/openai_medium_500k", 
            "dim": 1536,
            "size": "500K",
            "description": "OpenAI中规模数据集"
        },
        "Performance768D1M": {
            "path": "cohere/cohere_medium_1m",
            "dim": 768,
            "size": "1M", 
            "description": "Cohere大规模数据集"
        },
        # 可以添加更多case_type
    }
    
    return case_mapping.get(case_type, {
        "path": f"unknown/{case_type.lower()}",
        "dim": 1536,
        "size": "Unknown",
        "description": f"未知数据集 {case_type}"
    })

def get_dataset_path(case_type: str) -> Path:
    """获取数据集路径 - 支持多种配置方式"""
    config = server_state["config"]
    
    # 优先级1: 指定的具体数据集路径
    if config["specific_dataset_path"]:
        specific_path = Path(config["specific_dataset_path"])
        logger.info(f"使用指定的数据集路径: {specific_path}")
        return specific_path
    
    # 优先级2: 使用case_type映射
    case_info = detect_case_type_info(case_type)
    full_path = Path(config["dataset_base_path"]) / case_info["path"]
    logger.info(f"使用映射的数据集路径: {full_path}")
    return full_path

async def load_dataset_async(case_type: str, force_reload: bool = False) -> DatasetInfo:
    """异步加载数据集 - 增强版"""
    if case_type in server_state["datasets"] and not force_reload:
        logger.info(f"数据集 {case_type} 已加载，直接使用缓存")
        return server_state["datasets"][case_type]
    
    logger.info(f"开始加载数据集: {case_type}")
    
    # 获取数据集路径
    dataset_path = get_dataset_path(case_type)
    
    if not dataset_path.exists():
        raise HTTPException(
            status_code=404, 
            detail=f"数据集路径不存在: {dataset_path}"
        )
    
    # 查找parquet文件
    train_files = list(dataset_path.glob("*train*.parquet"))
    if not train_files:
        train_files = list(dataset_path.glob("*.parquet"))
    
    if not train_files:
        raise HTTPException(
            status_code=404,
            detail=f"在 {dataset_path} 中未找到训练数据文件"
        )
    
    # 加载数据文件
    train_file = train_files[0]
    logger.info(f"加载训练文件: {train_file}")
    
    try:
        # 异步加载数据
        loop = asyncio.get_event_loop()
        train_df = await loop.run_in_executor(None, pd.read_parquet, train_file)
        
        # 提取向量数据
        logger.info(f"数据文件列: {train_df.columns.tolist()}")
        logger.info(f"数据形状: {train_df.shape}")
        
        vectors = None
        
        if 'vector' in train_df.columns:
            vector_series = train_df['vector']
            sample_vector = vector_series.iloc[0]
            
            if isinstance(sample_vector, (list, tuple)):
                vectors = np.array(vector_series.tolist(), dtype=np.float32)
            elif isinstance(sample_vector, np.ndarray):
                vectors = np.stack(vector_series.values).astype(np.float32)
            else:
                vectors = np.array([np.array(v, dtype=np.float32) for v in vector_series], dtype=np.float32)
                
        elif 'embedding' in train_df.columns:
            embedding_series = train_df['embedding']
            vectors = np.array(embedding_series.tolist(), dtype=np.float32)
            
        elif 'emb' in train_df.columns:
            # 处理emb列 (OpenAI数据集格式)
            logger.info("使用 'emb' 列")
            emb_series = train_df['emb']
            sample_emb = emb_series.iloc[0]
            logger.info(f"emb样本类型: {type(sample_emb)}")
            
            if isinstance(sample_emb, (list, tuple)):
                vectors = np.array(emb_series.tolist(), dtype=np.float32)
            elif isinstance(sample_emb, np.ndarray):
                vectors = np.stack(emb_series.values).astype(np.float32)
            else:
                vectors = np.array([np.array(v, dtype=np.float32) for v in emb_series], dtype=np.float32)
            
        else:
            # 使用数值列作为向量
            numeric_cols = train_df.select_dtypes(include=[np.number]).columns.tolist()
            vector_cols = [col for col in numeric_cols if col not in ['id', 'label', 'metadata', 'index']]
            
            if vector_cols:
                vectors = train_df[vector_cols].values.astype(np.float32)
            else:
                raise ValueError("无法找到向量数据列")
        
        if vectors is None:
            raise ValueError("无法提取向量数据")
        
        # 限制数据集大小 (防止内存溢出)
        max_vectors = 100000  # 最大10万个向量
        if len(vectors) > max_vectors:
            logger.warning(f"数据集过大({len(vectors)}个向量)，限制为{max_vectors}个")
            vectors = vectors[:max_vectors]
        
        # 创建数据集信息
        case_info = detect_case_type_info(case_type)
        dataset_info = DatasetInfo(
            name=case_type,
            case_type=case_type,
            path=str(dataset_path),
            size=len(vectors),
            dim=vectors.shape[1],
            description=case_info["description"],
            vectors=vectors.tolist()  # 转换为列表格式便于JSON序列化
        )
        
        # 缓存数据集
        server_state["datasets"][case_type] = dataset_info
        logger.info(f"成功加载数据集 {case_type}: {len(vectors)}个{vectors.shape[1]}维向量")
        
        # 如果配置了自动创建索引，则创建FAISS索引
        if server_state["config"]["auto_create_index"]:
            await create_faiss_index_async(case_type, vectors)
        
        return dataset_info
        
    except Exception as e:
        logger.error(f"加载数据集失败: {e}")
        raise HTTPException(status_code=500, detail=f"加载数据集失败: {e}")

async def create_faiss_index_async(case_type: str, vectors: np.ndarray) -> str:
    """异步创建FAISS索引"""
    index_name = f"faiss_index_{case_type}"
    
    logger.info(f"创建FAISS索引: {index_name}")
    
    # 创建HNSW索引
    dim = vectors.shape[1]
    index = faiss.IndexHNSWFlat(dim, 16)  # M=16
    index.hnsw.ef_construction = 200
    
    # 异步添加向量
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, index.add, vectors)
    
    # 设置搜索参数
    index.hnsw.ef_search = 64
    
    # 缓存索引
    server_state["indexes"][index_name] = {
        "index": index,
        "case_type": case_type,
        "dim": dim,
        "size": len(vectors),
        "created_at": datetime.now().isoformat()
    }
    
    # 设置为当前索引
    server_state["current_index"] = index_name
    
    logger.info(f"FAISS索引创建完成: {index_name}")
    return index_name

# ============================================================================
# 服务器启动时的预加载逻辑
# ============================================================================

@app.on_event("startup")
async def startup_event():
    """服务器启动事件 - 预加载数据集"""
    logger.info("🚀 启动增强版FAISS服务器")
    logger.info(f"📋 服务器配置: {json.dumps(server_state['config'], indent=2, ensure_ascii=False)}")
    
    # 如果指定了默认case_type，预加载该数据集
    default_case_type = server_state["config"]["default_case_type"]
    if default_case_type:
        logger.info(f"🎯 预加载默认数据集: {default_case_type}")
        try:
            await load_dataset_async(default_case_type)
            logger.info(f"✅ 默认数据集 {default_case_type} 预加载完成")
        except Exception as e:
            logger.error(f"❌ 预加载默认数据集失败: {e}")
    
    # 预加载指定的数据集列表
    preload_datasets = server_state["config"]["preload_datasets"]
    if preload_datasets and preload_datasets != ['']:
        logger.info(f"🎯 预加载数据集列表: {preload_datasets}")
        for case_type in preload_datasets:
            if case_type and case_type != default_case_type:  # 避免重复加载
                try:
                    await load_dataset_async(case_type)
                    logger.info(f"✅ 数据集 {case_type} 预加载完成")
                except Exception as e:
                    logger.error(f"❌ 预加载数据集 {case_type} 失败: {e}")
    
    logger.info("🎊 服务器启动完成，等待客户端连接...")

# ============================================================================
# API端点
# ============================================================================

@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "Enhanced FAISS Server with Dataset Pre-configuration",
        "version": "3.0.0",
        "status": "running",
        "config": server_state["config"]
    }

@app.get("/status", response_model=ServerStatus)
async def get_server_status():
    """获取服务器状态"""
    import psutil
    process = psutil.Process()
    memory_mb = process.memory_info().rss / 1024 / 1024
    
    return ServerStatus(
        status="running",
        loaded_datasets=list(server_state["datasets"].keys()),
        available_indexes=list(server_state["indexes"].keys()),
        config=server_state["config"],
        memory_usage=f"{memory_mb:.1f} MB"
    )

@app.get("/datasets")
async def list_datasets():
    """列出可用的数据集"""
    available_datasets = []
    
    # 扫描数据集目录
    base_path = Path(server_state["config"]["dataset_base_path"])
    if base_path.exists():
        for case_type in ["Performance1536D50K", "Performance1536D500K", "Performance768D1M"]:
            case_info = detect_case_type_info(case_type)
            dataset_path = base_path / case_info["path"]
            
            available_datasets.append({
                "case_type": case_type,
                "path": str(dataset_path),
                "exists": dataset_path.exists(),
                "loaded": case_type in server_state["datasets"],
                "description": case_info["description"]
            })
    
    return {
        "base_path": str(base_path),
        "datasets": available_datasets,
        "loaded_count": len(server_state["datasets"])
    }

@app.post("/load_dataset")
async def load_dataset_endpoint(case_type: str, force_reload: bool = False):
    """加载指定的数据集"""
    try:
        dataset_info = await load_dataset_async(case_type, force_reload)
        return {
            "status": "success",
            "message": f"数据集 {case_type} 加载成功",
            "dataset": {
                "case_type": dataset_info.case_type,
                "size": dataset_info.size,
                "dim": dataset_info.dim,
                "description": dataset_info.description
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"加载数据集失败: {e}")

@app.post("/benchmark")
async def run_benchmark(request: BenchmarkRequest):
    """运行基准测试"""
    logger.info(f"收到基准测试请求: {request.case_type}")
    
    try:
        # 确保数据集已加载
        if request.case_type not in server_state["datasets"]:
            logger.info(f"数据集 {request.case_type} 未加载，开始加载...")
            await load_dataset_async(request.case_type)
        
        dataset_info = server_state["datasets"][request.case_type]
        
        # 确保索引已创建
        index_name = f"faiss_index_{request.case_type}"
        if index_name not in server_state["indexes"]:
            logger.info(f"索引 {index_name} 不存在，开始创建...")
            vectors = np.array(dataset_info.vectors, dtype=np.float32)
            await create_faiss_index_async(request.case_type, vectors)
        
        # 执行基准测试
        index_info = server_state["indexes"][index_name]
        index = index_info["index"]
        
        # 生成查询向量
        dim = dataset_info.dim
        num_queries = request.test_params.get('num_queries', 1000)
        query_vectors = np.random.random((num_queries, dim)).astype(np.float32)
        
        # 执行搜索测试
        topk = request.test_params.get('topk', 100)
        
        # 测量搜索性能
        start_time = datetime.now()
        distances, indices = index.search(query_vectors, topk)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        qps = num_queries / duration if duration > 0 else 0
        avg_latency = duration * 1000 / num_queries if num_queries > 0 else 0
        
        results = {
            "case_type": request.case_type,
            "test_params": request.test_params,
            "performance": {
                "qps": round(qps, 2),
                "average_latency_ms": round(avg_latency, 3),
                "total_duration_s": round(duration, 3),
                "num_queries": num_queries,
                "topk": topk
            },
            "dataset_info": {
                "size": dataset_info.size,
                "dim": dataset_info.dim,
                "description": dataset_info.description
            },
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"基准测试完成: QPS={qps:.2f}, 延迟={avg_latency:.3f}ms")
        return results
        
    except Exception as e:
        logger.error(f"基准测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"基准测试失败: {e}")

@app.post("/search")
async def search_vectors(request: SearchRequest):
    """向量搜索"""
    try:
        # 使用当前索引或指定索引
        index_name = request.index_name or server_state["current_index"]
        
        if not index_name or index_name not in server_state["indexes"]:
            raise HTTPException(status_code=404, detail="未找到可用的索引")
        
        index = server_state["indexes"][index_name]["index"]
        
        # 执行搜索
        query_vector = np.array([request.query], dtype=np.float32)
        distances, indices = index.search(query_vector, request.topk)
        
        return {
            "distances": distances[0].tolist(),
            "indices": indices[0].tolist(),
            "index_name": index_name
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {e}")

if __name__ == "__main__":
    import uvicorn
    
    # 从环境变量读取配置
    host = os.environ.get('SERVER_HOST', '0.0.0.0')
    port = int(os.environ.get('SERVER_PORT', '8004'))
    
    print("🚀 启动增强版FAISS服务器")
    print(f"📋 配置: {json.dumps(SERVER_CONFIG, indent=2, ensure_ascii=False)}")
    print(f"🌐 地址: http://{host}:{port}")
    
    uvicorn.run(app, host=host, port=port)
