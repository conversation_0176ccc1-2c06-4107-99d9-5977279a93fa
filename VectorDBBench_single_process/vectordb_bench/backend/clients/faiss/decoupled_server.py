#!/usr/bin/env python3
"""
🚀 解耦架构的FAISS服务器
- 服务端管理数据集
- 客户端专注索引配置
- 参数完全解耦
"""

import os
import sys
import asyncio
import pandas as pd
import numpy as np
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import faiss
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ============================================================================
# 服务器配置 - 数据集管理
# ============================================================================

def load_dataset_configs():
    """加载数据集配置"""
    default_configs = {
        "openai_50k": {
            "path": "/nas/yvan.chen/milvus/dataset/openai/openai_small_50k",
            "dim": 1536,
            "size": 50000,
            "description": "OpenAI小规模数据集",
            "file_pattern": "*train*.parquet"
        },
        "openai_500k": {
            "path": "/nas/yvan.chen/milvus/dataset/openai/openai_medium_500k",
            "dim": 1536,
            "size": 500000,
            "description": "OpenAI中规模数据集",
            "file_pattern": "*train*.parquet"
        },
        "cohere_1m": {
            "path": "/nas/yvan.chen/milvus/dataset/cohere/cohere_medium_1m",
            "dim": 768,
            "size": 1000000,
            "description": "Cohere大规模数据集",
            "file_pattern": "*train*.parquet"
        }
    }
    
    # 支持从环境变量读取自定义配置
    env_configs = os.environ.get('DATASET_CONFIGS')
    if env_configs:
        try:
            custom_configs = json.loads(env_configs)
            default_configs.update(custom_configs)
        except json.JSONDecodeError as e:
            logger.warning(f"无法解析DATASET_CONFIGS环境变量: {e}")
    
    # 过滤可用的数据集
    available_datasets = os.environ.get('AVAILABLE_DATASETS', 'openai_50k').split(',')
    filtered_configs = {name: config for name, config in default_configs.items() 
                       if name in available_datasets}
    
    logger.info(f"可用数据集: {list(filtered_configs.keys())}")
    return filtered_configs

SERVER_CONFIG = {
    "dataset_configs": load_dataset_configs(),
    "default_dataset": os.environ.get('DEFAULT_DATASET', 'openai_50k'),
    "auto_preload": os.environ.get('AUTO_PRELOAD', 'true').lower() == 'true'
}

app = FastAPI(
    title="Decoupled FAISS Server",
    description="解耦架构的FAISS服务器 - 服务端管理数据集，客户端专注索引配置",
    version="4.0.0"
)

# 全局状态
server_state = {
    "datasets": {},  # 已加载的数据集 {dataset_name: DatasetInfo}
    "indexes": {},   # 创建的索引 {index_id: IndexInfo}
    "config": SERVER_CONFIG
}

# ============================================================================
# 数据模型
# ============================================================================

class DatasetInfo(BaseModel):
    name: str
    path: str
    dim: int
    size: int
    description: str
    vectors: Optional[List[List[float]]] = None
    loaded_at: str

class IndexInfo(BaseModel):
    index_id: str
    index_type: str
    dataset_name: str
    parameters: Dict[str, Any]
    created_at: str
    
class ServerCapabilities(BaseModel):
    available_datasets: List[Dict[str, Any]]
    supported_index_types: List[str]
    server_status: str

class TestRequest(BaseModel):
    # 索引配置 (客户端关心的)
    index_type: str  # HNSW, IVF, Flat
    index_params: Optional[Dict[str, Any]] = None
    
    # 测试配置
    test_type: str = "performance"  # performance, accuracy, load
    topk: int = 100
    num_queries: int = 1000
    concurrency: int = 1
    
    # 数据集要求 (可选，服务端决定)
    prefer_dataset: Optional[str] = None
    require_dim: Optional[int] = None
    min_size: Optional[int] = None
    max_size: Optional[int] = None

class TestResult(BaseModel):
    test_id: str
    request: TestRequest
    selected_dataset: Dict[str, Any]
    index_info: Dict[str, Any]
    performance: Dict[str, Any]
    timestamp: str

# ============================================================================
# 数据集管理函数
# ============================================================================

async def load_dataset(dataset_name: str) -> DatasetInfo:
    """加载指定数据集"""
    if dataset_name in server_state["datasets"]:
        return server_state["datasets"][dataset_name]
    
    if dataset_name not in server_state["config"]["dataset_configs"]:
        raise HTTPException(status_code=404, detail=f"数据集 {dataset_name} 不存在")
    
    config = server_state["config"]["dataset_configs"][dataset_name]
    dataset_path = Path(config["path"])
    
    if not dataset_path.exists():
        raise HTTPException(status_code=404, detail=f"数据集路径不存在: {dataset_path}")
    
    # 查找数据文件
    pattern = config.get("file_pattern", "*.parquet")
    data_files = list(dataset_path.glob(pattern))
    if not data_files:
        raise HTTPException(status_code=404, detail=f"在{dataset_path}中未找到{pattern}文件")
    
    # 加载数据
    data_file = data_files[0]
    logger.info(f"加载数据集 {dataset_name}: {data_file}")
    
    df = pd.read_parquet(data_file)
    
    # 提取向量 (支持多种列名)
    vector_col = None
    for col_name in ['emb', 'vector', 'embedding']:
        if col_name in df.columns:
            vector_col = col_name
            break
    
    if vector_col is None:
        raise HTTPException(status_code=500, detail="无法找到向量列")
    
    vectors = np.array(df[vector_col].tolist(), dtype=np.float32)
    
    # 限制大小
    max_vectors = 100000
    if len(vectors) > max_vectors:
        vectors = vectors[:max_vectors]
        logger.warning(f"数据集{dataset_name}限制为{max_vectors}个向量")
    
    dataset_info = DatasetInfo(
        name=dataset_name,
        path=str(dataset_path),
        dim=vectors.shape[1],
        size=len(vectors),
        description=config["description"],
        vectors=vectors.tolist(),
        loaded_at=datetime.now().isoformat()
    )
    
    server_state["datasets"][dataset_name] = dataset_info
    logger.info(f"数据集 {dataset_name} 加载完成: {len(vectors)}个{vectors.shape[1]}维向量")
    
    return dataset_info

def select_best_dataset(request: TestRequest) -> str:
    """根据客户端要求选择最佳数据集"""
    available_datasets = server_state["config"]["dataset_configs"]
    
    # 如果指定了偏好数据集且可用，优先使用
    if request.prefer_dataset and request.prefer_dataset in available_datasets:
        config = available_datasets[request.prefer_dataset]
        if meets_requirements(config, request):
            return request.prefer_dataset
    
    # 否则查找满足要求的数据集
    for name, config in available_datasets.items():
        if meets_requirements(config, request):
            return name
    
    # 如果没有完全满足的，返回默认数据集
    default = server_state["config"]["default_dataset"]
    if default in available_datasets:
        return default
    
    # 最后返回第一个可用的
    return list(available_datasets.keys())[0]

def meets_requirements(dataset_config: Dict, request: TestRequest) -> bool:
    """检查数据集是否满足要求"""
    if request.require_dim and dataset_config["dim"] != request.require_dim:
        return False
    
    if request.min_size and dataset_config["size"] < request.min_size:
        return False
    
    if request.max_size and dataset_config["size"] > request.max_size:
        return False
    
    return True

async def create_index(dataset_name: str, index_type: str, index_params: Dict) -> str:
    """创建指定类型的索引"""
    dataset_info = server_state["datasets"][dataset_name]
    vectors = np.array(dataset_info.vectors, dtype=np.float32)
    dim = dataset_info.dim
    
    index_id = f"{index_type}_{dataset_name}_{hash(str(index_params)) % 10000}"
    
    # 创建不同类型的索引
    if index_type == "HNSW":
        m = index_params.get("m", 16)
        index = faiss.IndexHNSWFlat(dim, m)
        index.hnsw.ef_construction = index_params.get("ef_construction", 200)
        index.hnsw.ef_search = index_params.get("ef_search", 64)
        
    elif index_type == "IVF":
        nlist = index_params.get("nlist", 100)
        quantizer = faiss.IndexFlatL2(dim)
        index = faiss.IndexIVFFlat(quantizer, dim, nlist)
        index.train(vectors)
        index.nprobe = index_params.get("nprobe", 10)
        
    elif index_type == "Flat":
        index = faiss.IndexFlatL2(dim)
        
    else:
        raise HTTPException(status_code=400, detail=f"不支持的索引类型: {index_type}")
    
    # 添加向量
    index.add(vectors)
    
    index_info = IndexInfo(
        index_id=index_id,
        index_type=index_type,
        dataset_name=dataset_name,
        parameters=index_params,
        created_at=datetime.now().isoformat()
    )
    
    server_state["indexes"][index_id] = {
        "info": index_info,
        "index": index
    }
    
    logger.info(f"索引创建完成: {index_id}")
    return index_id

# ============================================================================
# API端点
# ============================================================================

@app.get("/")
async def root():
    return {
        "message": "Decoupled FAISS Server",
        "version": "4.0.0",
        "philosophy": "服务端管理数据集，客户端专注索引配置"
    }

@app.get("/capabilities", response_model=ServerCapabilities)
async def get_capabilities():
    """获取服务器能力"""
    datasets_info = []
    for name, config in server_state["config"]["dataset_configs"].items():
        datasets_info.append({
            "name": name,
            "dim": config["dim"],
            "size": config["size"],
            "description": config["description"],
            "loaded": name in server_state["datasets"]
        })
    
    return ServerCapabilities(
        available_datasets=datasets_info,
        supported_index_types=["HNSW", "IVF", "Flat"],
        server_status="running"
    )

@app.get("/datasets")
async def list_datasets():
    """列出数据集状态"""
    return {
        "configured": server_state["config"]["dataset_configs"],
        "loaded": {name: {
            "dim": info.dim,
            "size": info.size,
            "loaded_at": info.loaded_at
        } for name, info in server_state["datasets"].items()},
        "default": server_state["config"]["default_dataset"]
    }

@app.post("/test", response_model=TestResult)
async def run_test(request: TestRequest):
    """执行测试 - 解耦架构的核心API"""
    test_id = f"test_{int(datetime.now().timestamp())}"
    logger.info(f"收到测试请求: {test_id}")
    
    try:
        # 1. 选择最佳数据集
        selected_dataset_name = select_best_dataset(request)
        logger.info(f"选择数据集: {selected_dataset_name}")
        
        # 2. 确保数据集已加载
        dataset_info = await load_dataset(selected_dataset_name)
        
        # 3. 创建索引
        index_params = request.index_params or {}
        index_id = await create_index(selected_dataset_name, request.index_type, index_params)
        
        # 4. 执行测试
        index_obj = server_state["indexes"][index_id]["index"]
        vectors = np.array(dataset_info.vectors, dtype=np.float32)
        
        # 生成查询向量
        query_vectors = np.random.random((request.num_queries, dataset_info.dim)).astype(np.float32)
        
        # 性能测试
        start_time = datetime.now()
        distances, indices = index_obj.search(query_vectors, request.topk)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        qps = request.num_queries / duration if duration > 0 else 0
        avg_latency = duration * 1000 / request.num_queries
        
        # 构建结果
        result = TestResult(
            test_id=test_id,
            request=request,
            selected_dataset={
                "name": selected_dataset_name,
                "dim": dataset_info.dim,
                "size": dataset_info.size,
                "description": dataset_info.description
            },
            index_info={
                "index_id": index_id,
                "index_type": request.index_type,
                "parameters": index_params
            },
            performance={
                "qps": round(qps, 2),
                "avg_latency_ms": round(avg_latency, 3),
                "total_duration_s": round(duration, 3),
                "num_queries": request.num_queries,
                "topk": request.topk
            },
            timestamp=datetime.now().isoformat()
        )
        
        logger.info(f"测试完成: QPS={qps:.2f}, 延迟={avg_latency:.3f}ms")
        return result
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试失败: {e}")

@app.on_event("startup")
async def startup_event():
    """启动时预加载数据集"""
    logger.info("🚀 启动解耦架构FAISS服务器")
    
    if server_state["config"]["auto_preload"]:
        default_dataset = server_state["config"]["default_dataset"]
        if default_dataset:
            try:
                await load_dataset(default_dataset)
                logger.info(f"✅ 预加载默认数据集: {default_dataset}")
            except Exception as e:
                logger.error(f"❌ 预加载失败: {e}")

if __name__ == "__main__":
    import uvicorn
    
    host = os.environ.get('SERVER_HOST', '0.0.0.0')
    port = int(os.environ.get('SERVER_PORT', '8006'))
    
    print("🚀 解耦架构FAISS服务器")
    print(f"📋 可用数据集: {list(SERVER_CONFIG['dataset_configs'].keys())}")
    print(f"🎯 默认数据集: {SERVER_CONFIG['default_dataset']}")
    print(f"🌐 服务地址: http://{host}:{port}")
    
    uvicorn.run(app, host=host, port=port)
