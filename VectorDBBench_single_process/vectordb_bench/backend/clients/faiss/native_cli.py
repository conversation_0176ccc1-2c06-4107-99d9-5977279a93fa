#!/usr/bin/env python3
"""
FAISS 原生客户端 - 完全兼容 VectorDBBench 框架
采用 Milvus 相同的简洁模式，无需环境变量
"""

from typing import Annotated, Unpack
import click
from vectordb_bench.cli.cli import (
    CommonTypedDict,
    cli,
    click_parameter_decorators_from_typed_dict,
    run,
)
from vectordb_bench.backend.clients import DB

class FaissNativeTypedDict(CommonTypedDict):
    """FAISS 原生客户端配置 - 像 Milvus 一样简洁"""
    index_type: Annotated[
        str, click.option(
            "--index-type", 
            type=click.Choice(['Flat', 'HNSW', 'IVFFlat', 'IVFPQ']),
            help="FAISS 索引类型", 
            default="HNSW"
        ),
    ]
    m: Annotated[
        int, click.option(
            "--m", 
            type=int, 
            help="HNSW M 参数", 
            default=16
        ),
    ]
    ef_construction: Annotated[
        int, click.option(
            "--ef-construction", 
            type=int, 
            help="HNSW ef_construction 参数", 
            default=200
        ),
    ]
    ef_search: Annotated[
        int, click.option(
            "--ef-search", 
            type=int, 
            help="HNSW ef_search 参数", 
            default=100
        ),
    ]

@cli.command()
@click_parameter_decorators_from_typed_dict(FaissNativeTypedDict)
def faisslocal(**parameters: Unpack[FaissNativeTypedDict]):
    """
    FAISS 原生本地客户端 - 像 Milvus 一样简洁
    
    使用方式:
    vectordbbench faisslocal --case-type Performance1536D50K --index-type HNSW
    
    🎯 特点:
    - 无需 DATASET_LOCAL_DIR 环境变量
    - 无需启动服务器
    - 完全兼容 VectorDBBench 框架
    - 与 Milvus 使用体验一致
    """
    from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
    
    print(f"🎯 FAISS 原生本地客户端")
    print(f"📋 测试配置:")
    print(f"   📊 Case Type: {parameters.get('case_type')}")
    print(f"   🔧 Index Type: {parameters.get('index_type')}")
    print(f"   📐 HNSW M: {parameters.get('m')}")
    print(f"   🔍 EF Construction: {parameters.get('ef_construction')}")
    print(f"   ⚡ EF Search: {parameters.get('ef_search')}")
    
    # 构建索引配置
    if parameters.get('index_type') == 'HNSW':
        db_case_config = HNSWConfig(
            M=parameters.get('m'),
            efConstruction=parameters.get('ef_construction'),
            efSearch=parameters.get('ef_search'),
        )
    else:
        # 其他索引类型的配置
        from vectordb_bench.backend.clients.faiss_local.config import FlatConfig
        db_case_config = FlatConfig()
    
    # 运行VectorDBBench - 完全复用 Milvus 的模式
    run(
        db=DB.FaissLocal,  # 使用本地 FAISS
        db_config=FaissLocalConfig(
            db_label=parameters["db_label"],
            index_type=parameters.get('index_type'),
        ),
        db_case_config=db_case_config,
        **parameters,
    )

if __name__ == "__main__":
    print("🎯 FAISS 原生本地客户端")
    print("\n✨ 使用方式 (与 Milvus 一样简洁):")
    print("vectordbbench faisslocal --case-type Performance1536D50K")
    print("\n🔧 高级配置:")
    print("vectordbbench faisslocal \\")
    print("    --case-type Performance1536D50K \\")
    print("    --index-type HNSW \\")
    print("    --m 32 --ef-construction 400 --ef-search 200")
    print("\n💡 优势:")
    print("   ✅ 无需环境变量")
    print("   ✅ 无需启动服务器") 
    print("   ✅ 完全原生集成")
    print("   ✅ 与 Milvus 使用体验一致")
