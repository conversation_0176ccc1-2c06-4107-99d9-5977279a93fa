#!/usr/bin/env python3
"""
增强的 FAISS 远程客户端 CLI - 完美的服务端占位方案
客户端零配置，服务端智能处理所有复杂性
"""

from typing import Annotated, Unpack
import click
import re
from pydantic import SecretStr
import os

from vectordb_bench.cli.cli import (
    CommonTypedDict,
    cli,
    click_parameter_decorators_from_typed_dict,
    run,
    get_custom_case_config,
)
from vectordb_bench.backend.clients import DB

class SimpleFaissTypedDict(CommonTypedDict):
    """零配置的FAISS客户端 - 就像Milvus一样简单"""
    uri: Annotated[
        str, click.option(
            "--uri", 
            type=str, 
            help="FAISS服务器地址 (可选，支持自动发现)", 
            default=None
        ),
    ]

def smart_server_discovery() -> tuple[str, int]:
    """智能服务器发现 - 自动找到可用的FAISS服务器"""
    # 常用端口列表
    common_ports = [8011, 8010, 8009, 8008, 8007, 8006, 8005, 8004, 8003, 8002]
    
    print("🔍 正在搜索可用的FAISS服务器...")
    
    try:
        import requests
        for port in common_ports:
            try:
                response = requests.get(f"http://localhost:{port}/status", timeout=2)
                if response.status_code == 200:
                    status = response.json()
                    server_type = status.get('server_type', 'unknown')
                    if 'faiss' in server_type.lower():
                        print(f"✅ 发现FAISS服务器: localhost:{port}")
                        return "localhost", port
            except:
                continue
                
        # 如果没找到，使用默认值并提示
        print("⚠️ 未发现运行中的FAISS服务器，使用默认: localhost:8011")
        print("💡 请确保FAISS服务器正在运行，或使用 --uri 指定地址")
        return "localhost", 8011
        
    except ImportError:
        print("🔧 requests库不可用，使用默认服务器: localhost:8011")
        return "localhost", 8011

def parse_uri(uri: str = None) -> tuple[str, int]:
    """解析URI，支持多种格式"""
    if not uri:
        return smart_server_discovery()
    
    # 解析不同格式的URI
    if uri.startswith(('http://', 'https://')):
        match = re.match(r'https?://([^:]+):(\d+)', uri)
        if match:
            return match.group(1), int(match.group(2))
    elif ':' in uri:
        host, port = uri.split(':', 1)
        return host.strip(), int(port.strip())
    else:
        return uri.strip(), 8011
    
    raise ValueError(f"无法解析URI: {uri}")

def create_mock_dataset_environment():
    """创建模拟数据集环境，避免下载"""
    print("🎭 设置模拟数据集环境...")
    
    # 设置环境变量指向空目录，避免实际下载
    mock_dataset_dir = "/tmp/vectordb_bench_mock"
    os.makedirs(mock_dataset_dir, exist_ok=True)
    
    # 这些设置会让框架认为数据集"已存在"但实际不会使用
    os.environ['DATASET_LOCAL_DIR'] = mock_dataset_dir
    
    # 禁用网络下载
    os.environ['AWS_ACCESS_KEY_ID'] = ''
    os.environ['AWS_SECRET_ACCESS_KEY'] = ''
    
    return mock_dataset_dir

@cli.command()
@click_parameter_decorators_from_typed_dict(SimpleFaissTypedDict)
def faissremote(**parameters: Unpack[SimpleFaissTypedDict]):
    """
    零配置的FAISS远程客户端 - 就像Milvus一样简单！
    
    最简使用：
    python -m vectordb_bench.cli.vectordbbench faissremote --case-type Performance1536D50K
    
    服务端自动处理：数据集加载、索引创建、参数优化！
    """
    from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
    
    print("🎯 FAISS零配置客户端启动")
    print("=" * 50)
    
    # 1. 智能服务器发现
    try:
        host, port = parse_uri(parameters.get("uri"))
        print(f"🌐 目标服务器: {host}:{port}")
    except ValueError as e:
        click.echo(f"❌ URI解析错误: {e}", err=True)
        return
    
    # 2. 验证服务器连接
    server_ok = False
    try:
        import requests
        response = requests.get(f"http://{host}:{port}/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 服务器状态: {status.get('status', 'active')}")
            
            # 显示服务器能力
            if 'capabilities' in status:
                caps = status['capabilities']
                print(f"🎯 服务器能力: {', '.join(caps)}")
            
            # 显示当前数据集（如果有）
            if status.get('current_dataset'):
                dataset = status['current_dataset']
                print(f"📊 当前数据集: {dataset.get('case_type', 'unknown')}")
                
            server_ok = True
        else:
            print(f"⚠️ 服务器响应异常: HTTP {response.status_code}")
    except Exception as e:
        print(f"⚠️ 服务器连接失败: {e}")
        print("💡 请检查服务器是否运行，或使用 --uri 指定正确地址")
    
    if not server_ok:
        print("❌ 无法连接到FAISS服务器，请检查服务器状态")
        return
    
    # 3. 配置数据集切换（服务端处理）
    case_type = parameters.get("case_type")
    if case_type:
        try:
            print(f"📋 请求服务器准备数据集: {case_type}")
            switch_response = requests.post(
                f"http://{host}:{port}/prepare_dataset", 
                json={
                    "case_type": case_type,
                    "auto_optimize": True,
                    "client_mode": "vectordbbench"
                },
                timeout=60
            )
            
            if switch_response.status_code == 200:
                result = switch_response.json()
                if result.get("status") == "success":
                    dataset_info = result.get("dataset_info", {})
                    print(f"✅ 数据集准备完成:")
                    print(f"   📊 数据集: {dataset_info.get('name', 'unknown')}")
                    print(f"   📐 维度: {dataset_info.get('dim', 'unknown')}")
                    print(f"   📈 向量数: {dataset_info.get('size', 'unknown'):,}")
                    print(f"   🎯 索引类型: {dataset_info.get('index_type', 'auto')}")
                else:
                    print(f"⚠️ 数据集准备警告: {result.get('message', 'unknown')}")
            else:
                print(f"⚠️ 数据集准备请求失败: HTTP {switch_response.status_code}")
        except Exception as e:
            print(f"⚠️ 数据集准备请求异常: {e}")
    
    # 4. 创建模拟环境（客户端占位）
    mock_dir = create_mock_dataset_environment()
    print(f"🎭 客户端模拟环境: {mock_dir}")
    
    # 5. 运行VectorDBBench（所有真实工作在服务端）
    print(f"\n🚀 启动VectorDBBench客户端...")
    print(f"📋 测试配置:")
    print(f"   📊 Case: {parameters.get('case_type', 'Performance1536D50K')}")
    print(f"   ⚡ 并发: {parameters.get('num_concurrency', '8,16,32')}")
    print(f"   ⏱️ 时长: {parameters.get('concurrency_duration', 300)}秒")
    print(f"   🎯 模式: 服务端数据集 + 客户端测试")
    
    # 获取自定义配置
    parameters["custom_case"] = get_custom_case_config(parameters)
    
    # 运行测试（框架会连接到服务端进行实际测试）
    run(
        db=DB.Faiss,
        db_config=FaissConfig(
            host=host,
            port=port,
            index_type="ServerManaged",  # 服务端管理
            case_type=case_type
        ),
        db_case_config=FaissDBCaseConfig(),
        **parameters,
    )

if __name__ == "__main__":
    print("🎯 FAISS零配置远程客户端")
    print("\n✨ 极简使用方式（自动发现服务器）:")
    print("python -m vectordb_bench.cli.vectordbbench faissremote --case-type Performance1536D50K")
    print("\n🔧 指定服务器:")
    print("python -m vectordb_bench.cli.vectordbbench faissremote \\")
    print("    --uri localhost:8011 \\")
    print("    --case-type Performance1536D50K")
    print("\n🚀 完整测试:")
    print("python -m vectordb_bench.cli.vectordbbench faissremote \\")
    print("    --uri localhost:8011 \\")
    print("    --case-type Performance1536D50K \\")
    print("    --concurrency-duration 300 \\")
    print("    --num-concurrency 8,16,32")
    print("\n💡 核心特性:")
    print("   🔍 自动服务器发现")
    print("   🎭 客户端数据集占位")
    print("   📊 服务端数据集管理")
    print("   ⚡ 零配置即用")
    print("   🛡️ 向后兼容VectorDBBench")
