"""
简易 REST Faiss 客户端 —— 适配 VectorDBBench 的 VectorDB 抽象
"""

import requests
from contextlib import contextmanager
from typing import List, Tuple

from vectordb_bench.backend.clients.api import (
    VectorDB,
    DBConfig,
    DBCaseConfig,
    FilterOp,  # 添加FilterOp导入
)

# 避免循环引用，config 单独放在 faiss/config.py
from .config import FaissConfig, FaissDBCaseConfig


class FaissClient(VectorDB):
    """调用你写的 FastAPI 服务 (create_index / insert_bulk / search)"""

    supported_filter_types = [FilterOp.NonFilter]  # 修复：支持NonFilter

    def __init__(
        self,
        dim: int,
        db_config: DBConfig | dict,
        db_case_config: DBCaseConfig | None,
        collection_name: str = "faiss_collection",  # 提供默认值
        drop_old: bool = False,
        **kwargs,
    ) -> None:
        cfg = (
            db_config if isinstance(db_config, dict) else db_config.to_dict()
        )
        self.base_url: str = f"http://{cfg['host']}:{cfg['port']}"
        self.dim = dim
        self.index_type = cfg.get("index_type", "Flat")
        self.session = requests.Session()

        # 初始化索引 - 为IVF索引增加超时时间
        timeout = 300 if self.index_type.startswith("IVF") else 60  # IVF索引需要更长时间训练
        resp = self.session.post(
            f"{self.base_url}/create_index",
            json={"dim": self.dim, "index_type": self.index_type},
            timeout=timeout,
        )
        resp.raise_for_status()

    # --------------------- lifecycle ---------------------
    @contextmanager
    def init(self):
        """VectorDBBench 在每个进程里都会 with obj.init()"""
        yield                              # 无长连接，可直接 yield

    # --------------------- CRUD --------------------------
    def insert_embeddings(
        self,
        embeddings: List[List[float]],
        metadata: List[int],
        labels_data: List[str] | None = None,
        **kwargs,
    ) -> Tuple[int, Exception | None]:
        # 检查服务端是否已有足够的缓存数据，如果有则跳过所有插入操作
        try:
            status_resp = self.session.get(f"{self.base_url}/status", timeout=5)
            if status_resp.status_code == 200:
                status = status_resp.json()
                vectors_loaded = status.get('vectors_loaded', 0)
                
                # 如果服务端有100万+向量，认为数据已经充足，跳过插入
                if vectors_loaded >= 1000000:
                    # 不打印过多日志，避免刷屏
                    if not hasattr(self, '_cache_message_shown'):
                        print(f"🚀 智能缓存模式：服务端已有 {vectors_loaded:,} 个向量，跳过数据加载")
                        self._cache_message_shown = True
                    return len(embeddings), None  # 返回成功但不实际插入
                    
                # 如果数据量不足，继续插入但给出提示
                elif vectors_loaded > 0:
                    if not hasattr(self, '_partial_cache_shown'):
                        print(f"⚠️ 服务端数据不足 ({vectors_loaded:,}个向量)，继续加载数据")
                        self._partial_cache_shown = True
        except:
            pass  # 网络错误时继续正常流程
        
        # 正常的插入逻辑
        resp = self.session.post(
            f"{self.base_url}/insert_bulk",
            json={"vectors": embeddings},
            timeout=120,
        )
        resp.raise_for_status()
        return len(embeddings), None

    def search_embedding(self, query: List[float], k: int = 100) -> List[int]:
        resp = self.session.post(
            f"{self.base_url}/search",
            json={"query": query, "topk": k},
            timeout=60,
        )
        resp.raise_for_status()
        return [int(x) for x in resp.json()["ids"][0]]

    # --------------------- optimize ----------------------
    def optimize(self, data_size: int | None = None):
        """FAISS (Flat / IVF) 本 demo 不做额外优化"""
        return

