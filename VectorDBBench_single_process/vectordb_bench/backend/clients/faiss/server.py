from fastapi import FastAPI
from pydantic import BaseModel
import numpy as np
import faiss

app = FastAPI()
index = None

class CreateIndexRequest(BaseModel):
    dim: int
    index_type: str

class InsertRequest(BaseModel):
    vectors: list[list[float]]

class SearchRequest(BaseModel):
    query: list[float]
    topk: int

@app.post("/create_index")
def create_index(req: CreateIndexRequest):
    global index
    if req.index_type == "Flat":
        index = faiss.IndexFlatL2(req.dim)
    elif req.index_type.startswith("IVF"):
        nlist = int(req.index_type.split("IVF")[1])
        quantizer = faiss.IndexFlatL2(req.dim)
        index = faiss.IndexIVFFlat(quantizer, req.dim, nlist)
        
        # 修复：确保训练点数量至少是聚类数量的2倍
        train_size = max(nlist * 2, 10000)  # 至少是nlist的2倍，但不少于10000
        train_data = np.random.rand(train_size, req.dim).astype("float32")
        index.train(train_data)
    else:
        return {"error": "Unsupported index type"}
    return {"status": "index created"}

@app.post("/insert_bulk")
def insert_vectors(req: InsertRequest):
    global index
    vecs = np.array(req.vectors, dtype="float32")
    index.add(vecs)
    return {"status": f"Inserted {len(vecs)} vectors"}

@app.post("/search")
def search(req: SearchRequest):
    global index
    query = np.array([req.query], dtype="float32")
    D, I = index.search(query, req.topk)
    return {"distances": D.tolist(), "ids": I.tolist()}

