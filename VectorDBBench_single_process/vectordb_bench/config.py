"""
VectorDBBench 配置文件
"""
import os
import pathlib
import environs
from pathlib import Path

env = environs.Env()
env.read_env(path=".env", recurse=False)

# 从__init__.py导入的配置
ALIYUN_OSS_URL = "assets.zilliz.com.cn/benchmark/"
AWS_S3_URL = "assets.zilliz.com/benchmark/"

LOG_LEVEL = env.str("LOG_LEVEL", "INFO")

DEFAULT_DATASET_URL = env.str("DEFAULT_DATASET_URL", AWS_S3_URL)
DATASET_LOCAL_DIR = env.path("DATASET_LOCAL_DIR", "/tmp/vectordb_bench/dataset")
NUM_PER_BATCH = env.int("NUM_PER_BATCH", 100)
TIME_PER_BATCH = 1  # 1s. for streaming insertion.
MAX_INSERT_RETRY = 5
MAX_SEARCH_RETRY = 5

LOAD_MAX_TRY_COUNT = 10

DROP_OLD = env.bool("DROP_OLD", True)
USE_SHUFFLED_DATA = env.bool("USE_SHUFFLED_DATA", True)

NUM_CONCURRENCY = env.list("NUM_CONCURRENCY", [1, 5, 10, 20, 30, 40, 60, 80], subcast=int)

CONCURRENCY_DURATION = 120

CONCURRENCY_TIMEOUT = 3600

RESULTS_LOCAL_DIR = env.path(
    "RESULTS_LOCAL_DIR",
    pathlib.Path(__file__).parent.joinpath("results"),
)
CONFIG_LOCAL_DIR = env.path(
    "CONFIG_LOCAL_DIR",
    pathlib.Path(__file__).parent.joinpath("config-files"),
)

K_DEFAULT = 100  # default return top k nearest neighbors during search
CUSTOM_CONFIG_DIR = pathlib.Path(__file__).parent.joinpath("custom/custom_case.json")

CAPACITY_TIMEOUT_IN_SECONDS = 24 * 3600  # 24h
LOAD_TIMEOUT_DEFAULT = 24 * 3600  # 24h
LOAD_TIMEOUT_768D_100K = 24 * 3600  # 24h
LOAD_TIMEOUT_768D_1M = 24 * 3600  # 24h
LOAD_TIMEOUT_768D_10M = 240 * 3600  # 10d
LOAD_TIMEOUT_768D_100M = 2400 * 3600  # 100d

LOAD_TIMEOUT_1536D_500K = 24 * 3600  # 24h
LOAD_TIMEOUT_1536D_5M = 240 * 3600  # 10d

LOAD_TIMEOUT_1024D_1M = 24 * 3600  # 24h
LOAD_TIMEOUT_1024D_10M = 240 * 3600  # 10d

OPTIMIZE_TIMEOUT_DEFAULT = 24 * 3600  # 24h
OPTIMIZE_TIMEOUT_768D_100K = 24 * 3600  # 24h
OPTIMIZE_TIMEOUT_768D_1M = 24 * 3600  # 24h
OPTIMIZE_TIMEOUT_768D_10M = 240 * 3600  # 10d
OPTIMIZE_TIMEOUT_768D_100M = 2400 * 3600  # 100d

OPTIMIZE_TIMEOUT_1536D_500K = 24 * 3600  # 24h
OPTIMIZE_TIMEOUT_1536D_5M = 240 * 3600  # 10d

OPTIMIZE_TIMEOUT_1024D_1M = 24 * 3600  # 24h
OPTIMIZE_TIMEOUT_1024D_10M = 240 * 3600  # 10d

# 基础目录配置
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
CONFIG_LOCAL_DIR = os.environ.get("CONFIG_LOCAL_DIR", os.path.join(ROOT_DIR, "config-files"))
DEFAULT_RESULT_DIR = os.environ.get("DEFAULT_RESULT_DIR", os.path.expanduser("~/.vectordb_bench/results"))
DATASET_DIR = os.environ.get("DATASET_DIR", os.path.expanduser("~/.vectordb_bench/dataset"))
DEFAULT_DATASET = os.path.join(DATASET_DIR, "sift_1m")

# 确保目录存在
Path(DEFAULT_RESULT_DIR).mkdir(parents=True, exist_ok=True)
Path(CONFIG_LOCAL_DIR).mkdir(parents=True, exist_ok=True)
Path(DATASET_DIR).mkdir(parents=True, exist_ok=True)

# FAISS相关配置
FAISS_DEFAULT_INDEX = "HNSW"
FAISS_DEFAULT_M = 32
FAISS_DEFAULT_EF_CONSTRUCTION = 400
FAISS_DEFAULT_EF_SEARCH = 128