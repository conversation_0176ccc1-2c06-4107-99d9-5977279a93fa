#!/bin/bash
"""
FAISS 本地模式简单测试脚本
证明无需环境变量即可运行
"""

echo "🎯 FAISS 本地模式测试"
echo "===================="

echo "📋 测试配置:"
echo "  - 案例: Performance1536D50K (OpenAI 50K, 1536维)"
echo "  - 索引: HNSW (m=16, ef_construction=200, ef_search=100)"
echo "  - 测试: 数据加载 + 串行搜索"

echo ""
echo "🚀 开始测试..."

# 注意：无需设置任何环境变量！
python -m vectordb_bench.cli.vectordbbench faisslocalhnsw \
    --case-type Performance1536D50K \
    --m 16 \
    --ef-construction 200 \
    --ef-search 100 \
    --load \
    --search-serial \
    --db-label "faiss_local_test_$(date +%Y%m%d_%H%M%S)"

echo "✅ 测试完成！"
echo "📊 查看结果在 VectorDBBench 界面或日志中"
