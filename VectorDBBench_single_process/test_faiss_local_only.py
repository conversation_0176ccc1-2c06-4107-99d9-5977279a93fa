#!/usr/bin/env python3
"""
Quick fix: Run FAISS local with local-only dataset configuration
"""

import os
import sys
import subprocess

# Set environment
os.environ['DATASET_LOCAL_DIR'] = '/home/<USER>/VectorDBBench/dataset'

# Add path 
sys.path.insert(0, '/home/<USER>/VectorDBBench')

def patch_openai_dataset():
    """Temporarily patch OpenAI dataset to disable remote resource"""
    from vectordb_bench.backend.dataset import OpenAI
    
    # Store original value
    original_with_remote = OpenAI.with_remote_resource
    
    # Disable remote resource for local testing
    OpenAI.with_remote_resource = False
    print(f"🔧 Patched OpenAI.with_remote_resource: {original_with_remote} → {OpenAI.with_remote_resource}")
    
    return original_with_remote

def run_faiss_local_test():
    """Run FAISS local test with patches applied"""
    print("🚀 Running FAISS local test with local-only dataset...")
    
    # Patch the dataset
    original_with_remote = patch_openai_dataset()
    
    try:
        # Run the command
        cmd = [
            'python', '-m', 'vectordb_bench.cli.vectordbbench', 'faisslocalhnsw',
            '--case-type', 'Performance1536D50K',
            '--m', '16',
            '--ef-construction', '200', 
            '--ef-search', '100',
            '--concurrency-duration', '30',
            '--num-concurrency', '1'
        ]
        
        print(f"📋 Command: {' '.join(cmd)}")
        
        # Run with timeout
        result = subprocess.run(cmd, timeout=120, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ FAISS local test completed successfully!")
            print("Output:", result.stdout[-1000:])  # Last 1000 chars
        else:
            print(f"❌ Test failed with return code: {result.returncode}")
            print("Error:", result.stderr[-1000:])  # Last 1000 chars
            print("Output:", result.stdout[-1000:])  # Last 1000 chars
            
    except subprocess.TimeoutExpired:
        print("⏰ Test timed out after 120 seconds")
    except Exception as e:
        print(f"❌ Error running test: {e}")
    finally:
        # Restore original value
        from vectordb_bench.backend.dataset import OpenAI
        OpenAI.with_remote_resource = original_with_remote
        print(f"🔄 Restored OpenAI.with_remote_resource to {original_with_remote}")

if __name__ == "__main__":
    print("🎯 FAISS Local Test with Local Dataset Only")
    print(f"📂 Dataset directory: {os.environ.get('DATASET_LOCAL_DIR')}")
    run_faiss_local_test()
