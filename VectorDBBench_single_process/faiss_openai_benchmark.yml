faisslocalhnsw:
  # OpenAI Small 50K 测试
  - case_type: PerformanceCustomDataset
    custom_case_name: "OpenAI_Small_50K_HNSW"
    custom_case_description: "OpenAI small dataset 50K vectors with HNSW index"
    custom_dataset_name: "openai_small_50k"
    custom_dataset_dir: "openai_small_50k"
    custom_dataset_size: "50000"
    custom_dataset_dim: "1536"
    custom_dataset_metric_type: "COSINE"
    custom_dataset_file_count: "3"
    custom_dataset_use_shuffled: true
    custom_dataset_with_gt: true
    m: 16
    ef_construction: 200
    ef_search: 64
    num_concurrency: "1,4,8"
    k: 100
    db_label: "faiss_hnsw_openai_small"
    
  # OpenAI Medium 500K 测试  
  - case_type: PerformanceCustomDataset
    custom_case_name: "OpenAI_Medium_500K_HNSW"
    custom_case_description: "OpenAI medium dataset 500K vectors with HNSW index"
    custom_dataset_name: "openai_medium_500k"
    custom_dataset_dir: "openai_medium_500k"
    custom_dataset_size: "500000"
    custom_dataset_dim: "1536"
    custom_dataset_metric_type: "COSINE"
    custom_dataset_file_count: "3"
    custom_dataset_use_shuffled: true
    custom_dataset_with_gt: true
    m: 32
    ef_construction: 400
    ef_search: 128
    num_concurrency: "1,8,16"
    k: 100
    db_label: "faiss_hnsw_openai_medium"
