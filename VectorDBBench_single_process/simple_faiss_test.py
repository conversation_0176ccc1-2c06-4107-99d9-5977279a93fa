#!/usr/bin/env python3
"""
简化的 FAISS 测试以诊断维度问题
"""

import numpy as np
import sys
import os

# 导入必要的模块
try:
    from vectordb_bench.backend.clients.faiss_local.faiss_local import FaissLocalClient
    from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
    from vectordb_bench.backend.clients.api import MetricType
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_simple():
    """简单测试"""
    print("🧪 开始简单测试...")
    
    # 生成测试数据
    np.random.seed(42)
    dim = 1536
    n_vectors = 100
    
    train_vectors = np.random.normal(0, 1, (n_vectors, dim)).astype(np.float32)
    train_vectors = train_vectors / np.linalg.norm(train_vectors, axis=1, keepdims=True)
    
    print(f"📊 生成了 {n_vectors} 个 {dim}D 向量")
    print(f"   向量形状: {train_vectors.shape}")
    
    # 创建客户端
    db_config = FaissLocalConfig(
        db_label="test_simple",
        index_type="HNSW"
    )
    
    case_config = HNSWConfig(
        m=16,
        ef_construction=200,
        ef_search=64,
        metric_type=MetricType.COSINE,
    )
    
    client = FaissLocalClient(
        dim=dim,
        db_config=db_config,
        db_case_config=case_config,
        metric_type=MetricType.COSINE,
        drop_old=True
    )
    
    print(f"🏗️  客户端创建成功，期望维度: {client.dim}")
    
    # 插入数据
    print("📥 插入向量数据...")
    ids = list(range(n_vectors))
    embeddings = train_vectors.tolist()
    
    count, error = client.insert_embeddings(embeddings, ids)
    if error:
        print(f"❌ 插入失败: {error}")
        return False
    
    print(f"✅ 插入了 {count} 个向量")
    
    # 检查向量状态
    if client.vectors is not None:
        print(f"📊 客户端向量形状: {client.vectors.shape}")
    else:
        print("❌ 客户端向量为 None")
        return False
    
    # 构建索引
    print("🏗️  构建索引...")
    try:
        client.optimize()
        print("✅ 索引构建成功")
    except Exception as e:
        print(f"❌ 索引构建失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试搜索
    print("🔍 测试搜索...")
    test_query = train_vectors[0]  # 使用第一个向量作为查询
    
    try:
        results = client.search_embedding(test_query.tolist(), k=10)
        print(f"✅ 搜索成功，找到 {len(results)} 个结果")
        print(f"   结果ID: {results[:5]}...")  # 显示前5个结果
        return True
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple()
    if success:
        print("\n🎉 测试成功!")
    else:
        print("\n💥 测试失败!")
    
    sys.exit(0 if success else 1)
