#!/usr/bin/env python3
"""
创建分片索引
将31GB的大索引分解为多个小索引，降低内存压力
"""

import os
import sys
import time
import numpy as np
import faiss
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_large_index(index_path: str):
    """分析大索引的结构"""
    logger.info(f"🔍 分析大索引: {index_path}")
    
    file_size_gb = os.path.getsize(index_path) / (1024**3)
    logger.info(f"📁 文件大小: {file_size_gb:.2f} GB")
    
    try:
        # 只读取索引头部信息，不加载全部数据
        logger.info("📊 读取索引元信息...")
        
        # 使用较小的内存来读取索引信息
        index = faiss.read_index(index_path)
        
        logger.info(f"✅ 索引信息:")
        logger.info(f"   向量数量: {index.ntotal:,}")
        logger.info(f"   维度: {index.d}")
        logger.info(f"   索引类型: {type(index).__name__}")
        
        if hasattr(index, 'hnsw'):
            logger.info(f"   HNSW M: {index.hnsw.M}")
            logger.info(f"   HNSW efConstruction: {index.hnsw.efConstruction}")
        
        return {
            "total_vectors": index.ntotal,
            "dimension": index.d,
            "index_type": type(index).__name__,
            "file_size_gb": file_size_gb
        }
        
    except Exception as e:
        logger.error(f"❌ 分析失败: {e}")
        return None

def create_smaller_index_from_milvus(target_vectors=1000000):
    """从Milvus创建更小的索引"""
    logger.info(f"🎯 创建 {target_vectors:,} 向量的索引...")
    
    try:
        from pymilvus import connections, Collection
        
        # 连接Milvus
        connections.connect(host="localhost", port="19530")
        
        # 获取集合
        collection = Collection("VectorDBBenchCollection")
        collection.load()
        
        logger.info("📤 从Milvus导出向量...")
        
        # 分批导出向量
        batch_size = 10000
        all_vectors = []
        exported = 0
        
        while exported < target_vectors:
            current_batch = min(batch_size, target_vectors - exported)
            
            results = collection.query(
                expr="",
                output_fields=["vector"],
                offset=exported,
                limit=current_batch
            )
            
            if not results:
                break
                
            batch_vectors = [result["vector"] for result in results]
            all_vectors.extend(batch_vectors)
            exported += len(batch_vectors)
            
            if exported % 100000 == 0:
                logger.info(f"   已导出: {exported:,}/{target_vectors:,}")
        
        # 创建FAISS索引
        vectors = np.array(all_vectors, dtype=np.float32)
        dim = vectors.shape[1]
        
        logger.info(f"🏗️ 创建FAISS索引: {len(vectors):,} 向量, {dim} 维")
        
        # 使用较小的HNSW参数
        index = faiss.IndexHNSWFlat(dim, 16)  # M=16 (vs 30)
        index.hnsw.efConstruction = 200  # efConstruction=200 (vs 360)
        
        # 添加向量
        index.add(vectors)
        
        # 保存索引
        output_path = f"/home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_{target_vectors//1000}k_small.index"
        faiss.write_index(index, output_path)
        
        file_size_gb = os.path.getsize(output_path) / (1024**3)
        logger.info(f"✅ 小索引创建完成:")
        logger.info(f"   文件: {output_path}")
        logger.info(f"   大小: {file_size_gb:.2f} GB")
        logger.info(f"   向量: {index.ntotal:,}")
        
        return output_path
        
    except Exception as e:
        logger.error(f"❌ 创建小索引失败: {e}")
        return None

def create_sharded_indexes(large_index_path: str, shard_size=2000000):
    """将大索引分解为多个分片"""
    logger.info(f"🔪 分片大索引: {large_index_path}")
    logger.info(f"📦 分片大小: {shard_size:,} 向量/分片")
    
    try:
        # 这个方法需要大量内存，可能不可行
        logger.warning("⚠️ 此方法需要加载完整索引，可能导致内存不足")
        
        # 替代方案：从原始数据重新创建多个小索引
        logger.info("🔄 建议使用从Milvus重新创建的方式")
        
        return create_multiple_small_indexes()
        
    except Exception as e:
        logger.error(f"❌ 分片失败: {e}")
        return []

def create_multiple_small_indexes():
    """创建多个小索引"""
    logger.info("🏭 创建多个小索引...")
    
    sizes = [500000, 1000000, 2000000]  # 500K, 1M, 2M
    created_indexes = []
    
    for size in sizes:
        logger.info(f"📦 创建 {size:,} 向量索引...")
        
        try:
            index_path = create_smaller_index_from_milvus(size)
            if index_path:
                created_indexes.append(index_path)
                
        except Exception as e:
            logger.error(f"❌ 创建 {size:,} 向量索引失败: {e}")
    
    return created_indexes

def test_memory_usage():
    """测试不同大小索引的内存使用"""
    logger.info("🧪 测试内存使用情况...")
    
    try:
        import psutil
        process = psutil.Process()
        
        # 测试小索引
        small_indexes = [
            "/home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_500k_small.index",
            "/home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_1000k_small.index",
        ]
        
        for index_path in small_indexes:
            if os.path.exists(index_path):
                logger.info(f"📊 测试: {os.path.basename(index_path)}")
                
                memory_before = process.memory_info().rss / (1024**3)
                
                try:
                    index = faiss.read_index(index_path)
                    memory_after = process.memory_info().rss / (1024**3)
                    
                    logger.info(f"   向量数: {index.ntotal:,}")
                    logger.info(f"   内存增加: {memory_after - memory_before:.2f} GB")
                    
                    # 释放内存
                    del index
                    
                except Exception as e:
                    logger.error(f"   加载失败: {e}")
        
    except ImportError:
        logger.warning("⚠️ psutil未安装，无法测试内存使用")

def main():
    """主函数"""
    logger.info("🎯 FAISS索引分片工具")
    logger.info("=" * 50)
    
    large_index_path = "/home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index"
    
    # 1. 分析大索引
    if os.path.exists(large_index_path):
        logger.info("📊 分析现有大索引...")
        info = analyze_large_index(large_index_path)
        if info:
            logger.info(f"大索引信息: {info}")
    
    # 2. 创建小索引
    logger.info("🏭 创建小索引...")
    created_indexes = create_multiple_small_indexes()
    
    if created_indexes:
        logger.info(f"✅ 成功创建 {len(created_indexes)} 个小索引:")
        for idx_path in created_indexes:
            logger.info(f"   - {idx_path}")
    
    # 3. 测试内存使用
    logger.info("🧪 测试内存使用...")
    test_memory_usage()
    
    logger.info("🎉 完成!")

if __name__ == "__main__":
    main()
