#!/usr/bin/env python3
"""
创建符合VectorDBBench期望格式的OpenAI数据集
"""

import pandas as pd
import numpy as np
from pathlib import Path

def create_vectordb_compatible_dataset():
    """创建完全符合VectorDBBench期望的数据集"""
    
    dataset_dir = Path("/home/<USER>/VectorDBBench/dataset/openai/openai_small_50k")
    dataset_dir.mkdir(parents=True, exist_ok=True)
    
    print("🏗️ 创建VectorDBBench兼容数据集...")
    print(f"📂 目标目录: {dataset_dir}")
    
    # 设置数据集参数
    train_size = 1000  # 训练数据大小
    test_size = 100    # 测试数据大小
    dim = 1536         # 向量维度
    k = 100           # 近邻数量
    
    print(f"📊 数据集参数:")
    print(f"   训练数据: {train_size:,} 向量")
    print(f"   测试数据: {test_size:,} 向量") 
    print(f"   向量维度: {dim}")
    print(f"   近邻数量: {k}")
    
    # 1. 创建训练数据 (shuffle_train.parquet)
    print("\n🔧 创建训练数据...")
    np.random.seed(42)  # 固定随机种子保证可重现
    
    train_vectors = np.random.random((train_size, dim)).astype(np.float32)
    # 归一化向量 (COSINE距离需要)
    train_vectors = train_vectors / np.linalg.norm(train_vectors, axis=1, keepdims=True)
    
    train_df = pd.DataFrame({
        'id': range(train_size),
        'emb': train_vectors.tolist()
    })
    
    train_file = dataset_dir / "shuffle_train.parquet"
    train_df.to_parquet(train_file, index=False)
    print(f"✅ {train_file.name}: {train_df.shape}")
    
    # 2. 创建测试数据 (test.parquet)
    print("🔧 创建测试数据...")
    
    test_vectors = np.random.random((test_size, dim)).astype(np.float32) 
    test_vectors = test_vectors / np.linalg.norm(test_vectors, axis=1, keepdims=True)
    
    test_df = pd.DataFrame({
        'id': range(test_size),
        'emb': test_vectors.tolist()
    })
    
    test_file = dataset_dir / "test.parquet"
    test_df.to_parquet(test_file, index=False)
    print(f"✅ {test_file.name}: {test_df.shape}")
    
    # 3. 创建ground truth邻居 (neighbors.parquet)
    print("🔧 创建ground truth邻居...")
    
    # 计算真实的最近邻（使用余弦相似度）
    from sklearn.metrics.pairwise import cosine_similarity
    
    # 计算测试向量与训练向量的相似度
    similarities = cosine_similarity(test_vectors, train_vectors)
    
    # 获取每个测试向量的top-k最近邻
    neighbors_list = []
    distances_list = []
    
    for i in range(test_size):
        # 获取相似度最高的k个邻居的索引
        top_k_indices = np.argsort(similarities[i])[-k:][::-1]  # 降序排列
        top_k_distances = similarities[i][top_k_indices]
        
        neighbors_list.append(top_k_indices.tolist())
        distances_list.append(top_k_distances.tolist())
    
    neighbors_df = pd.DataFrame({
        'neighbors': neighbors_list,
        'distances': distances_list
    })
    
    neighbors_file = dataset_dir / "neighbors.parquet"
    neighbors_df.to_parquet(neighbors_file, index=False)
    print(f"✅ {neighbors_file.name}: {neighbors_df.shape}")
    
    # 4. 创建标量标签 (scalar_labels.parquet)
    print("🔧 创建标量标签...")
    
    labels_df = pd.DataFrame({
        'id': range(train_size),
        'label': [f"category_{i % 10}" for i in range(train_size)]  # 10个类别
    })
    
    labels_file = dataset_dir / "scalar_labels.parquet"
    labels_df.to_parquet(labels_file, index=False)
    print(f"✅ {labels_file.name}: {labels_df.shape}")
    
    # 验证所有文件
    print("\n✅ 验证生成的数据集:")
    total_size = 0
    
    for filename in ["shuffle_train.parquet", "test.parquet", "neighbors.parquet", "scalar_labels.parquet"]:
        filepath = dataset_dir / filename
        if filepath.exists():
            df = pd.read_parquet(filepath)
            size = filepath.stat().st_size
            total_size += size
            print(f"   📄 {filename}: {df.shape} - {size:,} bytes - 列: {df.columns.tolist()}")
        else:
            print(f"   ❌ {filename}: 不存在")
    
    print(f"\n📊 数据集总大小: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")
    
    return dataset_dir

if __name__ == "__main__":
    print("🎯 创建VectorDBBench兼容的OpenAI数据集")
    print("=" * 60)
    
    dataset_dir = create_vectordb_compatible_dataset()
    
    print(f"\n🎊 数据集创建完成！")
    print(f"📁 位置: {dataset_dir}")
    print(f"\n🚀 现在可以运行FAISS测试:")
    print(f"vectordbbench faisslocalhnsw --case-type Performance1536D50K --m 16 --ef-construction 200 --ef-search 100")
