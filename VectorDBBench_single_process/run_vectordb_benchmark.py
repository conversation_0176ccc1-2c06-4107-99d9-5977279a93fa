#!/usr/bin/env python3
"""
使用 VectorDBBench 框架运行完整的 FAISS 基准测试
"""

import os
import sys
import time
import logging

# 设置环境变量
os.environ['DATASET_LOCAL_DIR'] = '/home/<USER>/VectorDBBench/dataset'

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')

def run_vectordb_benchmark():
    """使用 VectorDBBench 框架运行标准基准测试"""
    print("🚀 启动 VectorDBBench 标准测试框架...")
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
        from vectordb_bench.backend.cases import CaseType
        from vectordb_bench.backend.clients.api import MetricType
        
        # 获取数据集的 metric_type
        case_type = CaseType.Performance1536D50K  
        case = case_type.case_cls()
        dataset_metric_type = case.dataset.data.metric_type
        
        print(f"📊 测试案例: {case.name}")
        print(f"📈 数据集: {case.dataset.data.name} ({case.dataset.data.size:,} 个向量)")
        print(f"🔍 向量维度: {case.dataset.data.dim}")
        print(f"📏 距离度量: {dataset_metric_type}")
        
        # 创建配置
        db_config = FaissLocalConfig(
            db_label="faiss_benchmark_standard",
            index_type="HNSW"
        )
        
        db_case_config = HNSWConfig(
            m=16,
            ef_construction=200,
            ef_search=64,
            metric_type=dataset_metric_type,
        )
        
        print("\n🔧 FAISS 配置:")
        print(f"  🏗️  索引类型: HNSW")
        print(f"  🔗 m 参数: {db_case_config.m}")
        print(f"  🏭 ef_construction: {db_case_config.ef_construction}")
        print(f"  🔍 ef_search: {db_case_config.ef_search}")
        print(f"  📏 metric_type: {db_case_config.metric_type}")
        
        # 运行测试
        print("\n🎯 开始基准测试...")
        print("=" * 60)
        
        parameters = {
            'case_type': 'Performance1536D50K',
            'k': 100,
            'num_concurrency': [1],  # 单线程测试
            'concurrency_duration': 30,  # 30秒测试
            'concurrency_timeout': 3600,
            'db_label': 'faiss_benchmark_standard',
            'load': True,
            'search_serial': True,
            'search_concurrent': True,
            'drop_old': True,
            'dry_run': False,
            'task_label': 'FAISS_Performance_Test',
            'custom_case': {}
        }
        
        start_time = time.time()
        
        run(
            db=DB.FaissLocal,
            db_config=db_config,
            db_case_config=db_case_config,
            **parameters
        )
        
        total_time = time.time() - start_time
        print(f"\n⏱️  总测试时间: {total_time:.2f}s")
        print("✅ VectorDBBench 测试完成!")
        
        return True
        
    except Exception as e:
        print(f"❌ 基准测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_results():
    """检查测试结果"""
    print("\n📋 检查测试结果...")
    
    try:
        import glob
        import json
        from pathlib import Path
        
        # 查找结果文件
        results_dir = Path('/home/<USER>/VectorDBBench/results')
        if results_dir.exists():
            result_files = list(results_dir.glob('**/*.json'))
            if result_files:
                print(f"📁 找到 {len(result_files)} 个结果文件:")
                for i, file in enumerate(result_files[-5:]):  # 显示最近的5个文件
                    print(f"  {i+1}. {file.name}")
                    
                # 读取最新的结果文件
                latest_result = max(result_files, key=lambda x: x.stat().st_mtime)
                print(f"\n📊 最新结果文件: {latest_result.name}")
                
                with open(latest_result, 'r') as f:
                    result_data = json.load(f)
                    
                # 提取关键指标
                if 'results' in result_data:
                    results = result_data['results']
                    print("\n🎯 关键性能指标:")
                    
                    for result in results:
                        if 'metrics' in result:
                            metrics = result['metrics']
                            print(f"  🚀 QPS: {metrics.get('qps', 'N/A')}")
                            print(f"  🎯 召回率: {metrics.get('recall', 'N/A')}")
                            print(f"  ⏱️  平均延迟: {metrics.get('latency_p50', 'N/A')}ms")
                            print(f"  📊 P99 延迟: {metrics.get('latency_p99', 'N/A')}ms")
                            print(f"  🏗️  索引构建时间: {metrics.get('load_duration', 'N/A')}s")
                            
            else:
                print("⚠️  未找到结果文件")
        else:
            print("⚠️  结果目录不存在")
            
        # 检查日志
        log_file = Path('/home/<USER>/VectorDBBench/logs/vectordb_bench.log')
        if log_file.exists():
            print(f"\n📜 最新日志:")
            with open(log_file, 'r') as f:
                lines = f.readlines()
                for line in lines[-10:]:  # 显示最后10行
                    if 'QPS' in line or 'recall' in line or 'latency' in line:
                        print(f"  {line.strip()}")
        
    except Exception as e:
        print(f"❌ 检查结果失败: {e}")

if __name__ == "__main__":
    print("🔬 VectorDBBench FAISS 性能测试")
    print("=" * 50)
    
    # 运行基准测试
    success = run_vectordb_benchmark()
    
    # 等待一下让结果写入完成
    if success:
        time.sleep(2)
        check_results()
    
    sys.exit(0 if success else 1)
