#!/usr/bin/env python3
"""
对比不同并发模型的资源消耗和启动时间
"""

import time
import threading
import multiprocessing as mp
import psutil
import os
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

class ConcurrencyComparison:
    def __init__(self):
        self.base_memory = psutil.Process().memory_info().rss
        
    def measure_resources(self, label):
        """测量当前资源使用"""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        num_threads = process.num_threads()
        
        # 统计子进程
        children = process.children(recursive=True)
        child_count = len(children)
        child_memory = sum(child.memory_info().rss for child in children) / 1024 / 1024
        
        print(f"📊 {label}:")
        print(f"   主进程内存: {memory_mb:.1f} MB")
        print(f"   子进程数量: {child_count}")
        print(f"   子进程内存: {child_memory:.1f} MB")
        print(f"   总内存: {memory_mb + child_memory:.1f} MB")
        print(f"   线程数: {num_threads}")
        
        return {
            'memory': memory_mb + child_memory,
            'processes': child_count + 1,
            'threads': num_threads
        }
    
    def dummy_worker_thread(self, worker_id):
        """线程工作函数"""
        time.sleep(2)
        return f"Thread-{worker_id}"
    
    def dummy_worker_process(self, worker_id):
        """进程工作函数"""
        time.sleep(2)
        return f"Process-{worker_id}"
    
    async def dummy_worker_async(self, session, worker_id):
        """异步工作函数"""
        await asyncio.sleep(0.1)
        return f"Async-{worker_id}"
    
    def test_thread_concurrency(self, concurrency):
        """测试线程并发"""
        print(f"\n🧵 测试线程并发 ({concurrency})")
        print("=" * 40)
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = [
                executor.submit(self.dummy_worker_thread, i) 
                for i in range(concurrency)
            ]
            
            startup_time = time.time() - start_time
            print(f"⏱️  启动时间: {startup_time:.3f} 秒")
            
            resources = self.measure_resources("线程并发")
            
            # 等待完成
            results = [f.result() for f in futures]
        
        total_time = time.time() - start_time
        print(f"⏱️  总时间: {total_time:.3f} 秒")
        
        return {
            'startup_time': startup_time,
            'total_time': total_time,
            'resources': resources
        }
    
    def test_process_concurrency(self, concurrency):
        """测试进程并发"""
        print(f"\n🔄 测试进程并发 ({concurrency})")
        print("=" * 40)
        
        start_time = time.time()
        
        with ProcessPoolExecutor(max_workers=concurrency) as executor:
            futures = [
                executor.submit(self.dummy_worker_process, i) 
                for i in range(concurrency)
            ]
            
            startup_time = time.time() - start_time
            print(f"⏱️  启动时间: {startup_time:.3f} 秒")
            
            resources = self.measure_resources("进程并发")
            
            # 等待完成
            results = [f.result() for f in futures]
        
        total_time = time.time() - start_time
        print(f"⏱️  总时间: {total_time:.3f} 秒")
        
        return {
            'startup_time': startup_time,
            'total_time': total_time,
            'resources': resources
        }
    
    async def test_async_concurrency(self, concurrency):
        """测试异步并发"""
        print(f"\n⚡ 测试异步并发 ({concurrency})")
        print("=" * 40)
        
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            tasks = [
                self.dummy_worker_async(session, i) 
                for i in range(concurrency)
            ]
            
            startup_time = time.time() - start_time
            print(f"⏱️  启动时间: {startup_time:.3f} 秒")
            
            resources = self.measure_resources("异步并发")
            
            # 等待完成
            results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        print(f"⏱️  总时间: {total_time:.3f} 秒")
        
        return {
            'startup_time': startup_time,
            'total_time': total_time,
            'resources': resources
        }
    
    def run_comparison(self, concurrency_levels=[50, 100, 200]):
        """运行对比测试"""
        print("🔬 并发模型对比测试")
        print("=" * 60)
        
        results = {}
        
        for concurrency in concurrency_levels:
            print(f"\n🎯 测试并发级别: {concurrency}")
            print("=" * 50)
            
            # 1. 线程并发
            try:
                thread_result = self.test_thread_concurrency(concurrency)
                results[f'thread_{concurrency}'] = thread_result
            except Exception as e:
                print(f"❌ 线程并发测试失败: {e}")
            
            time.sleep(1)  # 清理间隔
            
            # 2. 进程并发
            try:
                process_result = self.test_process_concurrency(concurrency)
                results[f'process_{concurrency}'] = process_result
            except Exception as e:
                print(f"❌ 进程并发测试失败: {e}")
            
            time.sleep(1)  # 清理间隔
            
            # 3. 异步并发
            try:
                async_result = asyncio.run(self.test_async_concurrency(concurrency))
                results[f'async_{concurrency}'] = async_result
            except Exception as e:
                print(f"❌ 异步并发测试失败: {e}")
            
            time.sleep(2)  # 清理间隔
        
        # 生成对比报告
        self.generate_comparison_report(results)
    
    def generate_comparison_report(self, results):
        """生成对比报告"""
        print("\n" + "=" * 60)
        print("📋 并发模型对比报告")
        print("=" * 60)
        
        print(f"{'并发类型':<15} {'启动时间':<10} {'内存(MB)':<10} {'进程数':<8} {'线程数':<8}")
        print("-" * 60)
        
        for key, result in results.items():
            model_type, concurrency = key.split('_')
            startup_time = result['startup_time']
            memory = result['resources']['memory']
            processes = result['resources']['processes']
            threads = result['resources']['threads']
            
            print(f"{model_type:<15} {startup_time:<10.3f} {memory:<10.1f} {processes:<8} {threads:<8}")
        
        print("\n💡 关键观察:")
        print("1. 进程并发启动时间 >> 线程并发启动时间")
        print("2. 进程并发内存消耗 >> 线程并发内存消耗")
        print("3. 异步并发资源消耗最小")
        print("4. VectorDBBench选择进程并发是为了:")
        print("   - 避免Python GIL限制")
        print("   - 模拟真实分布式客户端")
        print("   - 提供进程级隔离")

def main():
    """主函数"""
    comparison = ConcurrencyComparison()
    
    # 测试不同并发级别
    comparison.run_comparison([50, 100])  # 先测试较小的并发数
    
    print("\n🎯 VectorDBBench 512并发问题解释:")
    print("=" * 50)
    print("1. VectorDBBench使用多进程并发，不是多线程")
    print("2. 512个进程同时启动需要大量系统资源")
    print("3. 进程间同步比线程间同步复杂得多")
    print("4. 这就是为什么512并发会卡在同步阶段")
    print("5. 我们的修复优化了进程启动等待逻辑")

if __name__ == "__main__":
    main()
