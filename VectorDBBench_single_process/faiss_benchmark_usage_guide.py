#!/usr/bin/env python3
"""
演示如何使用增强版的 FAISS 基准测试脚本
支持本地和远程 FAISS 连接
"""

def show_usage_examples():
    """显示使用示例"""
    print("🎯 VectorDBBench FAISS 基准测试 - 使用示例")
    print("=" * 50)
    
    print("\n📋 基本用法:")
    print("python run_faiss_benchmark_enhanced.py [选项]")
    
    print("\n🏠 本地 FAISS 模式:")
    print("```bash")
    print("# 使用本地 FAISS (默认)")
    print("python run_faiss_benchmark_enhanced.py --mode local")
    print("```")
    
    print("\n🌐 远程 FAISS 模式 - 使用 URI:")
    print("```bash")
    print("# 通过 URI 连接远程 FAISS 服务器")
    print("python run_faiss_benchmark_enhanced.py \\")
    print("    --mode remote \\")
    print("    --uri 'http://***********:8002' \\")
    print("    --index-type Flat")
    print("```")
    
    print("\n🌐 远程 FAISS 模式 - 使用主机和端口:")
    print("```bash")
    print("# 通过主机和端口连接远程 FAISS 服务器")
    print("python run_faiss_benchmark_enhanced.py \\")
    print("    --mode remote \\")
    print("    --host *********** \\")
    print("    --port 8002 \\")
    print("    --index-type IVF1024")
    print("```")

def show_comparison():
    """显示原版与增强版的对比"""
    print("\n📊 原版 vs 增强版对比:")
    print("=" * 30)
    
    print("🔵 原版 run_real_vectordb_benchmark.py:")
    print("• ✅ 支持本地 FAISS (FaissLocal)")
    print("• ❌ 不支持远程 FAISS 连接")
    print("• ❌ 无法指定服务器地址")
    print("• ❌ 固定的测试配置")
    
    print("\n🟢 增强版 run_faiss_benchmark_enhanced.py:")
    print("• ✅ 支持本地 FAISS (FaissLocal)")
    print("• ✅ 支持远程 FAISS 连接 (Faiss)")
    print("• ✅ 可通过 --uri 或 --host/--port 指定服务器")
    print("• ✅ 支持多种索引类型 (Flat, IVF1024, IVF2048, IVF4096)")
    print("• ✅ 命令行参数配置")
    print("• ✅ 自动服务器连接检查")

def show_parameters():
    """显示参数说明"""
    print("\n⚙️ 参数说明:")
    print("=" * 15)
    
    print("• --mode          运行模式 (local/remote)")
    print("• --uri           远程服务器 URI (如: http://***********:8002)")
    print("• --host          远程服务器主机 (如: ***********)")
    print("• --port          远程服务器端口 (如: 8002)")
    print("• --index-type    远程索引类型 (Flat/IVF1024/IVF2048/IVF4096)")

def show_server_setup():
    """显示服务器设置"""
    print("\n🌐 远程服务器设置:")
    print("=" * 20)
    
    print("在目标服务器 (如 ***********) 上运行:")
    print("```bash")
    print("cd /path/to/VectorDBBench")
    print("pip install fastapi uvicorn")
    print("python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002")
    print("```")
    
    print("\n检查服务器状态:")
    print("```bash")
    print("curl http://***********:8002/docs")
    print("```")

def show_equivalent_commands():
    """显示等效命令对比"""
    print("\n🔄 等效命令对比:")
    print("=" * 20)
    
    print("🔵 原版脚本 (仅本地):")
    print("```bash")
    print("python run_real_vectordb_benchmark.py")
    print("```")
    
    print("\n🟢 增强版脚本 (本地模式):")
    print("```bash")
    print("python run_faiss_benchmark_enhanced.py --mode local")
    print("```")
    
    print("\n🟢 增强版脚本 (远程模式 - 新功能!):")
    print("```bash")
    print("python run_faiss_benchmark_enhanced.py \\")
    print("    --mode remote \\")
    print("    --uri 'http://***********:8002' \\")
    print("    --index-type Flat")
    print("```")
    
    print("\n💡 这等效于你想要的命令行方式:")
    print("```bash")
    print("# 类似 Milvus 的使用方式")
    print("python -m vectordb_bench.cli.vectordbbench faissremote \\")
    print("    --uri 'http://***********:8002' \\")
    print("    --case-type Performance1536D50K \\")
    print("    --index-type Flat")
    print("```")

if __name__ == "__main__":
    show_usage_examples()
    show_comparison()
    show_parameters()
    show_server_setup()
    show_equivalent_commands()
    
    print(f"\n🎉 总结:")
    print("• ✅ 增强版脚本完全兼容原版功能")
    print("• ✅ 新增远程 FAISS 服务器连接支持")
    print("• ✅ 支持通过 URI 或 IP:端口 连接")
    print("• ✅ 支持多种 FAISS 索引类型")
    print("• ✅ 命令行参数灵活配置")
    print("\n现在你可以通过 URL 或 IP 端口连接到 FAISS 服务进行基准测试了!")
