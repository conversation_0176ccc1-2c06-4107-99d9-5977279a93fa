# FAISS索引配置指南 - 10M数据集优化

## 当前测试结果分析

### 默认配置 (Flat索引)
```
索引类型: Flat (IndexFlatL2)
距离度量: L2 欧几里得距离
测试结果:
- 数据加载: 446.97秒 
- 最大QPS: 709.65 (并发16)
- P99延迟: 2.5ms (串行), 28.74ms (并发16)
- 精确度: 100%
```

## 推荐的索引配置

### 1. 🚀 生产推荐: IVF4096
```python
# 在 enhanced_server.py 中修改默认索引类型
index_type: str = "IVF4096"  # 替换 "Flat"

# 或在创建索引时指定
index = faiss.IndexIVFFlat(quantizer, dim, 4096)
```

**特点:**
- 查询速度提升 2-5倍 (预期QPS: 1400-3500)
- 内存使用降低 30-50%
- 精确度: 95-98%
- 构建时间: +2-3分钟

**适用场景:** 大部分生产环境，平衡性能和精度

### 2. 🎯 高精度: IVF8192
```python
index_type: str = "IVF8192"
```

**特点:**
- 查询速度提升 2-3倍 (预期QPS: 1400-2100)
- 精确度: 96-99%
- 构建时间: +3-5分钟

**适用场景:** 对精度要求较高的应用

### 3. ⚡ 极致性能: HNSW
```python
index_type: str = "HNSW"
# 参数: M=32, ef_construction=400
```

**特点:**
- 查询速度提升 5-10倍 (预期QPS: 3500-7000)
- 延迟降低到 0.5ms 以下
- 精确度: 95-99%
- 构建时间: +5-8分钟

**适用场景:** 对延迟极度敏感的实时应用

## 修改配置的方法

### 方法1: 修改默认配置
编辑 `vectordb_bench/backend/clients/faiss/config.py`:
```python
class FaissConfig(DBConfig):
    host: str = "127.0.0.1"
    port: int = 8002
    index_type: str = "IVF4096"  # 修改这里
```

### 方法2: 修改服务器端
编辑 `enhanced_server.py` 中的 `create_index` 函数:
```python
@app.post("/create_index")
def create_index(req: LegacyCreateIndexRequest):
    # 强制使用推荐的索引类型
    recommended_index_type = "IVF4096"  # 或 "HNSW", "IVF8192"
    
    if recommended_index_type.startswith("IVF"):
        nlist = int(recommended_index_type.replace("IVF", ""))
        quantizer = faiss.IndexFlatL2(req.dim)
        index = faiss.IndexIVFFlat(quantizer, req.dim, nlist)
        # 训练索引...
```

### 方法3: 命令行参数
```bash
# 添加索引类型参数到VectorDBBench命令
python -m vectordb_bench.cli.vectordbbench faissremote \
  --uri 'localhost:8002' \
  --case-type Performance768D10M \
  --index-type IVF4096 \
  --concurrency-duration 300 \
  --num-concurrency 8,16,32,64,128
```

## 测试不同配置

### 快速测试脚本
```bash
#!/bin/bash
# 测试特定索引配置
./test_index_configs.sh
```

### 完整基准测试
```bash
# 测试IVF4096
DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset \
numactl -N 0 python -m vectordb_bench.cli.vectordbbench faissremote \
--uri 'localhost:8002' --case-type Performance768D10M \
--concurrency-duration 300 --num-concurrency 8,16,32,64,128
```

## 性能预测

基于10M数据集的理论分析:

| 配置 | 构建时间 | QPS提升 | 延迟改善 | 精确度 | 内存节省 |
|------|----------|---------|----------|---------|----------|
| Flat (当前) | 7分钟 | 基准 | 基准 | 100% | 基准 |
| IVF4096 | 10分钟 | 2-5x | 50-70% | 96-98% | 30-50% |
| IVF8192 | 12分钟 | 2-3x | 40-60% | 97-99% | 20-40% |
| HNSW | 15分钟 | 5-10x | 70-80% | 95-99% | 0-20% |

## 监控和调优

### 关键指标
- QPS (每秒查询数)
- P99延迟
- 内存使用量
- 精确度 (recall@100)

### 调优参数
```python
# IVF参数调优
nprobe = 32  # 搜索时探测的cluster数量，影响精度和速度

# HNSW参数调优
ef_search = 100  # 搜索时的候选数量
M = 32           # 连接数，影响精度和构建时间
```

## 总结

对于10M的cohere数据集，推荐配置优先级：

1. **生产环境**: IVF4096 (最佳平衡)
2. **高精度需求**: IVF8192
3. **极致性能**: HNSW
4. **完全精确**: Flat (当前配置，适合验证)

选择建议：如果当前Flat的性能已满足需求，可保持现状；如需提升吞吐量，推荐IVF4096。
