# 🎯 VectorDBBench 远程FAISS基准测试完整部署指南

## 📋 概述

本指南将帮助您在**客户机**上进行**远程FAISS服务器**的基准测试。这种架构可以将测试控制和计算资源分离，实现更灵活的性能测试。

## 🏗️ 架构图

```
客户机 (控制端)                     服务器 (计算端)
┌─────────────────────┐            ┌─────────────────────┐
│  VectorDBBench      │   HTTP     │   FAISS Server      │
│  Framework          │ <--------> │   (FastAPI)         │
│                     │            │                     │
│ • 测试控制          │            │ • FAISS库           │
│ • 数据管理          │            │ • 向量索引          │  
│ • 结果收集          │            │ • 搜索计算          │
└─────────────────────┘            └─────────────────────┘
```

## 📦 准备工作

### 1. 文件需求

我已经为您创建了完整的部署包：

**服务器端** (`faiss_server.tar.gz`):
- FAISS服务器程序
- 最小化依赖
- 环境设置脚本

**客户端** (`vectordb_bench_client.tar.gz`):
- 完整的VectorDBBench框架
- 远程FAISS客户端
- 测试脚本和工具

### 2. 系统要求

**服务器端**:
- Python 3.8+
- 4GB+ 内存 (取决于数据规模)
- 网络端口 8002 开放

**客户端**:
- Python 3.8+
- 网络连接到服务器
- 足够存储空间用于测试数据

## 🚀 部署步骤

### 步骤1: 服务器端部署

1. **复制文件到服务器**
```bash
# 将 faiss_server.tar.gz 上传到服务器
scp faiss_server.tar.gz user@server-ip:~/

# 在服务器上解压
ssh user@server-ip
tar -xzf faiss_server.tar.gz
cd faiss_server
```

2. **设置服务器环境**
```bash
chmod +x setup_server.sh
./setup_server.sh
```

3. **启动FAISS服务器**
```bash
source faiss-server-env/bin/activate
uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002
```

4. **验证服务器运行**
```bash
# 在另一个终端检查
curl http://localhost:8002/health
curl http://localhost:8002/docs  # API文档
```

### 步骤2: 客户端部署

1. **解压客户端包**
```bash
tar -xzf vectordb_bench_client.tar.gz
cd vectordb_bench_client
```

2. **设置客户端环境**
```bash
chmod +x setup_client.sh
./setup_client.sh
source vdbench-client-env/bin/activate
```

3. **测试连接**
```bash
python test_connection.py 服务器IP 8002
```

### 步骤3: 配置远程连接

修改测试脚本中的配置：

```python
# 在 run_real_vectordb_benchmark.py 或自定义脚本中
from vectordb_bench.cli.cli import run
from vectordb_bench.backend.clients import DB
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
from vectordb_bench.backend.clients.api import MetricType

# 配置远程FAISS服务器
db_config = FaissConfig(
    host='*************',    # 替换为您的服务器IP
    port=8002,               # FAISS服务器端口
    index_type='Flat',       # 索引类型: Flat, IVF1024, IVF2048等
    db_label='remote_faiss_test'
)

db_case_config = FaissDBCaseConfig(
    metric_type=MetricType.COSINE  # 距离度量
)

# 运行基准测试
run(
    db=DB.Faiss,
    db_config=db_config,
    db_case_config=db_case_config,
    case_type='Performance1536D50K',    # 测试案例
    dataset_name='openai_small_50k',    # 数据集
    k=10,                               # 返回结果数
    num_concurrency=[1, 4, 8],         # 并发度
    concurrency_duration=30,            # 测试时长(秒)
    task_label='RemoteFaissTest',
    dry_run=False,
    load=True,
    search_serial=True,
    search_concurrent=True,
    drop_old=True
)
```

## 🔧 高级配置

### 1. 性能优化

**服务器端**:
```bash
# 使用多个worker进程
uvicorn vectordb_bench.backend.clients.faiss.server:app \
    --host 0.0.0.0 --port 8002 \
    --workers 4 \
    --access-log

# 如果有GPU，安装GPU版本的FAISS
pip install faiss-gpu
```

**客户端**:
- 调整并发度参数
- 使用本地数据集减少网络传输
- 监控网络延迟影响

### 2. 安全配置

```bash
# 服务器防火墙设置
sudo ufw allow 8002
# 或者限制来源IP
sudo ufw allow from 客户机IP to any port 8002

# 使用nginx反向代理（可选）
# 添加认证、HTTPS等
```

### 3. 监控和日志

```bash
# 服务器端监控
htop                    # CPU/内存使用
netstat -tlnp | grep 8002  # 端口状态
tail -f uvicorn.log     # 应用日志

# 客户端监控
# 基准测试会自动记录性能指标
```

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 检查防火墙设置
   - 验证服务器IP和端口
   - 确认服务器程序正在运行

2. **依赖错误**
   - 检查Python版本 (需要3.8+)
   - 重新运行安装脚本
   - 查看错误日志

3. **性能问题**
   - 监控服务器资源使用
   - 检查网络延迟
   - 调整并发参数

4. **数据集问题**
   - 确保数据集路径正确
   - 检查数据集完整性
   - 配置数据源为S3或本地

### 调试命令

```bash
# 检查服务器状态
curl http://服务器IP:8002/health

# 测试基本功能
python test_connection.py 服务器IP 8002

# 查看详细日志
python run_real_vectordb_benchmark.py --verbose

# 检查端口占用
netstat -tlnp | grep 8002
```

## 📊 结果分析

测试完成后，结果文件将保存在客户端的 `vectordb_bench/results/Faiss/` 目录下。

典型的结果包含：
- **QPS**: 每秒查询数
- **延迟**: P50, P99响应时间
- **召回率**: Recall@K准确性
- **吞吐量**: 并发性能数据

示例结果文件：
```json
{
  "run_id": "uuid",
  "task_label": "RemoteFaissTest",
  "results": [
    {
      "metrics": {
        "qps": 1234.56,
        "serial_latency_p99": 45.2,
        "recall": 0.95,
        "ndcg": 0.92
      }
    }
  ]
}
```

## 🎯 最佳实践

1. **测试前准备**
   - 确保服务器资源充足
   - 预热FAISS索引
   - 网络稳定性测试

2. **测试执行**
   - 从小规模数据开始
   - 逐步增加并发度
   - 监控系统资源

3. **结果解释**
   - 考虑网络延迟影响
   - 对比本地基准性能
   - 分析瓶颈所在

## 🎊 总结

通过这种架构，您可以：

✅ **分离资源**: 客户端控制，服务端计算
✅ **灵活部署**: 不同机器承担不同角色  
✅ **扩展性强**: 可以测试多个远程服务器
✅ **标准化**: 使用VectorDBBench标准指标
✅ **易维护**: 模块化部署和管理

现在您已经拥有了完整的远程FAISS基准测试解决方案！
