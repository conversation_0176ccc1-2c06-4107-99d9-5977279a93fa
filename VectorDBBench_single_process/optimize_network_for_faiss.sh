#!/bin/bash
# 网络优化脚本 - 减少高并发测试中的连接错误

echo "🔧 优化网络配置以支持FAISS高并发测试"
echo "=================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  需要root权限来修改系统网络配置"
    echo "请使用: sudo $0"
    exit 1
fi

echo "📊 当前网络配置:"
echo "   TCP连接队列: $(cat /proc/sys/net/core/somaxconn)"
echo "   TCP最大连接数: $(cat /proc/sys/net/core/netdev_max_backlog)"
echo "   文件描述符限制: $(ulimit -n)"

echo ""
echo "🔧 应用网络优化..."

# 1. 增加TCP连接队列
echo "🔧 增加TCP连接队列..."
echo 8192 > /proc/sys/net/core/somaxconn
echo 8192 > /proc/sys/net/core/netdev_max_backlog

# 2. 优化TCP参数
echo "🔧 优化TCP参数..."
echo 1 > /proc/sys/net/ipv4/tcp_tw_reuse
echo 1 > /proc/sys/net/ipv4/tcp_fin_timeout
echo 30 > /proc/sys/net/ipv4/tcp_keepalive_time
echo 5 > /proc/sys/net/ipv4/tcp_keepalive_probes
echo 2 > /proc/sys/net/ipv4/tcp_keepalive_intvl

# 3. 增加端口范围
echo "🔧 增加可用端口范围..."
echo "1024 65535" > /proc/sys/net/ipv4/ip_local_port_range

# 4. 优化内存使用
echo "🔧 优化网络内存使用..."
echo "4096 65536 16777216" > /proc/sys/net/ipv4/tcp_rmem
echo "4096 65536 16777216" > /proc/sys/net/ipv4/tcp_wmem

# 5. 增加文件描述符限制
echo "🔧 增加文件描述符限制..."
ulimit -n 65536

# 临时增加用户的文件描述符限制
if ! grep -q "* soft nofile 65536" /etc/security/limits.conf; then
    echo "* soft nofile 65536" >> /etc/security/limits.conf
    echo "* hard nofile 65536" >> /etc/security/limits.conf
fi

echo ""
echo "✅ 网络优化完成！"
echo "📊 优化后配置:"
echo "   TCP连接队列: $(cat /proc/sys/net/core/somaxconn)"
echo "   TCP最大连接数: $(cat /proc/sys/net/core/netdev_max_backlog)"
echo "   端口范围: $(cat /proc/sys/net/ipv4/ip_local_port_range)"
echo "   文件描述符限制: $(ulimit -n)"

echo ""
echo "💡 建议:"
echo "   1. 重启FAISS服务以应用新配置"
echo "   2. 如果仍有连接错误，可以减少并发数"
echo "   3. 监控系统资源使用情况"

echo ""
echo "🔧 重启FAISS服务命令:"
echo "   pkill -f smart_faiss_server"
echo "   sleep 3"
echo "   ./start_faiss_gunicorn.sh"
