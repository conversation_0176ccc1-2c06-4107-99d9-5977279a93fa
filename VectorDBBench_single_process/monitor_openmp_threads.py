#!/usr/bin/env python3
"""
监控FAISS OpenMP线程活动
"""

import psutil
import time
import subprocess
import threading
import requests
import json

def get_faiss_server_pid():
    """获取smart_faiss_server.py的进程ID"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and any('smart_faiss_server.py' in arg for arg in proc.info['cmdline']):
                return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def monitor_process_threads(pid, duration=35):
    """监控指定进程的线程CPU使用情况"""
    print(f"🔍 监控进程 {pid} 的线程活动...")
    
    try:
        proc = psutil.Process(pid)
        print(f"   进程名: {proc.name()}")
        print(f"   命令行: {' '.join(proc.cmdline())}")
        print()
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            threads = proc.threads()
            print(f"⏰ {time.strftime('%H:%M:%S')} - 活跃线程数: {len(threads)}")
            
            # 显示CPU使用情况
            cpu_percent = proc.cpu_percent()
            memory_percent = proc.memory_percent()
            print(f"   进程CPU使用率: {cpu_percent:.1f}%")
            print(f"   进程内存使用率: {memory_percent:.1f}%")
            
            # 显示线程详情
            for i, thread in enumerate(threads):
                print(f"   线程{i+1}: id={thread.id}, user_time={thread.user_time:.3f}s, system_time={thread.system_time:.3f}s")
            
            print("-" * 60)
            time.sleep(2)
            
    except psutil.NoSuchProcess:
        print(f"❌ 进程 {pid} 不存在")
    except Exception as e:
        print(f"❌ 监控错误: {e}")

def send_concurrent_requests():
    """发送并发搜索请求来触发OpenMP"""
    print("🚀 开始发送并发搜索请求...")
    
    def send_request(thread_id):
        vector = [0.1] * 768
        try:
            for i in range(20):  # 每个线程发送20个请求
                response = requests.post('http://127.0.0.1:8001/search', 
                                       json={'query': vector, 'topk': 100}, 
                                       timeout=10)
                if response.status_code == 200:
                    print(f"🔄 线程{thread_id} 请求{i+1}: 成功")
                else:
                    print(f"❌ 线程{thread_id} 请求{i+1}: 失败 {response.status_code}")
                time.sleep(0.1)  # 小间隔
        except Exception as e:
            print(f"❌ 线程{thread_id} 异常: {e}")
    
    # 启动10个并发线程
    threads = []
    for i in range(10):
        t = threading.Thread(target=send_request, args=(i+1,))
        threads.append(t)
        t.start()
    
    # 等待所有线程完成
    for t in threads:
        t.join()
    
    print("✅ 并发请求完成")

def main():
    print("🎯 FAISS OpenMP线程监控器")
    print("=" * 50)
    
    # 找到FAISS服务器进程
    pid = get_faiss_server_pid()
    if not pid:
        print("❌ 未找到smart_faiss_server.py进程")
        print("请确保服务器正在运行:")
        print("   python smart_faiss_server.py")
        return
    
    print(f"✅ 找到FAISS服务器进程: PID={pid}")
    
    # 启动线程监控
    monitor_thread = threading.Thread(target=monitor_process_threads, args=(pid, 35))
    monitor_thread.start()
    
    # 等待3秒后开始发送请求
    time.sleep(3)
    send_concurrent_requests()
    
    # 等待监控完成
    monitor_thread.join()
    
    print()
    print("📊 监控总结:")
    print("✅ 如果看到多个线程活跃且CPU使用率高，说明OpenMP工作正常")
    print("❓ 如果只看到1个线程活跃，可能是:")
    print("   1. OpenMP环境变量没有正确设置")
    print("   2. FAISS编译时没有OpenMP支持")
    print("   3. 搜索负载不足以触发多线程")

if __name__ == "__main__":
    main()
