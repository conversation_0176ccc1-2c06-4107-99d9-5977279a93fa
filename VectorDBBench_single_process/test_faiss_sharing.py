#!/usr/bin/env python3
"""
测试 FAISS 实时索引构建和文件共享
"""

import sys
import os
import tempfile
import time

# 添加路径
sys.path.append('/home/<USER>/VectorDBBench')

def test_faiss_file_sharing():
    """测试 FAISS 文件共享功能"""
    
    print("🧪 FAISS 文件共享测试")
    print("=" * 40)
    
    # 导入必要的模块
    from vectordb_bench.backend.clients.faiss_local.faiss_local import FaissLocalClient
    from vectordb_bench.backend.clients.faiss_local.config import FaissLocalConfig, HNSWConfig
    from vectordb_bench.backend.clients.api import MetricType
    
    # 创建配置
    db_config = FaissLocalConfig(
        db_label="test_faiss_sharing",
        index_type="HNSW"
    )
    
    db_case_config = HNSWConfig(
        m=16,
        ef_construction=200,
        ef_search=64,
        metric_type=MetricType.COSINE
    )
    
    # 创建客户端实例
    print("1️⃣ 创建 FAISS 客户端")
    client = FaissLocalClient(
        dim=128,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="test_collection"
    )
    
    print(f"   临时目录: {client.temp_dir}")
    print(f"   索引文件: {client.index_file}")
    
    # 插入一些测试数据
    print("2️⃣ 插入测试向量")
    import numpy as np
    
    # 生成测试向量
    test_vectors = np.random.random((100, 128)).tolist()
    test_ids = list(range(100))
    
    with client.init():
        result = client.insert_embeddings(test_vectors, test_ids)
        print(f"   插入结果: {result}")
    
    # 检查文件是否创建
    print("3️⃣ 检查文件创建")
    if os.path.exists(client.index_file):
        size = os.path.getsize(client.index_file)
        print(f"   ✅ 索引文件存在: {size} bytes")
    else:
        print(f"   ❌ 索引文件不存在")
    
    if os.path.exists(client.metadata_file):
        size = os.path.getsize(client.metadata_file)
        print(f"   ✅ 元数据文件存在: {size} bytes")
    else:
        print(f"   ❌ 元数据文件不存在")
    
    # 创建新的客户端实例来模拟新进程
    print("4️⃣ 创建新客户端实例（模拟新进程）")
    client2 = FaissLocalClient(
        dim=128,
        db_config=db_config,
        db_case_config=db_case_config,
        collection_name="test_collection"
    )
    
    print(f"   新客户端临时目录: {client2.temp_dir}")
    print(f"   路径是否一致: {client.temp_dir == client2.temp_dir}")
    
    # 尝试搜索
    print("5️⃣ 在新客户端中搜索")
    query_vector = np.random.random(128).tolist()
    
    with client2.init():
        results = client2.search_embedding(query_vector, k=5)
        print(f"   搜索结果: {results}")
        print(f"   结果数量: {len(results)}")
    
    # 清理
    print("6️⃣ 清理")
    try:
        import shutil
        shutil.rmtree(client.temp_dir)
        print("   ✅ 清理完成")
    except Exception as e:
        print(f"   ⚠️ 清理失败: {e}")

if __name__ == "__main__":
    test_faiss_file_sharing()
