#!/usr/bin/env python3
"""
最终解决方案：创建一个无AWS依赖的本地FAISS客户端wrapper
"""

import os
import sys
import subprocess
import json
import tempfile
import shutil
from pathlib import Path

def setup_aws_mock():
    """设置AWS模拟配置，避免配置错误"""
    
    # 创建临时AWS配置目录
    aws_dir = os.path.expanduser("~/.aws")
    os.makedirs(aws_dir, exist_ok=True)
    
    # 创建基本的AWS配置文件
    config_content = """[default]
region = us-east-1
output = json
"""
    
    credentials_content = """[default]
aws_access_key_id = fake_key
aws_secret_access_key = fake_secret
"""
    
    config_file = os.path.join(aws_dir, "config")
    credentials_file = os.path.join(aws_dir, "credentials")
    
    with open(config_file, 'w') as f:
        f.write(config_content)
    
    with open(credentials_file, 'w') as f:
        f.write(credentials_content)
    
    print(f"✅ AWS模拟配置创建完成: {aws_dir}")

def create_offline_dataset():
    """创建完全离线的数据集副本"""
    
    source_dir = "/home/<USER>/VectorDBBench/dataset"
    offline_dir = "/tmp/offline_vectordb_dataset"
    
    if os.path.exists(offline_dir):
        shutil.rmtree(offline_dir)
    
    if os.path.exists(source_dir):
        shutil.copytree(source_dir, offline_dir)
        print(f"✅ 离线数据集: {source_dir} → {offline_dir}")
    else:
        print(f"❌ 源数据集不存在: {source_dir}")
        return None
    
    return offline_dir

def run_offline_faiss_test():
    """运行完全离线的FAISS测试"""
    
    print("🎯 完全离线FAISS测试")
    print("=" * 40)
    
    # 1. 设置AWS模拟
    setup_aws_mock()
    
    # 2. 创建离线数据集
    offline_dir = create_offline_dataset()
    if not offline_dir:
        return False
    
    # 3. 设置环境变量
    env = os.environ.copy()
    env.update({
        'DATASET_LOCAL_DIR': offline_dir,
        'AWS_ACCESS_KEY_ID': 'fake_key',
        'AWS_SECRET_ACCESS_KEY': 'fake_secret',
        'AWS_DEFAULT_REGION': 'us-east-1'
    })
    
    print(f"📂 数据集路径: {offline_dir}")
    print(f"🔧 AWS配置: 模拟模式")
    
    # 4. 尝试FAISS Local（更可能成功）
    print(f"\n🚀 尝试FAISS Local模式...")
    
    cmd = [
        'python', '-m', 'vectordb_bench.cli.vectordbbench', 'faisslocalhnsw',
        '--case-type', 'Performance1536D50K',
        '--m', '16',
        '--ef-construction', '200',
        '--ef-search', '100',
        '--concurrency-duration', '15',
        '--num-concurrency', '1'
    ]
    
    try:
        print(f"📋 命令: {' '.join(cmd)}")
        print(f"⏰ 超时: 90秒")
        
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True,
            timeout=90
        )
        
        if result.returncode == 0:
            print("✅ FAISS Local测试成功！")
            print("输出预览:")
            print(result.stdout[-1000:])  # 最后1000字符
            return True
        else:
            print(f"❌ 测试失败，返回码: {result.returncode}")
            print("错误输出:")
            print(result.stderr[-1000:])
            print("标准输出:")
            print(result.stdout[-1000:])
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时（90秒）")
        print("💡 这通常意味着仍有网络下载或其他阻塞")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        # 清理
        try:
            if os.path.exists(offline_dir):
                shutil.rmtree(offline_dir)
                print(f"🧹 清理离线数据集: {offline_dir}")
        except:
            pass

def show_working_commands():
    """显示可工作的命令"""
    print(f"\n🎊 如果测试成功，你可以使用以下命令:")
    print("=" * 50)
    
    print("1️⃣ 设置环境:")
    print("export DATASET_LOCAL_DIR=/home/<USER>/VectorDBBench/dataset")
    print("export AWS_ACCESS_KEY_ID=fake_key")
    print("export AWS_SECRET_ACCESS_KEY=fake_secret") 
    print("export AWS_DEFAULT_REGION=us-east-1")
    
    print("\n2️⃣ 运行FAISS Local:")
    print("vectordbbench faisslocalhnsw \\")
    print("    --case-type Performance1536D50K \\")
    print("    --m 16 --ef-construction 200 --ef-search 100")
    
    print("\n3️⃣ 或者使用wrapper脚本:")
    print("python ultimate_faiss_wrapper.py")

if __name__ == "__main__":
    success = run_offline_faiss_test()
    
    if success:
        print("\n🎊 成功！FAISS现在可以离线工作了！")
        show_working_commands()
    else:
        print("\n💡 建议：如果仍有问题，可能需要:")
        print("   1. 检查数据集文件是否完整")
        print("   2. 使用更简单的测试参数")
        print("   3. 考虑使用Docker容器隔离环境")
