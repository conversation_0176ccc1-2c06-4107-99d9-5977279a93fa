#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
gen_template.py  —— 生成 Pydantic Case 的最小合法占位模板

用法：
    python gen_template.py                   # 默认 CapacityDim128
    python gen_template.py Performance768D1M # 换其他 Case
"""

import sys, json, importlib, pandas as pd
from typing import Any, get_origin, get_args
from pydantic import BaseModel

# ------------------------------------------------------------
# 1) 选择目标 Case (默认 CapacityDim128，可通过参数覆盖)
# ------------------------------------------------------------
case_name = sys.argv[1] if len(sys.argv) > 1 else "CapacityDim128"
case_mod = importlib.import_module("vectordb_bench.backend.cases")
TargetCase: type[BaseModel] = getattr(case_mod, case_name)

# ------------------------------------------------------------
# 2) 帮助函数
# ------------------------------------------------------------
def _is_dataframe(field) -> bool:
    """判断 Field 是否是 pandas.DataFrame / Optional[DataFrame] / List[DataFrame]"""
    return (
        field.type_ is pd.DataFrame
        or field.outer_type_ is pd.DataFrame
        or get_origin(field.outer_type_) is pd.DataFrame
    )

def _blank(model: type[BaseModel]) -> dict[str, Any]:
    """
    递归生成最小合法 dict：
    - 对必填字段一定生成占位
    - 如果字段可选，但它内部子模型有必填字段，也生成
    - DataFrame 字段会被跳过
    """
    obj: dict[str, Any] = {}

    for name, field in model.__fields__.items():
        # 跳过 DataFrame
        if _is_dataframe(field):
            continue

        t = field.outer_type_
        origin = get_origin(t)

        # ---------------- 判断是否需要生成 ----------------
        need = field.required  # 必填直接需要
        sub_obj: Any | None = None

        # ① 嵌套 BaseModel
        if isinstance(t, type) and issubclass(t, BaseModel):
            sub_obj = _blank(t)
            need = need or bool(sub_obj)

        # ② List[SubModel]
        elif origin is list:
            sub_type = get_args(t)[0]
            if isinstance(sub_type, type) and issubclass(sub_type, BaseModel):
                sub_template = _blank(sub_type)
                sub_obj = [sub_template] if sub_template else []
                need = need or bool(sub_template)

        # 若既非必填且内部也没必填字段 → 跳过
        if not need:
            continue

        # ---------------- 生成占位值 ----------------
        if sub_obj is not None:
            obj[name] = sub_obj

        elif origin is list:
            obj[name] = []

        elif origin is dict:
            obj[name] = {}

        elif t in (int, float):
            obj[name] = 0

        elif t is bool:
            obj[name] = False

        else:  # string / enum / 其它
            obj[name] = ""

    return obj

# ------------------------------------------------------------
# 3) 生成并打印模板
# ------------------------------------------------------------
template_dict = _blank(TargetCase)
print(json.dumps(template_dict, indent=2))

