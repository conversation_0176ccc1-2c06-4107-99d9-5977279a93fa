#!/usr/bin/env python3
"""
验证120秒测试配置 - 使用较小数据集
"""

import subprocess
import sys
import time
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_vectordb_bench_120s():
    """运行VectorDBBench测试，验证120秒配置"""
    logger.info("🎯 运行VectorDBBench Performance768D1M测试(120秒)")
    logger.info("🔧 配置: 120秒测试时间, 16C64G资源限制")
    
    # 构建命令
    cmd = [
        sys.executable, "-m", "vectordb_bench",
        "--host", "localhost",
        "--port", "8001", 
        "--uri", "http://localhost:8001",
        "--db", "Faiss",
        "--case-type", "Performance768D1M",  # 使用1M数据集，更快完成
        "--concurrency-duration", "120",     # 120秒测试时间
        "--timeout", "600"                   # 10分钟总超时
    ]
    
    logger.info(f"📋 执行命令: {' '.join(cmd)}")
    
    try:
        start_time = time.time()
        
        # 运行命令
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=900,  # 15分钟超时
            cwd="/home/<USER>/VectorDBBench"
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        logger.info(f"⏱️ 测试完成，耗时: {duration:.1f}秒")
        
        # 输出结果
        if result.stdout:
            logger.info("📊 标准输出:")
            print(result.stdout)
        
        if result.stderr:
            logger.info("⚠️ 错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            logger.info("✅ 测试成功完成!")
            
            # 查找性能结果
            if "qps" in result.stdout.lower() or "QPS" in result.stdout:
                logger.info("🚀 发现QPS性能数据!")
            
            return True
        else:
            logger.error(f"❌ 测试失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ 测试超时(15分钟)")
        return False
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        return False

def check_config():
    """检查120秒配置"""
    logger.info("🔍 验证120秒配置...")
    
    try:
        from vectordb_bench import config
        duration = getattr(config, 'CONCURRENCY_DURATION', None)
        
        if duration == 120:
            logger.info("✅ 配置验证通过: CONCURRENCY_DURATION = 120秒")
            return True
        else:
            logger.warning(f"⚠️ 配置可能未生效: CONCURRENCY_DURATION = {duration}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 配置检查失败: {e}")
        return False

def main():
    logger.info("🎯 VectorDBBench 120秒配置验证测试")
    logger.info("=" * 60)
    
    # 1. 检查配置
    config_ok = check_config()
    
    # 2. 运行测试
    if config_ok:
        logger.info("🚀 开始运行VectorDBBench测试...")
        success = run_vectordb_bench_120s()
        
        if success:
            logger.info("🎉 120秒配置验证成功!")
        else:
            logger.error("❌ 测试失败")
    else:
        logger.error("❌ 配置验证失败，跳过测试")

if __name__ == "__main__":
    main()
