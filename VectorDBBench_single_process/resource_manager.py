#!/usr/bin/env python3
"""
资源限制配置脚本
为FAISS服务器设置16C64G资源限制
"""

import psutil
import resource
import os
import signal
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResourceManager:
    def __init__(self, max_cpu_cores=16, max_memory_gb=64):
        self.max_cpu_cores = max_cpu_cores
        self.max_memory_bytes = max_memory_gb * 1024 * 1024 * 1024
        self.pid = os.getpid()
        
    def set_cpu_affinity(self):
        """设置CPU亲和性，限制使用的CPU核心数"""
        try:
            # 获取系统可用CPU核心
            available_cpus = list(range(min(self.max_cpu_cores, psutil.cpu_count())))
            psutil.Process(self.pid).cpu_affinity(available_cpus)
            logger.info(f"CPU affinity set to cores: {available_cpus}")
        except Exception as e:
            logger.warning(f"Failed to set CPU affinity: {e}")
    
    def set_memory_limit(self):
        """设置内存软限制"""
        try:
            # 设置虚拟内存软限制
            resource.setrlimit(resource.RLIMIT_AS, (self.max_memory_bytes, self.max_memory_bytes))
            logger.info(f"Memory limit set to {self.max_memory_bytes / 1024**3:.1f}GB")
        except Exception as e:
            logger.warning(f"Failed to set memory limit: {e}")
    
    def monitor_resources(self):
        """监控当前资源使用情况"""
        process = psutil.Process(self.pid)
        
        # CPU使用率
        cpu_percent = process.cpu_percent()
        cpu_count = len(process.cpu_affinity()) if hasattr(process, 'cpu_affinity') else psutil.cpu_count()
        
        # 内存使用
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        memory_percent = process.memory_percent()
        
        logger.info(f"Resource Usage - CPU: {cpu_percent:.1f}% ({cpu_count} cores), "
                   f"Memory: {memory_mb:.1f}MB ({memory_percent:.1f}%)")
        
        # 检查是否超过限制
        if memory_info.rss > self.max_memory_bytes:
            logger.warning(f"Memory usage ({memory_mb:.1f}MB) exceeds limit ({self.max_memory_bytes/1024**2:.1f}MB)")
        
        return {
            'cpu_percent': cpu_percent,
            'cpu_cores': cpu_count,
            'memory_mb': memory_mb,
            'memory_percent': memory_percent
        }
    
    def apply_limits(self):
        """应用所有资源限制"""
        logger.info(f"Applying resource limits: {self.max_cpu_cores} CPU cores, {self.max_memory_bytes/1024**3:.1f}GB RAM")
        self.set_cpu_affinity()
        self.set_memory_limit()

# 使用示例
if __name__ == "__main__":
    # 创建资源管理器：16核64GB
    resource_manager = ResourceManager(max_cpu_cores=16, max_memory_gb=64)
    
    # 应用资源限制
    resource_manager.apply_limits()
    
    # 监控资源使用
    import time
    for i in range(5):
        stats = resource_manager.monitor_resources()
        time.sleep(1)
