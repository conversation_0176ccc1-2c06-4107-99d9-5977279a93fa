#!/usr/bin/env python3
"""
简单的FAISS服务器模拟器 - 用于测试客户端占位方案
"""

from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn
import argparse
import logging

app = FastAPI(title="FAISS Mock Server", description="用于测试的FAISS服务器模拟器")

# 模拟服务器状态
mock_status = {
    "status": "active",
    "server_type": "faiss_mock_server",
    "version": "1.0.0",
    "capabilities": ["vector_search", "index_management", "dataset_management"],
    "current_dataset": None
}

@app.get("/status")
async def get_status():
    """返回服务器状态"""
    return JSONResponse(mock_status)

@app.post("/prepare_dataset")
async def prepare_dataset(request: dict):
    """模拟数据集准备"""
    case_type = request.get("case_type")
    
    # 模拟数据集信息
    dataset_info = {
        "name": f"Mock_{case_type}",
        "dim": 1536,
        "size": 50000,
        "index_type": "HNSW",
        "status": "ready"
    }
    
    # 更新当前数据集
    mock_status["current_dataset"] = {
        "case_type": case_type,
        "dim": 1536,
        "size": 50000
    }
    
    return JSONResponse({
        "status": "success",
        "message": f"数据集 {case_type} 准备完成",
        "dataset_info": dataset_info
    })

@app.get("/")
async def root():
    """根路径"""
    return {"message": "FAISS Mock Server for Testing"}

def main():
    parser = argparse.ArgumentParser(description="FAISS模拟服务器")
    parser.add_argument("--port", type=int, default=8011, help="服务器端口")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="服务器地址")
    args = parser.parse_args()
    
    print(f"🚀 启动FAISS模拟服务器: http://{args.host}:{args.port}")
    print("📋 API端点:")
    print(f"   GET  /status - 服务器状态")
    print(f"   POST /prepare_dataset - 数据集准备")
    
    # 配置日志
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    
    uvicorn.run(app, host=args.host, port=args.port, log_level="info")

if __name__ == "__main__":
    main()
