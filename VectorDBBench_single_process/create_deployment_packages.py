#!/usr/bin/env python3
"""
📦 文件打包脚本
将必要的文件打包以便部署到客户机和服务器
"""

import os
import shutil
import tarfile
from pathlib import Path

def create_client_package():
    """创建客户端部署包"""
    print("📱 创建客户端部署包")
    print("=" * 30)
    
    # 客户端需要的文件
    client_files = [
        # 核心框架
        "vectordb_bench/__init__.py",
        "vectordb_bench/__main__.py", 
        "vectordb_bench/config.py",
        "vectordb_bench/models.py",
        "vectordb_bench/metric.py",
        "vectordb_bench/interface.py",
        
        # CLI模块
        "vectordb_bench/cli/__init__.py",
        "vectordb_bench/cli/cli.py",
        
        # 后端核心
        "vectordb_bench/backend/__init__.py",
        "vectordb_bench/backend/task_runner.py",
        "vectordb_bench/backend/assembler.py", 
        "vectordb_bench/backend/data_source.py",
        "vectordb_bench/backend/result_collector.py",
        
        # 客户端基础
        "vectordb_bench/backend/clients/__init__.py",
        "vectordb_bench/backend/clients/api.py",
        
        # FAISS远程客户端
        "vectordb_bench/backend/clients/faiss/__init__.py",
        "vectordb_bench/backend/clients/faiss/faiss.py",
        "vectordb_bench/backend/clients/faiss/config.py",
        
        # 用户脚本
        "run_real_vectordb_benchmark.py",
        
        # 配置和设置文件
        "pyproject.toml",
        "setup_client.sh",
        
        # 说明文件
        "README_CLIENT.md"
    ]
    
    # 创建客户端包目录
    client_dir = Path("vectordb_bench_client")
    if client_dir.exists():
        shutil.rmtree(client_dir)
    client_dir.mkdir()
    
    # 复制文件
    copied_files = []
    for file_path in client_files:
        src = Path(file_path)
        if src.exists():
            dst = client_dir / file_path
            dst.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(src, dst)
            copied_files.append(file_path)
            print(f"   ✅ {file_path}")
        else:
            print(f"   ⚠️  跳过不存在的文件: {file_path}")
    
    # 创建客户端说明文档
    create_client_readme(client_dir)
    
    # 创建tar.gz包
    with tarfile.open("vectordb_bench_client.tar.gz", "w:gz") as tar:
        tar.add(client_dir, arcname="vectordb_bench_client")
    
    print(f"\n📦 客户端包创建完成:")
    print(f"   📁 目录: {client_dir}")
    print(f"   📄 压缩包: vectordb_bench_client.tar.gz")
    print(f"   📊 包含文件: {len(copied_files)} 个")
    
    return client_dir

def create_server_package():
    """创建服务器端部署包"""
    print("\n🖥️ 创建服务器端部署包")
    print("=" * 30)
    
    # 服务器端需要的文件
    server_files = [
        # FAISS服务器
        "vectordb_bench/backend/clients/faiss/server.py",
        "vectordb_bench/backend/clients/faiss/config.py",
        "vectordb_bench/backend/clients/faiss/__init__.py",
        
        # 基础接口（服务器可能需要）
        "vectordb_bench/backend/clients/api.py",
        "vectordb_bench/backend/clients/__init__.py",
        "vectordb_bench/backend/__init__.py",
        "vectordb_bench/__init__.py",
        "vectordb_bench/models.py",
        
        # 配置文件
        "pyproject.toml",
        "setup_server.sh",
        
        # 说明文件
        "README_SERVER.md"
    ]
    
    # 创建服务器包目录
    server_dir = Path("faiss_server")
    if server_dir.exists():
        shutil.rmtree(server_dir)
    server_dir.mkdir()
    
    # 复制文件
    copied_files = []
    for file_path in server_files:
        src = Path(file_path)
        if src.exists():
            dst = server_dir / file_path
            dst.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(src, dst)
            copied_files.append(file_path)
            print(f"   ✅ {file_path}")
        else:
            print(f"   ⚠️  跳过不存在的文件: {file_path}")
    
    # 创建服务器说明文档
    create_server_readme(server_dir)
    
    # 创建tar.gz包
    with tarfile.open("faiss_server.tar.gz", "w:gz") as tar:
        tar.add(server_dir, arcname="faiss_server")
    
    print(f"\n📦 服务器包创建完成:")
    print(f"   📁 目录: {server_dir}")
    print(f"   📄 压缩包: faiss_server.tar.gz")
    print(f"   📊 包含文件: {len(copied_files)} 个")
    
    return server_dir

def create_client_readme(client_dir):
    """创建客户端说明文档"""
    readme_content = """# VectorDBBench 客户端部署包

## 📋 概述
这个包包含了在客户机上进行远程FAISS基准测试所需的所有文件。

## 🔧 环境要求
- Python 3.8+
- 网络连接到FAISS服务器

## 📥 安装步骤

### 1. 解压部署包
```bash
tar -xzf vectordb_bench_client.tar.gz
cd vectordb_bench_client
```

### 2. 设置环境
```bash
chmod +x setup_client.sh
./setup_client.sh
```

### 3. 激活环境
```bash
source vdbench-client-env/bin/activate
```

## 🚀 使用方法

### 1. 配置远程服务器
修改测试脚本中的配置，指向远程FAISS服务器：

```python
from vectordb_bench.backend.clients.faiss.config import FaissConfig

db_config = FaissConfig(
    host='你的服务器IP',      # 例如: '*************'
    port=8002,               # FAISS服务器端口
    index_type='Flat',       # 索引类型
    db_label='remote_test'
)
```

### 2. 运行基准测试
```bash
python run_real_vectordb_benchmark.py
```

## 📊 测试结果
测试结果将保存在 `vectordb_bench/results/` 目录下。

## 🔍 故障排除
1. 检查网络连接到服务器
2. 确认服务器端口开放
3. 验证FAISS服务器正在运行
4. 查看客户端错误日志

## 📞 支持
如有问题，请检查服务器端日志和客户端错误信息。
"""
    
    with open(client_dir / "README_CLIENT.md", "w", encoding="utf-8") as f:
        f.write(readme_content)

def create_server_readme(server_dir):
    """创建服务器端说明文档"""
    readme_content = """# FAISS 服务器部署包

## 📋 概述
这个包包含了运行FAISS服务器所需的所有文件。

## 🔧 环境要求
- Python 3.8+
- FAISS库 (CPU或GPU版本)
- 足够的内存和计算资源

## 📥 安装步骤

### 1. 解压部署包
```bash
tar -xzf faiss_server.tar.gz
cd faiss_server
```

### 2. 设置环境
```bash
chmod +x setup_server.sh
./setup_server.sh
```

### 3. 激活环境
```bash
source faiss-server-env/bin/activate
```

## 🚀 启动服务器

### 标准启动
```bash
uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002
```

### 生产环境启动
```bash
uvicorn vectordb_bench.backend.clients.faiss.server:app \\
    --host 0.0.0.0 \\
    --port 8002 \\
    --workers 4 \\
    --access-log
```

## 🔗 API 端点
服务器启动后，可以访问：
- API文档: http://服务器IP:8002/docs
- 健康检查: http://服务器IP:8002/health

## 📊 支持的操作
- POST /create_index - 创建FAISS索引
- POST /insert_bulk - 批量插入向量
- POST /search - 向量搜索
- GET /health - 健康检查

## 🔥 防火墙设置
确保开放8002端口：
```bash
sudo ufw allow 8002
# 或者
sudo iptables -A INPUT -p tcp --dport 8002 -j ACCEPT
```

## 📈 性能监控
建议监控以下指标：
- CPU使用率
- 内存使用率
- 网络I/O
- API响应时间

## 🔍 故障排除
1. 检查端口是否被占用: `netstat -tlnp | grep 8002`
2. 查看服务器日志输出
3. 验证FAISS库安装: `python -c "import faiss; print(faiss.__version__)"`
4. 检查防火墙设置

## 📞 支持
如有问题，请检查服务器日志和系统资源使用情况。
"""
    
    with open(server_dir / "README_SERVER.md", "w", encoding="utf-8") as f:
        f.write(readme_content)

def create_connection_test_script():
    """创建连接测试脚本"""
    test_script = """#!/usr/bin/env python3
'''
🔍 FAISS 远程连接测试脚本
用于验证客户端与服务器的连接
'''

import requests
import json
import sys
import numpy as np

def test_server_connection(host='localhost', port=8002):
    '''测试服务器连接'''
    print(f"🔍 测试连接到 {host}:{port}")
    
    base_url = f"http://{host}:{port}"
    
    try:
        # 健康检查
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接成功")
            return True
        else:
            print(f"❌ 服务器响应错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_faiss_operations(host='localhost', port=8002):
    '''测试基本FAISS操作'''
    print(f"🧪 测试FAISS基本操作")
    
    base_url = f"http://{host}:{port}"
    
    try:
        # 创建索引
        index_data = {
            "dimension": 128,
            "index_type": "Flat",
            "metric_type": "COSINE"
        }
        
        response = requests.post(
            f"{base_url}/create_index", 
            json=index_data,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 索引创建成功")
        else:
            print(f"❌ 索引创建失败: {response.status_code}")
            return False
        
        # 插入向量
        vectors = np.random.rand(100, 128).astype(np.float32)
        ids = list(range(100))
        
        insert_data = {
            "vectors": vectors.tolist(),
            "ids": ids
        }
        
        response = requests.post(
            f"{base_url}/insert_bulk",
            json=insert_data,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ 向量插入成功")
        else:
            print(f"❌ 向量插入失败: {response.status_code}")
            return False
        
        # 搜索测试
        query = np.random.rand(1, 128).astype(np.float32)
        
        search_data = {
            "query_vectors": query.tolist(),
            "k": 10
        }
        
        response = requests.post(
            f"{base_url}/search",
            json=search_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 搜索成功，返回{len(result['results'][0])}个结果")
            return True
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        host = sys.argv[1]
    else:
        host = input("请输入FAISS服务器地址 (默认localhost): ") or "localhost"
    
    if len(sys.argv) > 2:
        port = int(sys.argv[2])
    else:
        port = int(input("请输入FAISS服务器端口 (默认8002): ") or "8002")
    
    print("🎯 FAISS远程连接测试")
    print("=" * 40)
    
    # 测试连接
    if not test_server_connection(host, port):
        print("💥 连接测试失败，请检查服务器状态")
        sys.exit(1)
    
    # 测试操作
    if test_faiss_operations(host, port):
        print("🎉 所有测试通过！可以进行基准测试")
        sys.exit(0)
    else:
        print("💥 FAISS操作测试失败")
        sys.exit(1)
"""
    
    with open("test_connection.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("📄 创建连接测试脚本: test_connection.py")

def main():
    print("📦 VectorDBBench 部署包创建工具")
    print("=" * 50)
    
    # 创建客户端包
    client_dir = create_client_package()
    
    # 创建服务器包
    server_dir = create_server_package()
    
    # 创建连接测试脚本
    create_connection_test_script()
    
    print("\n🎊 部署包创建完成!")
    print("=" * 25)
    print("📱 客户端:")
    print(f"   📁 目录: {client_dir}")
    print("   📄 压缩包: vectordb_bench_client.tar.gz")
    
    print("\n🖥️ 服务器端:")
    print(f"   📁 目录: {server_dir}")
    print("   📄 压缩包: faiss_server.tar.gz")
    
    print("\n🔍 测试工具:")
    print("   📄 连接测试: test_connection.py")
    
    print("\n🚀 使用指南:")
    print("1. 将 faiss_server.tar.gz 复制到服务器并解压")
    print("2. 将 vectordb_bench_client.tar.gz 复制到客户机并解压")
    print("3. 在各自机器上运行 setup_*.sh 脚本")
    print("4. 启动服务器，然后在客户端运行测试")

if __name__ == "__main__":
    main()
