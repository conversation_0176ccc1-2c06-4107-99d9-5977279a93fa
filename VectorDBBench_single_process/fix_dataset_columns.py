#!/usr/bin/env python3
"""
修复数据集列名问题：将'vector'列重命名为'emb'
"""

import pandas as pd
from pathlib import Path
import shutil

def fix_dataset_columns():
    """修复数据集列名问题"""
    
    dataset_dir = Path("/home/<USER>/VectorDBBench/dataset/openai")
    
    print("🔧 修复数据集列名问题...")
    print(f"📂 数据集目录: {dataset_dir}")
    
    # 需要处理的文件
    files_to_fix = [
        "test.parquet",
        "shuffle_train.parquet", 
        "train.parquet"
    ]
    
    for filename in files_to_fix:
        filepath = dataset_dir / filename
        if filepath.exists():
            print(f"🔍 处理文件: {filename}")
            
            # 备份原文件
            backup_path = filepath.with_suffix('.parquet.backup')
            if not backup_path.exists():
                shutil.copy2(filepath, backup_path)
                print(f"📁 备份: {backup_path}")
            
            # 读取数据
            df = pd.read_parquet(filepath)
            print(f"   📊 原始列名: {df.columns.tolist()}")
            print(f"   📏 数据形状: {df.shape}")
            
            # 检查是否需要重命名
            if 'vector' in df.columns and 'emb' not in df.columns:
                # 重命名列
                df = df.rename(columns={'vector': 'emb'})
                print(f"   ✅ 重命名: vector → emb")
                
                # 保存修复后的文件
                df.to_parquet(filepath)
                print(f"   💾 已保存修复后的文件")
            elif 'emb' in df.columns:
                print(f"   ✅ 列名已正确: emb")
            else:
                print(f"   ⚠️ 未找到vector或emb列: {df.columns.tolist()}")
        else:
            print(f"❌ 文件不存在: {filename}")
    
    # 检查其他必需文件
    other_files = ["neighbors.parquet", "scalar_labels.parquet"]
    for filename in other_files:
        filepath = dataset_dir / filename
        if filepath.exists():
            df = pd.read_parquet(filepath)
            print(f"📄 {filename}: {df.columns.tolist()} - 形状: {df.shape}")
        else:
            print(f"❌ 缺失文件: {filename}")

def verify_fixed_dataset():
    """验证修复后的数据集"""
    
    dataset_dir = Path("/home/<USER>/VectorDBBench/dataset/openai")
    
    print("\n✅ 验证修复后的数据集:")
    
    required_files = [
        "test.parquet",
        "shuffle_train.parquet",
        "neighbors.parquet", 
        "scalar_labels.parquet"
    ]
    
    all_good = True
    
    for filename in required_files:
        filepath = dataset_dir / filename
        if filepath.exists():
            try:
                df = pd.read_parquet(filepath)
                size = filepath.stat().st_size
                print(f"   ✅ {filename}: {df.shape} - {size:,} bytes")
                
                # 检查向量文件是否有正确的列名
                if filename in ["test.parquet", "shuffle_train.parquet"]:
                    if 'emb' not in df.columns:
                        print(f"   ❌ {filename} 缺少 'emb' 列: {df.columns.tolist()}")
                        all_good = False
                
            except Exception as e:
                print(f"   ❌ {filename} 读取失败: {e}")
                all_good = False
        else:
            print(f"   ❌ {filename} 不存在")
            all_good = False
    
    return all_good

if __name__ == "__main__":
    print("🎯 修复VectorDBBench数据集列名问题")
    print("=" * 50)
    
    fix_dataset_columns()
    
    if verify_fixed_dataset():
        print("\n🎊 数据集修复完成！现在可以运行FAISS测试了")
        print("\n🚀 测试命令:")
        print("vectordbbench faisslocalhnsw --case-type Performance1536D50K --m 16 --ef-construction 200 --ef-search 100")
    else:
        print("\n❌ 数据集修复有问题，请检查上述错误")
