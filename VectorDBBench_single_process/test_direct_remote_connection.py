#!/usr/bin/env python3
"""
直接测试远程 FAISS 连接功能
演示如何通过 IP 和端口连接到远程 FAISS 服务器
"""

import requests
import time
import numpy as np
from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
from vectordb_bench.backend.clients.faiss.faiss import FaissClient
from vectordb_bench.backend.task_runner import MetricType

def test_direct_connection(host="127.0.0.1", port=8002):
    """直接测试远程 FAISS 连接"""
    print(f"🔗 测试直接连接到远程 FAISS: {host}:{port}")
    print("=" * 60)
    
    # 1. 检查服务器连接
    try:
        response = requests.get(f"http://{host}:{port}/docs", timeout=5)
        if response.status_code == 200:
            print(f"✅ 远程 FAISS 服务器连接成功")
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False
    
    # 2. 创建 FAISS 客户端配置
    print(f"\n🔧 创建远程 FAISS 客户端配置...")
    db_config = FaissConfig(
        host=host,
        port=port,
        index_type="Flat"  # 使用简单的 Flat 索引进行测试
    )
    
    db_case_config = FaissDBCaseConfig(
        metric_type=MetricType.COSINE
    )
    
    print(f"   📋 配置详情:")
    print(f"      主机: {db_config.host}")
    print(f"      端口: {db_config.port}")
    print(f"      索引类型: {db_config.index_type}")
    print(f"      距离度量: {db_case_config.metric_type}")
    
    # 3. 创建客户端实例
    print(f"\n🚀 创建远程 FAISS 客户端...")
    try:
        client = FaissClient(
            dim=128,  # 使用较小的维度进行测试
            db_config=db_config,
            db_case_config=db_case_config,
            collection_name="test_collection",
            drop_old=True
        )
        print(f"✅ 远程 FAISS 客户端创建成功")
    except Exception as e:
        print(f"❌ 客户端创建失败: {e}")
        return False
    
    # 4. 测试基本操作
    print(f"\n📊 测试基本 FAISS 操作...")
    
    try:
        # 生成测试数据
        test_vectors = np.random.random((100, 128)).astype(np.float32)
        test_ids = list(range(100))
        
        print(f"   🔄 插入 {len(test_vectors)} 个测试向量...")
        insert_count, error = client.insert_embeddings(test_vectors.tolist(), test_ids)
        if error:
            print(f"   ❌ 插入失败: {error}")
            return False
        print(f"   ✅ 成功插入 {insert_count} 个向量")
        
        # 测试搜索
        print(f"   🔍 执行向量搜索测试...")
        query_vector = test_vectors[0].tolist()  # 使用第一个向量作为查询
        results = client.search_embedding(query_vector, k=5)
        print(f"   ✅ 搜索成功，返回 {len(results)} 个结果")
        
        # 显示搜索结果
        if results:
            print(f"   📋 搜索结果:")
            for i, doc_id in enumerate(results[:3]):
                print(f"      {i+1}. ID: {doc_id}")
        
    except Exception as e:
        print(f"❌ FAISS 操作测试失败: {e}")
        return False
    
    print(f"\n🎉 远程 FAISS 连接和操作测试完全成功！")
    return True

def test_multiple_index_types(host="127.0.0.1", port=8002):
    """测试多种索引类型"""
    print(f"\n🔬 测试多种索引类型...")
    print("=" * 40)
    
    index_types = ["Flat", "IVF1024", "IVF2048"]
    
    for index_type in index_types:
        print(f"\n🧪 测试索引类型: {index_type}")
        
        try:
            # 创建配置
            db_config = FaissConfig(
                host=host,
                port=port,
                index_type=index_type
            )
            
            db_case_config = FaissDBCaseConfig(
                metric_type=MetricType.COSINE
            )
            
            # 创建客户端
            client = FaissClient(
                dim=64,  # 使用更小的维度以加快测试
                db_config=db_config,
                db_case_config=db_case_config,
                collection_name=f"test_{index_type.lower()}",
                drop_old=True
            )
            
            # 插入少量数据测试
            test_vectors = np.random.random((50, 64)).astype(np.float32)
            test_ids = list(range(50))
            
            insert_count, error = client.insert_embeddings(test_vectors.tolist(), test_ids)
            if error:
                print(f"   ❌ {index_type}: 插入失败 - {error}")
                continue
            
            # 搜索测试
            query_vector = test_vectors[0].tolist()
            results = client.search_embedding(query_vector, k=3)
            
            print(f"   ✅ {index_type}: 插入 {insert_count} 个向量，搜索返回 {len(results)} 个结果")
            
        except Exception as e:
            print(f"   ❌ {index_type}: 测试失败 - {e}")

def show_connection_info():
    """显示连接信息和使用指南"""
    print(f"\n📖 远程 FAISS 连接指南")
    print("=" * 40)
    
    print(f"🌐 服务器启动:")
    print(f"   python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002")
    
    print(f"\n🔗 客户端连接:")
    print(f"   # 方法 1: 使用增强版脚本")
    print(f"   python run_faiss_benchmark_enhanced.py --mode remote --host 127.0.0.1 --port 8002")
    
    print(f"\n   # 方法 2: 直接使用客户端")
    print(f"   from vectordb_bench.backend.clients.faiss.config import FaissConfig")
    print(f"   from vectordb_bench.backend.clients.faiss.faiss_client import FaissClient")
    print(f"   ")
    print(f"   config = FaissConfig(host='127.0.0.1', port=8002, index_type='Flat')")
    print(f"   client = FaissClient(dim=1536, db_config=config, ...)")
    
    print(f"\n   # 方法 3: 使用 CLI 命令")
    print(f"   python -m vectordb_bench.cli.vectordbbench faissremote --uri 'http://127.0.0.1:8002'")

if __name__ == "__main__":
    print("🎯 直接远程 FAISS 连接测试")
    print("=" * 50)
    
    # 基本连接测试
    basic_test = test_direct_connection()
    
    if basic_test:
        # 多索引类型测试
        test_multiple_index_types()
        
        # 显示使用指南
        show_connection_info()
        
        print(f"\n🎉 所有测试完成！远程 FAISS 连接功能正常工作。")
    else:
        print(f"\n❌ 基本连接测试失败，请检查服务器是否正在运行。")
        print(f"启动命令: python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002")
