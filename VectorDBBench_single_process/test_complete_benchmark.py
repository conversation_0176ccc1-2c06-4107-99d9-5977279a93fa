#!/usr/bin/env python3
"""
专门测试：使用完整数据集运行远程 FAISS 基准测试并生成完整的性能结果
"""

import sys
import time
import traceback
from pathlib import Path

def run_complete_remote_faiss_test():
    """运行完整的远程 FAISS 测试以生成 QPS、时延、召回率"""
    print("🎯 完整远程 FAISS 基准测试")
    print("=" * 60)
    
    try:
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
        from vectordb_bench.backend.cases import CaseType
        from vectordb_bench.backend.clients.api import MetricType
        
        print("✅ 所有模块导入成功")
        
        # 创建配置 
        db_config = FaissConfig(
            host='127.0.0.1',
            port=8002,
            index_type='Flat',
            db_label=f'complete_test_{int(time.time())}'
        )
        
        db_case_config = FaissDBCaseConfig(
            metric_type=MetricType.COSINE
        )
        
        print("✅ 配置创建成功")
        print(f"   🔧 数据库配置: {db_config}")
        print(f"   📋 案例配置: {db_case_config}")
        
        # 测试配置
        test_configs = [
            {
                'name': '小规模完整测试 (50K)',
                'case_type': 'Performance1536D50K',  # 使用字符串
                'dataset_name': 'openai_small_50k',  # 使用有完整数据的数据集
                'k': 100,
                'num_concurrency': [1, 2],  # 减少并发以便观察
                'concurrency_duration': 30,  # 稍短的测试时间
                'timeout': 300
            }
        ]
        
        results = []
        
        for test_config in test_configs:
            print(f"\n🚀 运行测试: {test_config['name']}")
            print("=" * 50)
            
            start_time = time.time()
            
            try:
                print("🔍 测试参数:")
                print(f"   案例类型: {test_config['case_type']}")
                print(f"   数据集: {test_config['dataset_name']}")
                print(f"   k值: {test_config['k']}")
                print(f"   并发度: {test_config['num_concurrency']}")
                print(f"   测试时长: {test_config['concurrency_duration']}s")
                print(f"   超时设置: {test_config['timeout']}s")
                
                print(f"\n⏰ 开始时间: {time.ctime()}")
                
                # 运行完整的基准测试
                result = run(
                    db=DB.Faiss,
                    db_config=db_config,
                    db_case_config=db_case_config,
                    case_type='Performance1536D50K',  # 使用字符串而不是枚举
                    dataset_name=test_config['dataset_name'],
                    k=test_config['k'],
                    num_concurrency=test_config['num_concurrency'],
                    concurrency_duration=test_config['concurrency_duration'],
                    concurrency_timeout=test_config['timeout'],
                    task_label='RemoteFaissCompleteTest',
                    dry_run=False,
                    load=True,
                    search_serial=True,
                    search_concurrent=True,
                    drop_old=True
                )
                
                test_time = time.time() - start_time
                
                print(f"\n✅ 测试完成!")
                print(f"   ⏱️  测试耗时: {test_time:.2f}s")
                print(f"   📊 返回结果: {result}")
                
                results.append({
                    'name': test_config['name'],
                    'duration': test_time,
                    'result': result,
                    'status': 'success'
                })
                
                # 检查结果文件
                print(f"\n📄 检查生成的结果文件:")
                results_dir = Path("vectordb_bench/results/Faiss")
                if results_dir.exists():
                    result_files = list(results_dir.glob("*.json"))
                    if result_files:
                        latest_file = max(result_files, key=lambda x: x.stat().st_mtime)
                        print(f"   ✅ 最新结果文件: {latest_file}")
                        print(f"   📏 文件大小: {latest_file.stat().st_size} bytes")
                        
                        # 尝试读取和显示结果
                        try:
                            import json
                            with open(latest_file, 'r') as f:
                                result_data = json.load(f)
                            
                            print(f"   📊 结果数据预览:")
                            if 'results' in result_data:
                                for stage, stage_result in result_data['results'].items():
                                    print(f"      {stage}: {type(stage_result)}")
                                    if isinstance(stage_result, dict):
                                        for key, value in list(stage_result.items())[:3]:
                                            print(f"         {key}: {value}")
                        except Exception as e:
                            print(f"   ⚠️  无法解析结果文件: {e}")
                    else:
                        print(f"   ❌ 结果目录为空")
                else:
                    print(f"   ❌ 结果目录不存在")
                    
            except Exception as e:
                test_time = time.time() - start_time
                print(f"\n❌ 测试失败: {e}")
                print(f"   ⏱️  失败耗时: {test_time:.2f}s")
                traceback.print_exc()
                
                results.append({
                    'name': test_config['name'],
                    'duration': test_time,
                    'error': str(e),
                    'status': 'failed'
                })
        
        # 显示总结
        print(f"\n📋 测试总结")
        print("=" * 30)
        
        for result in results:
            status_icon = "✅" if result['status'] == 'success' else "❌"
            print(f"{status_icon} {result['name']}: {result['duration']:.2f}s")
            if result['status'] == 'failed':
                print(f"   错误: {result['error']}")
        
        return len([r for r in results if r['status'] == 'success']) > 0
        
    except Exception as e:
        print(f"❌ 整体测试失败: {e}")
        traceback.print_exc()
        return False

def check_faiss_server_status():
    """检查 FAISS 服务器状态"""
    print("🌐 检查 FAISS 服务器状态")
    print("=" * 30)
    
    try:
        import requests
        response = requests.get("http://127.0.0.1:8002/docs", timeout=5)
        if response.status_code == 200:
            print("✅ FAISS 服务器运行正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到 FAISS 服务器: {e}")
        print("💡 请确保 FAISS 服务器正在运行:")
        print("   python -m uvicorn vectordb_bench.backend.clients.faiss.server:app --host 0.0.0.0 --port 8002")
        return False

def main():
    print("🎯 远程 FAISS 完整基准测试")
    print("=" * 50)
    print("目标：生成完整的 QPS、时延、召回率等性能指标")
    print("")
    
    # 检查服务器
    if not check_faiss_server_status():
        print("❌ FAISS 服务器未运行，退出测试")
        return False
    
    # 运行完整测试
    success = run_complete_remote_faiss_test()
    
    if success:
        print(f"\n🎉 测试成功！应该已经生成了完整的性能结果。")
        print("📁 请检查 vectordb_bench/results/Faiss/ 目录中的结果文件")
    else:
        print(f"\n❌ 测试失败，需要进一步调试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
