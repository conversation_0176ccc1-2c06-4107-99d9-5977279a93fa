# Smart FAISS Server 使用指南

## 🎯 概述

现在 `smart_faiss_server.py` 已经支持两种启动模式：
1. **Gunicorn 多进程模式** (推荐) - 高性能，充分利用多核
2. **Uvicorn 单进程模式** (对比) - 简单部署，性能有限

## 🚀 快速启动

### 方式1: 直接使用 Python 脚本 (推荐)

#### **多进程高性能模式**
```bash
# 启动 Gunicorn 多进程服务 (8 workers + preload)
python3.11 smart_faiss_server.py --use-gunicorn --workers 8 --preload

# 或者使用便捷脚本
./start_faiss_gunicorn.sh
```

#### **单进程对比模式**
```bash
# 启动 Uvicorn 单进程服务
python3.11 smart_faiss_server.py

# 或者使用便捷脚本
./start_faiss_single.sh
```

### 方式2: 使用启动脚本

```bash
# 多进程模式 (推荐)
./start_faiss_gunicorn.sh

# 单进程模式 (对比测试)
./start_faiss_single.sh
```

## 📊 参数说明

### smart_faiss_server.py 参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--host` | 0.0.0.0 | 服务器地址 |
| `--port` | 8005 | 服务器端口 |
| `--use-gunicorn` | False | 启用 Gunicorn 多进程模式 |
| `--workers` | 8 | Gunicorn Worker 进程数 |
| `--preload` | True | 预加载应用 (避免内存爆炸) |

### 使用示例

```bash
# 基础启动 (单进程)
python3.11 smart_faiss_server.py

# 多进程启动 (8 workers)
python3.11 smart_faiss_server.py --use-gunicorn

# 自定义配置
python3.11 smart_faiss_server.py --use-gunicorn --workers 4 --port 8006

# 指定地址和端口
python3.11 smart_faiss_server.py --host 127.0.0.1 --port 8007
```

## 🔧 配置对比

### 多进程模式 (Gunicorn)
```
✅ 8个 Worker 进程
✅ preload_app = True (内存共享)
✅ 每Worker 2个线程 (总共16线程)
✅ Copy-on-Write 内存优化
✅ 进程级故障隔离
✅ 最大并发: 8000 连接
```

### 单进程模式 (Uvicorn)
```
🔄 1个进程
🔄 16个线程
🔄 200并发限制
⚠️  无故障隔离
⚠️  CPU利用率有限
```

## 📈 性能对比

| 指标 | 单进程模式 | 多进程模式 | 提升倍数 |
|------|------------|------------|----------|
| **QPS** | ~470 | 1500-3000 | 3-6x |
| **CPU利用率** | 25% | 80-90% | 3-4x |
| **并发连接** | 200 | 8000 | 40x |
| **内存使用** | 8-12GB | 12-18GB | 1.5x |
| **延迟P99** | 18-36ms | 10-25ms | 更低 |

## 🔍 验证和测试

### 1. 验证服务状态
```bash
# 检查进程
ps aux | grep smart_faiss_server

# 检查端口
netstat -tlnp | grep 8005

# 测试API
curl http://localhost:8005/status

# 完整验证
./verify_faiss_gunicorn.sh
```

### 2. 性能测试
```bash
# 完整性能测试
./test_faiss_gunicorn_performance.sh

# 本地测试
./test_faiss_gunicorn_performance.sh local
```

### 3. 对比测试
```bash
# 1. 先测试单进程模式
./start_faiss_single.sh
./test_faiss_gunicorn_performance.sh local

# 2. 再测试多进程模式
./start_faiss_gunicorn.sh
./test_faiss_gunicorn_performance.sh local

# 3. 对比结果
```

## 🛠️ 故障排除

### 常见问题

#### 1. Gunicorn 未安装
```bash
# 错误信息
❌ Gunicorn未安装，正在安装...

# 解决方案
pip install gunicorn
```

#### 2. 端口被占用
```bash
# 检查端口占用
netstat -tlnp | grep 8005

# 停止现有服务
pkill -f smart_faiss_server
```

#### 3. 内存不足
```bash
# 检查内存使用
free -h

# 减少Worker数量
python3.11 smart_faiss_server.py --use-gunicorn --workers 4
```

#### 4. 性能不达预期
```bash
# 检查CPU使用
htop

# 检查线程配置
ps -eLf | grep smart_faiss_server

# 调整Worker数量
python3.11 smart_faiss_server.py --use-gunicorn --workers 6
```

## 📊 监控命令

### 实时监控
```bash
# 进程监控
watch -n 1 'ps aux | grep smart_faiss_server'

# 内存监控
watch -n 1 'free -h'

# CPU监控
htop

# 网络监控
netstat -i
```

### 日志查看
```bash
# 查看最新日志
ls -la faiss_*.log

# 实时日志
tail -f faiss_gunicorn_*.log

# 错误日志
grep -i error faiss_*.log
```

## 🎯 最佳实践

### 1. 生产环境推荐配置
```bash
# 推荐配置
python3.11 smart_faiss_server.py \
    --use-gunicorn \
    --workers 8 \
    --preload \
    --host 0.0.0.0 \
    --port 8005
```

### 2. 开发环境配置
```bash
# 开发环境 (快速启动)
python3.11 smart_faiss_server.py --port 8005
```

### 3. 测试环境配置
```bash
# 测试环境 (中等性能)
python3.11 smart_faiss_server.py \
    --use-gunicorn \
    --workers 4 \
    --port 8005
```

## 🔄 服务管理

### 启动服务
```bash
# 方式1: 直接启动
python3.11 smart_faiss_server.py --use-gunicorn

# 方式2: 后台启动
nohup python3.11 smart_faiss_server.py --use-gunicorn > faiss.log 2>&1 &

# 方式3: 使用脚本
./start_faiss_gunicorn.sh
```

### 停止服务
```bash
# 停止所有相关进程
pkill -f smart_faiss_server

# 或者使用停止脚本
./stop_faiss_gunicorn.sh
```

### 重启服务
```bash
# 停止并重启
pkill -f smart_faiss_server
sleep 2
./start_faiss_gunicorn.sh
```

## 🎉 总结

通过修改 `smart_faiss_server.py`，我们现在可以：

1. ✅ **直接使用 Python 脚本启动** - 无需额外的 shell 脚本
2. ✅ **支持 Gunicorn 多进程模式** - 包含 preload_app=True
3. ✅ **智能线程配置** - 根据模式自动调整线程数
4. ✅ **向后兼容** - 默认仍是单进程模式
5. ✅ **简单切换** - 通过 --use-gunicorn 参数切换

现在你可以直接使用：
```bash
python3.11 smart_faiss_server.py --use-gunicorn
```

来启动高性能的多进程 FAISS 服务器！
