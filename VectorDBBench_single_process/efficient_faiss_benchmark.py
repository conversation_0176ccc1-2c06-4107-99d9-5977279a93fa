#!/usr/bin/env python3
"""
高效FAISS Benchmark客户端
基于 asyncio + aiohttp 架构，支持万级QPS
"""

import asyncio
import aiohttp
import numpy as np
import time
import json
import argparse
from typing import AsyncIterator, List, Dict, Any
import logging
from dataclasses import dataclass
from collections import defaultdict
import statistics

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s: %(message)s')
log = logging.getLogger(__name__)

@dataclass
class BenchmarkConfig:
    """Benchmark配置"""
    endpoint: str = "http://localhost:8005/search"
    concurrency: int = 512
    duration: int = 60
    batch_size: int = 1
    topk: int = 100
    vector_dim: int = 768
    warmup_duration: int = 10
    report_interval: int = 5

class MetricsCollector:
    """高精度延迟统计"""
    
    def __init__(self):
        self.latencies = []
        self.request_count = 0
        self.error_count = 0
        self.start_time = None
        self.warmup_end_time = None
        
    def start_warmup(self):
        """开始预热"""
        self.start_time = time.perf_counter()
        
    def end_warmup(self):
        """结束预热"""
        self.warmup_end_time = time.perf_counter()
        self.latencies.clear()  # 清除预热期间的数据
        self.request_count = 0
        self.error_count = 0
        
    def record_latency(self, latency_ms: float):
        """记录延迟"""
        if self.warmup_end_time:  # 只在正式测试期间记录
            self.latencies.append(latency_ms)
        self.request_count += 1
        
    def record_error(self):
        """记录错误"""
        self.error_count += 1
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.latencies:
            return {}
            
        sorted_latencies = sorted(self.latencies)
        n = len(sorted_latencies)
        
        return {
            'total_requests': self.request_count,
            'successful_requests': len(self.latencies),
            'error_count': self.error_count,
            'error_rate': self.error_count / max(1, self.request_count),
            'avg_latency_ms': statistics.mean(sorted_latencies),
            'p50_latency_ms': sorted_latencies[int(n * 0.5)],
            'p95_latency_ms': sorted_latencies[int(n * 0.95)],
            'p99_latency_ms': sorted_latencies[int(n * 0.99)],
            'max_latency_ms': max(sorted_latencies),
            'min_latency_ms': min(sorted_latencies),
        }

class QueryFeeder:
    """查询向量生成器"""
    
    def __init__(self, vector_dim: int, batch_size: int):
        self.vector_dim = vector_dim
        self.batch_size = batch_size
        self.rng = np.random.default_rng()
        
    async def generate_queries(self) -> AsyncIterator[List[List[float]]]:
        """无限生成查询向量"""
        while True:
            if self.batch_size == 1:
                # 单个向量
                vector = self.rng.random(self.vector_dim, dtype=np.float32).tolist()
                yield [vector]
            else:
                # 批量向量
                vectors = self.rng.random((self.batch_size, self.vector_dim), dtype=np.float32).tolist()
                yield vectors
            
            # 让出控制权，避免阻塞
            await asyncio.sleep(0)

class EfficientFAISSBenchmark:
    """高效FAISS Benchmark"""
    
    def __init__(self, config: BenchmarkConfig):
        self.config = config
        self.metrics = MetricsCollector()
        self.feeder = QueryFeeder(config.vector_dim, config.batch_size)
        self.running = False
        
    async def worker(self, session: aiohttp.ClientSession, semaphore: asyncio.Semaphore, worker_id: int):
        """工作协程"""
        query_gen = self.feeder.generate_queries()
        
        while self.running:
            try:
                async with semaphore:
                    # 获取查询向量
                    queries = await query_gen.__anext__()
                    
                    # 构造请求
                    if self.config.batch_size == 1:
                        payload = {
                            "query": queries[0],
                            "topk": self.config.topk
                        }
                    else:
                        payload = {
                            "queries": queries,
                            "topk": self.config.topk
                        }
                    
                    # 发送请求并计时
                    start_time = time.perf_counter()
                    
                    async with session.post(
                        self.config.endpoint,
                        json=payload,
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response:
                        
                        if response.status == 200:
                            await response.read()  # 消费响应体以复用连接
                            latency_ms = (time.perf_counter() - start_time) * 1000
                            self.metrics.record_latency(latency_ms)
                        else:
                            self.metrics.record_error()
                            log.warning(f"Worker {worker_id}: HTTP {response.status}")
                            
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.metrics.record_error()
                log.warning(f"Worker {worker_id}: {e}")
                await asyncio.sleep(0.1)  # 避免错误风暴
    
    async def reporter(self):
        """定期报告统计信息"""
        last_count = 0
        last_time = time.perf_counter()
        
        while self.running:
            await asyncio.sleep(self.config.report_interval)
            
            current_time = time.perf_counter()
            current_count = self.metrics.request_count
            
            # 计算QPS
            time_diff = current_time - last_time
            count_diff = current_count - last_count
            qps = count_diff / time_diff if time_diff > 0 else 0
            
            # 获取延迟统计
            stats = self.metrics.get_stats()
            
            if stats:
                log.info(f"📊 QPS: {qps:.1f} | "
                        f"Avg: {stats['avg_latency_ms']:.2f}ms | "
                        f"P95: {stats['p95_latency_ms']:.2f}ms | "
                        f"P99: {stats['p99_latency_ms']:.2f}ms | "
                        f"Errors: {stats['error_count']}")
            else:
                log.info(f"📊 QPS: {qps:.1f} | Warming up...")
            
            last_count = current_count
            last_time = current_time
    
    async def run_benchmark(self):
        """运行Benchmark"""
        log.info(f"🚀 启动FAISS Benchmark")
        log.info(f"   端点: {self.config.endpoint}")
        log.info(f"   并发数: {self.config.concurrency}")
        log.info(f"   测试时长: {self.config.duration}s")
        log.info(f"   预热时长: {self.config.warmup_duration}s")
        log.info(f"   批量大小: {self.config.batch_size}")
        log.info(f"   向量维度: {self.config.vector_dim}")
        
        # 创建连接器和会话
        connector = aiohttp.TCPConnector(
            limit=self.config.concurrency * 2,  # 连接池大小
            limit_per_host=self.config.concurrency * 2,
            ttl_dns_cache=300,
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            json_serialize=json.dumps
        ) as session:
            
            # 创建信号量控制并发
            semaphore = asyncio.Semaphore(self.config.concurrency)
            
            # 启动工作协程
            self.running = True
            workers = [
                asyncio.create_task(self.worker(session, semaphore, i))
                for i in range(self.config.concurrency)
            ]
            
            # 启动报告协程
            reporter_task = asyncio.create_task(self.reporter())
            
            try:
                # 预热阶段
                log.info(f"🔥 预热阶段 ({self.config.warmup_duration}s)")
                self.metrics.start_warmup()
                await asyncio.sleep(self.config.warmup_duration)
                
                # 正式测试阶段
                log.info(f"📊 正式测试阶段 ({self.config.duration}s)")
                self.metrics.end_warmup()
                await asyncio.sleep(self.config.duration)
                
            finally:
                # 停止所有任务
                self.running = False
                
                # 取消所有工作协程
                for worker in workers:
                    worker.cancel()
                
                reporter_task.cancel()
                
                # 等待所有任务结束
                await asyncio.gather(*workers, reporter_task, return_exceptions=True)
        
        # 输出最终统计
        self.print_final_stats()
    
    def print_final_stats(self):
        """输出最终统计信息"""
        stats = self.metrics.get_stats()
        
        if not stats:
            log.error("❌ 没有收集到有效数据")
            return
        
        # 计算QPS
        test_duration = self.config.duration
        qps = stats['successful_requests'] / test_duration
        
        print("\n" + "=" * 60)
        print("📊 FAISS Benchmark 最终结果")
        print("=" * 60)
        print(f"🎯 测试配置:")
        print(f"   并发数: {self.config.concurrency}")
        print(f"   测试时长: {self.config.duration}s")
        print(f"   批量大小: {self.config.batch_size}")
        print(f"   向量维度: {self.config.vector_dim}")
        
        print(f"\n📈 性能指标:")
        print(f"   QPS: {qps:.2f}")
        print(f"   总请求数: {stats['total_requests']}")
        print(f"   成功请求数: {stats['successful_requests']}")
        print(f"   错误率: {stats['error_rate']*100:.2f}%")
        
        print(f"\n⏱️  延迟统计:")
        print(f"   平均延迟: {stats['avg_latency_ms']:.2f} ms")
        print(f"   P50延迟: {stats['p50_latency_ms']:.2f} ms")
        print(f"   P95延迟: {stats['p95_latency_ms']:.2f} ms")
        print(f"   P99延迟: {stats['p99_latency_ms']:.2f} ms")
        print(f"   最大延迟: {stats['max_latency_ms']:.2f} ms")
        print(f"   最小延迟: {stats['min_latency_ms']:.2f} ms")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="高效FAISS Benchmark客户端")
    parser.add_argument("--endpoint", default="http://localhost:8005/search", help="FAISS服务端点")
    parser.add_argument("--concurrency", type=int, default=512, help="并发数")
    parser.add_argument("--duration", type=int, default=60, help="测试时长(秒)")
    parser.add_argument("--batch-size", type=int, default=1, help="批量大小")
    parser.add_argument("--topk", type=int, default=100, help="Top-K")
    parser.add_argument("--vector-dim", type=int, default=768, help="向量维度")
    parser.add_argument("--warmup", type=int, default=10, help="预热时长(秒)")
    
    args = parser.parse_args()
    
    config = BenchmarkConfig(
        endpoint=args.endpoint,
        concurrency=args.concurrency,
        duration=args.duration,
        batch_size=args.batch_size,
        topk=args.topk,
        vector_dim=args.vector_dim,
        warmup_duration=args.warmup
    )
    
    benchmark = EfficientFAISSBenchmark(config)
    await benchmark.run_benchmark()

if __name__ == "__main__":
    asyncio.run(main())
