#!/usr/bin/env python3
"""
精简的真实数据集测试 - 只测试 50K OpenAI 数据集
"""

import os
import sys
import time

# 添加 VectorDBBench 到路径
sys.path.append('/home/<USER>/VectorDBBench')

def run_simple_real_test():
    """运行简化的真实数据集测试"""
    
    print("🚀 FAISS 真实数据集测试（50K OpenAI）")
    print("=" * 50)
    
    # 检查数据集
    dataset_path = "/nas/yvan.chen/milvus/dataset"
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return
    
    print(f"✅ 数据集路径: {dataset_path}")
    
    # 导入 VectorDBBench
    from vectordb_bench.cli.cli import run
    from vectordb_bench import config
    
    # 设置测试参数
    print("📋 配置测试参数...")
    
    test_args = [
        "--run-name", "FaissRealTest50K",
        "--engines", "faiss_local",
        "--datasets", "openai-small-50k", 
        "--case", "performance",
        "--dataset-path", dataset_path,
        "--timeout", "300"
    ]
    
    print(f"🎯 测试命令: {' '.join(test_args)}")
    
    try:
        # 运行测试
        print("🚀 开始执行测试...")
        result = run(test_args)
        print(f"✅ 测试完成，结果: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 检查结果文件
    print("\n📊 检查测试结果...")
    results_dir = "/home/<USER>/VectorDBBench/vectordb_bench/results/FaissLocal"
    if os.path.exists(results_dir):
        result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
        result_files.sort(key=lambda x: os.path.getmtime(os.path.join(results_dir, x)), reverse=True)
        
        if result_files:
            latest_file = result_files[0]
            file_path = os.path.join(results_dir, latest_file)
            size = os.path.getsize(file_path)
            print(f"   📄 最新结果文件: {latest_file} ({size} bytes)")
            
            # 尝试读取并显示部分内容
            try:
                import json
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    if 'results' in data and data['results']:
                        result = data['results'][0]
                        print(f"   📊 QPS: {result.get('qps', 'N/A')}")
                        print(f"   📊 Recall: {result.get('recall', 'N/A')}")
                        print(f"   📊 Load Duration: {result.get('load_duration', 'N/A')}")
            except Exception as e:
                print(f"   ⚠️ 无法解析结果文件: {e}")
        else:
            print("   ❌ 未找到结果文件")
    else:
        print("   ❌ 结果目录不存在")

if __name__ == "__main__":
    run_simple_real_test()
