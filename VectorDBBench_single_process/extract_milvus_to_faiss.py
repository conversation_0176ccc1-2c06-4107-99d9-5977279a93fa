#!/usr/bin/env python3
"""
从Milvus数据文件中提取向量数据并创建FAISS索引
复用现有的61GB Milvus数据，避免重新加载
"""

import os
import sys
import time
import struct
import numpy as np
import faiss
from pathlib import Path
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/VectorDBBench/milvus_to_faiss.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Milvus数据路径
MILVUS_DATA_PATH = "/data/milvus/volumes/milvus/data"
FAISS_OUTPUT_PATH = "/home/<USER>/VectorDBBench/prebuilt_indexes"

def analyze_milvus_data_structure():
    """分析Milvus数据结构"""
    logger.info("🔍 分析Milvus数据结构...")
    
    insert_log_path = Path(MILVUS_DATA_PATH) / "insert_log"
    index_files_path = Path(MILVUS_DATA_PATH) / "index_files"
    
    logger.info(f"📁 Insert Log 路径: {insert_log_path}")
    logger.info(f"📁 Index Files 路径: {index_files_path}")
    
    # 统计文件数量和大小
    if insert_log_path.exists():
        insert_files = list(insert_log_path.rglob("*"))
        insert_files = [f for f in insert_files if f.is_file()]
        total_size = sum(f.stat().st_size for f in insert_files)
        logger.info(f"📊 Insert Log: {len(insert_files)} 个文件, 总大小: {total_size / (1024**3):.2f} GB")
        
        # 分析文件结构
        if insert_files:
            sample_file = insert_files[0]
            logger.info(f"📄 样本文件: {sample_file}")
            logger.info(f"   大小: {sample_file.stat().st_size / (1024**2):.2f} MB")
            
            # 尝试读取文件头部
            try:
                with open(sample_file, 'rb') as f:
                    header = f.read(64)
                    logger.info(f"   文件头: {header[:32].hex()}")
            except Exception as e:
                logger.warning(f"   无法读取文件头: {e}")
    
    if index_files_path.exists():
        index_files = list(index_files_path.rglob("*"))
        index_files = [f for f in index_files if f.is_file()]
        total_size = sum(f.stat().st_size for f in index_files)
        logger.info(f"📊 Index Files: {len(index_files)} 个文件, 总大小: {total_size / (1024**3):.2f} GB")

def try_read_milvus_insert_log(file_path, max_vectors=1000):
    """尝试读取Milvus insert log文件"""
    logger.info(f"🔍 尝试读取: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            # 读取文件大小
            file_size = f.seek(0, 2)
            f.seek(0)
            
            logger.info(f"   文件大小: {file_size / (1024**2):.2f} MB")
            
            # 尝试不同的读取方式
            
            # 方式1: 尝试读取为float32数组
            try:
                f.seek(0)
                data = f.read()
                
                # 假设是768维向量，每个float32占4字节
                vector_dim = 768
                bytes_per_vector = vector_dim * 4
                
                if len(data) % bytes_per_vector == 0:
                    num_vectors = len(data) // bytes_per_vector
                    logger.info(f"   可能包含 {num_vectors} 个 {vector_dim}维向量")
                    
                    if num_vectors > 0:
                        # 读取前几个向量进行验证
                        vectors_to_read = min(max_vectors, num_vectors)
                        vector_data = np.frombuffer(data[:vectors_to_read * bytes_per_vector], dtype=np.float32)
                        vectors = vector_data.reshape(vectors_to_read, vector_dim)
                        
                        logger.info(f"   成功读取 {vectors_to_read} 个向量")
                        logger.info(f"   向量形状: {vectors.shape}")
                        logger.info(f"   数值范围: [{vectors.min():.6f}, {vectors.max():.6f}]")
                        
                        return vectors, num_vectors
                        
            except Exception as e:
                logger.debug(f"   方式1失败: {e}")
            
            # 方式2: 尝试其他维度
            for dim in [1536, 512, 256, 128]:
                try:
                    f.seek(0)
                    data = f.read()
                    bytes_per_vector = dim * 4
                    
                    if len(data) % bytes_per_vector == 0:
                        num_vectors = len(data) // bytes_per_vector
                        if num_vectors > 0:
                            vectors_to_read = min(max_vectors, num_vectors)
                            vector_data = np.frombuffer(data[:vectors_to_read * bytes_per_vector], dtype=np.float32)
                            vectors = vector_data.reshape(vectors_to_read, dim)
                            
                            logger.info(f"   尝试 {dim}维: 成功读取 {vectors_to_read} 个向量")
                            return vectors, num_vectors
                            
                except Exception as e:
                    logger.debug(f"   {dim}维尝试失败: {e}")
                    
    except Exception as e:
        logger.error(f"❌ 读取文件失败: {e}")
        return None, 0
    
    return None, 0

def extract_vectors_from_milvus():
    """从Milvus数据中提取向量"""
    logger.info("🚀 开始从Milvus数据中提取向量...")
    
    insert_log_path = Path(MILVUS_DATA_PATH) / "insert_log"
    
    if not insert_log_path.exists():
        logger.error(f"❌ Insert log路径不存在: {insert_log_path}")
        return None
    
    # 查找所有数据文件
    data_files = []
    for root, dirs, files in os.walk(insert_log_path):
        for file in files:
            file_path = Path(root) / file
            if file_path.stat().st_size > 1024 * 1024:  # 大于1MB的文件
                data_files.append(file_path)
    
    logger.info(f"📊 找到 {len(data_files)} 个数据文件")
    
    if not data_files:
        logger.error("❌ 未找到数据文件")
        return None
    
    # 按大小排序，优先处理大文件
    data_files.sort(key=lambda x: x.stat().st_size, reverse=True)
    
    all_vectors = []
    total_vectors = 0
    vector_dim = None
    
    # 处理前10个最大的文件
    for i, file_path in enumerate(data_files[:10]):
        logger.info(f"📄 处理文件 {i+1}/10: {file_path.name}")
        logger.info(f"   大小: {file_path.stat().st_size / (1024**2):.2f} MB")
        
        vectors, file_vector_count = try_read_milvus_insert_log(file_path, max_vectors=10000)
        
        if vectors is not None:
            if vector_dim is None:
                vector_dim = vectors.shape[1]
                logger.info(f"✅ 确定向量维度: {vector_dim}")
            elif vectors.shape[1] != vector_dim:
                logger.warning(f"⚠️ 维度不匹配: {vectors.shape[1]} != {vector_dim}，跳过")
                continue
            
            all_vectors.append(vectors)
            total_vectors += len(vectors)
            
            logger.info(f"   ✅ 提取 {len(vectors)} 个向量，累计: {total_vectors}")
            
            # 限制总向量数量，避免内存不足
            if total_vectors >= 1000000:  # 1M向量
                logger.info(f"🎯 达到目标向量数量: {total_vectors}")
                break
        else:
            logger.warning(f"   ❌ 无法从文件中提取向量")
    
    if not all_vectors:
        logger.error("❌ 未能提取任何向量")
        return None
    
    # 合并所有向量
    logger.info("🔗 合并所有向量...")
    final_vectors = np.vstack(all_vectors)
    
    logger.info(f"✅ 成功提取向量:")
    logger.info(f"   总数量: {len(final_vectors):,}")
    logger.info(f"   维度: {final_vectors.shape[1]}")
    logger.info(f"   数据类型: {final_vectors.dtype}")
    logger.info(f"   内存占用: {final_vectors.nbytes / (1024**3):.2f} GB")
    
    return final_vectors

def create_faiss_index_from_vectors(vectors):
    """从向量创建FAISS索引"""
    logger.info("🏗️ 创建FAISS索引...")
    
    dim = vectors.shape[1]
    num_vectors = len(vectors)
    
    logger.info(f"📊 索引参数:")
    logger.info(f"   维度: {dim}")
    logger.info(f"   向量数量: {num_vectors:,}")
    
    # 创建HNSW索引
    index = faiss.IndexHNSWFlat(dim, 30)  # M=30
    index.hnsw.efConstruction = 360
    
    logger.info("📝 添加向量到索引...")
    start_time = time.time()
    
    # 分批添加，避免内存问题
    batch_size = 10000
    for i in range(0, num_vectors, batch_size):
        batch = vectors[i:i+batch_size]
        index.add(batch)
        
        if (i + batch_size) % 100000 == 0:
            elapsed = time.time() - start_time
            progress = (i + batch_size) / num_vectors * 100
            logger.info(f"   进度: {progress:.1f}% ({i + batch_size:,}/{num_vectors:,}), 耗时: {elapsed:.1f}s")
    
    total_time = time.time() - start_time
    logger.info(f"✅ 索引创建完成:")
    logger.info(f"   总耗时: {total_time:.1f}s")
    logger.info(f"   索引大小: {index.ntotal:,} 向量")
    
    return index

def save_faiss_index(index, vectors):
    """保存FAISS索引"""
    logger.info("💾 保存FAISS索引...")
    
    # 确保输出目录存在
    os.makedirs(FAISS_OUTPUT_PATH, exist_ok=True)
    
    dim = vectors.shape[1]
    num_vectors = len(vectors)
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"faiss_hnsw_{dim}d_{num_vectors//1000}k_from_milvus_{timestamp}.index"
    output_path = Path(FAISS_OUTPUT_PATH) / filename
    
    logger.info(f"📁 保存路径: {output_path}")
    
    try:
        faiss.write_index(index, str(output_path))
        file_size = output_path.stat().st_size
        
        logger.info(f"✅ 索引保存成功:")
        logger.info(f"   文件: {filename}")
        logger.info(f"   大小: {file_size / (1024**3):.2f} GB")
        
        return output_path
        
    except Exception as e:
        logger.error(f"❌ 保存索引失败: {e}")
        return None

def main():
    """主函数"""
    logger.info("🎯 Milvus数据转FAISS索引工具")
    logger.info("=" * 60)
    
    # 1. 分析数据结构
    analyze_milvus_data_structure()
    
    # 2. 提取向量
    vectors = extract_vectors_from_milvus()
    if vectors is None:
        logger.error("❌ 向量提取失败")
        return False
    
    # 3. 创建索引
    index = create_faiss_index_from_vectors(vectors)
    if index is None:
        logger.error("❌ 索引创建失败")
        return False
    
    # 4. 保存索引
    output_path = save_faiss_index(index, vectors)
    if output_path is None:
        logger.error("❌ 索引保存失败")
        return False
    
    logger.info("🎉 转换完成!")
    logger.info(f"✅ 成功从Milvus数据创建FAISS索引: {output_path}")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
