#!/bin/bash
# FAISS Gunicorn 服务停止脚本

echo "🛑 停止 FAISS Gunicorn 服务"
echo "=========================="

# 1. 查找 Gunicorn 进程
echo "🔍 查找 Gunicorn 进程..."
GUNICORN_PIDS=$(pgrep -f "gunicorn.*smart_faiss_server")

if [ -z "$GUNICORN_PIDS" ]; then
    echo "ℹ️  未找到运行中的 Gunicorn 进程"
    
    # 检查是否有其他 FAISS 相关进程
    OTHER_FAISS=$(pgrep -f "faiss|uvicorn.*faiss")
    if [ -n "$OTHER_FAISS" ]; then
        echo "⚠️  发现其他 FAISS 相关进程:"
        ps aux | grep -E "(faiss|uvicorn.*faiss)" | grep -v grep
        echo ""
        read -p "是否要停止这些进程? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "🛑 停止其他 FAISS 进程..."
            kill $OTHER_FAISS
            sleep 2
            # 强制杀死仍在运行的进程
            kill -9 $OTHER_FAISS 2>/dev/null || true
            echo "✅ 其他 FAISS 进程已停止"
        fi
    fi
    exit 0
fi

echo "📊 找到以下 Gunicorn 进程:"
ps aux | grep gunicorn | grep -v grep

# 2. 优雅停止
echo ""
echo "🔄 尝试优雅停止 Gunicorn 进程..."
for pid in $GUNICORN_PIDS; do
    echo "   停止进程 $pid..."
    kill -TERM $pid 2>/dev/null || true
done

# 3. 等待进程退出
echo "⏳ 等待进程退出 (最多15秒)..."
for i in {1..15}; do
    REMAINING_PIDS=$(pgrep -f "gunicorn.*smart_faiss_server")
    if [ -z "$REMAINING_PIDS" ]; then
        echo "✅ 所有 Gunicorn 进程已优雅退出"
        break
    fi
    echo "   等待中... ($i/15)"
    sleep 1
done

# 4. 强制停止仍在运行的进程
REMAINING_PIDS=$(pgrep -f "gunicorn.*smart_faiss_server")
if [ -n "$REMAINING_PIDS" ]; then
    echo "⚠️  仍有进程在运行，强制停止..."
    for pid in $REMAINING_PIDS; do
        echo "   强制停止进程 $pid..."
        kill -9 $pid 2>/dev/null || true
    done
    sleep 2
fi

# 5. 验证停止结果
echo ""
echo "🔍 验证停止结果..."
FINAL_CHECK=$(pgrep -f "gunicorn.*smart_faiss_server")
if [ -z "$FINAL_CHECK" ]; then
    echo "✅ 所有 Gunicorn 进程已成功停止"
else
    echo "❌ 仍有进程在运行:"
    ps aux | grep gunicorn | grep -v grep
fi

# 6. 检查端口状态
echo ""
echo "🌐 检查端口状态..."
PORT_CHECK=$(netstat -tlnp 2>/dev/null | grep ":8005" || ss -tlnp | grep ":8005")
if [ -z "$PORT_CHECK" ]; then
    echo "✅ 端口 8005 已释放"
else
    echo "⚠️  端口 8005 仍被占用:"
    echo "$PORT_CHECK"
fi

# 7. 清理临时文件 (可选)
echo ""
echo "🧹 清理选项..."
read -p "是否要清理日志文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -f "faiss_server_gunicorn.log" ]; then
        mv faiss_server_gunicorn.log "faiss_server_gunicorn.log.$(date +%Y%m%d_%H%M%S)"
        echo "✅ 日志文件已备份"
    fi
fi

# 8. 从 Cgroup 中移除 (如果适用)
echo ""
echo "📊 清理 Cgroup 配置..."
if [ -d "/sys/fs/cgroup/faiss-service" ]; then
    # 清空 cgroup.procs 文件
    sudo tee /sys/fs/cgroup/faiss-service/cgroup.procs < /dev/null > /dev/null 2>&1 || true
    echo "✅ Cgroup 进程列表已清理"
else
    echo "ℹ️  Cgroup 配置不存在，跳过清理"
fi

# 9. 显示系统资源状态
echo ""
echo "📈 当前系统资源状态:"
echo "   内存使用:"
free -h | grep -E "(Mem|Swap)" | sed 's/^/   /'
echo "   CPU负载:"
uptime | sed 's/^/   /'

# 10. 总结
echo ""
echo "📋 停止操作完成"
echo "==============="
echo "✅ FAISS Gunicorn 服务已停止"
echo ""
echo "🔧 相关命令:"
echo "   重新启动: ./start_faiss_with_cgroup.sh"
echo "   查看日志: ls -la faiss_server_gunicorn.log*"
echo "   检查进程: ps aux | grep gunicorn"
echo "   检查端口: netstat -tlnp | grep 8005"
