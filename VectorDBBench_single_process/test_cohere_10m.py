#!/usr/bin/env python3
"""
测试cohere_large_10m数据集支持
"""

import requests
import json
import time

# 服务器配置
SERVER_URL = "http://localhost:8002"

def test_new_dataset():
    """测试新增的Performance768D10M数据集"""
    
    print("=== 测试cohere_large_10m数据集支持 ===\n")
    
    # 1. 检查可用数据集
    print("1. 检查可用数据集...")
    response = requests.get(f"{SERVER_URL}/datasets")
    if response.status_code == 200:
        data = response.json()
        print(f"   数据集基础路径: {data['dataset_base_path']}")
        print("   可用数据集:")
        for dataset in data['available_datasets']:
            print(f"   - {dataset['path']} ({dataset['type']}, {dataset['files']} 文件)")
        
        # 检查是否包含cohere_large_10m
        cohere_10m_found = any(d['path'] == 'cohere/cohere_large_10m' for d in data['available_datasets'])
        print(f"   ✓ cohere_large_10m 数据集发现: {'是' if cohere_10m_found else '否'}")
    else:
        print(f"   ✗ 获取数据集列表失败: {response.status_code}")
        return
    
    print()
    
    # 2. 测试加载Performance768D10M数据集
    print("2. 加载Performance768D10M数据集...")
    load_request = {
        "case_type": "Performance768D10M",
        "force_reload": True
    }
    
    response = requests.post(f"{SERVER_URL}/load_dataset", json=load_request)
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ 数据集加载成功:")
        dataset_info = data['dataset']
        print(f"     - 名称: {dataset_info['name']}")
        print(f"     - 类型: {dataset_info['case_type']}")
        print(f"     - 维度: {dataset_info['vector_dim']}")
        print(f"     - 向量数量: {dataset_info['total_vectors']}")
        print(f"     - 路径: {dataset_info['dataset_path']}")
        print(f"     - 度量类型: {dataset_info['metric_type']}")
    else:
        print(f"   ✗ 数据集加载失败: {response.status_code}")
        print(f"     错误信息: {response.text}")
        return
    
    print()
    
    # 3. 测试创建索引
    print("3. 基于数据集创建FAISS索引...")
    index_request = {
        "case_type": "Performance768D10M",
        "index_type": "Flat",
        "index_params": {}
    }
    
    response = requests.post(f"{SERVER_URL}/create_index_with_dataset", json=index_request)
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ 索引创建成功:")
        print(f"     - 索引名称: {data['index_name']}")
        print(f"     - 索引类型: {data['index_type']}")
        print(f"     - 向量总数: {data['total_vectors']}")
    else:
        print(f"   ✗ 索引创建失败: {response.status_code}")
        print(f"     错误信息: {response.text}")
        return
    
    print()
    
    # 4. 检查服务器状态
    print("4. 检查服务器状态...")
    response = requests.get(f"{SERVER_URL}/server_status")
    if response.status_code == 200:
        data = response.json()
        print(f"   已加载数据集数量: {data['datasets_loaded']}")
        print(f"   已创建索引数量: {data['indexes_created']}")
        print(f"   已加载的数据集: {data['memory_usage']['datasets']}")
        print(f"   已创建的索引: {data['memory_usage']['indexes']}")
    else:
        print(f"   ✗ 获取服务器状态失败: {response.status_code}")
    
    print()
    print("=== 测试完成 ===")

if __name__ == "__main__":
    try:
        test_new_dataset()
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，请确保服务器正在运行在 http://localhost:8002")
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
