#!/usr/bin/env python3
"""
测试FAISS服务器加载状态并执行简单搜索
"""

import time
import requests
import json
import random

def wait_for_load_completion():
    """等待数据集加载完成"""
    print("⏳ 等待数据集加载完成...")
    
    while True:
        try:
            response = requests.get("http://localhost:8001/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                if status.get("status") == "loaded":
                    print(f"✅ 数据集加载完成: {status.get('total_vectors', 0):,} 个向量")
                    return True
                else:
                    print(f"🔄 加载中... 状态: {status.get('status', 'unknown')}")
            
            time.sleep(10)  # 等待10秒再检查
            
        except Exception as e:
            print(f"❌ 检查状态失败: {e}")
            time.sleep(5)
            return False

def test_search():
    """测试搜索功能"""
    print("🔍 测试搜索功能...")
    
    # 生成768维随机查询向量
    query_vector = [random.gauss(0, 1) for _ in range(768)]
    
    payload = {
        "query": query_vector,
        "topk": 10
    }
    
    try:
        start_time = time.time()
        response = requests.post("http://localhost:8001/search", json=payload, timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            latency = (end_time - start_time) * 1000  # 转换为毫秒
            print(f"✅ 搜索成功!")
            print(f"   延迟: {latency:.2f}ms")
            print(f"   返回结果数: {len(result.get('ids', [[]])[0])}")
            return True
        else:
            print(f"❌ 搜索失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 搜索异常: {e}")
        return False

def main():
    print("🎯 FAISS服务器状态检查和搜索测试")
    print("=" * 50)
    
    # 等待加载完成
    if not wait_for_load_completion():
        print("❌ 数据集加载失败或超时")
        return
    
    # 测试搜索
    if test_search():
        print("🎉 服务器测试通过!")
        print("📋 120秒配置已生效，可以开始性能测试")
    else:
        print("❌ 搜索测试失败")

if __name__ == "__main__":
    main()
