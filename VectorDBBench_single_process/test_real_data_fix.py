#!/usr/bin/env python3
"""
简化的真实数据集测试，专注验证文件共享修复
"""

import os
import sys
import time

# 添加 VectorDBBench 到路径
sys.path.append('/home/<USER>/VectorDBBench')

def test_faiss_real_data():
    """测试 FAISS 使用真实数据集的基本功能"""
    
    print("🧪 FAISS 真实数据集文件共享测试")
    print("=" * 50)
    
    # 检查数据集
    dataset_path = "/nas/yvan.chen/milvus/dataset"
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集路径不存在: {dataset_path}")
        return
    
    print(f"✅ 数据集路径: {dataset_path}")
    
    # 执行简单的VectorDBBench测试
    from vectordb_bench.cli.vectordbbench import main as cli_main
    import subprocess
    
    # 构建测试命令
    test_config = f"""
    --run-name "FaissRealDataTest"
    --engines faiss_local
    --datasets openai-small-50k
    --case performance
    --dataset-path {dataset_path}
    """.strip().replace('\n', ' ')
    
    cmd = f"python -m vectordb_bench.cli.vectordbbench {test_config}"
    print(f"🚀 执行命令: {cmd}")
    
    # 执行测试
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
    
    print("\n📊 测试结果:")
    print("STDOUT:", result.stdout[-1000:] if len(result.stdout) > 1000 else result.stdout)
    if result.stderr:
        print("STDERR:", result.stderr[-500:] if len(result.stderr) > 500 else result.stderr)
    
    print(f"\n返回码: {result.returncode}")
    
    # 检查临时文件
    print("\n📁 检查临时文件:")
    import tempfile
    temp_dirs = [d for d in os.listdir(tempfile.gettempdir()) if d.startswith('faiss_vectordb_')]
    for temp_dir in temp_dirs:
        full_path = os.path.join(tempfile.gettempdir(), temp_dir)
        print(f"  📂 {full_path}")
        for file in os.listdir(full_path):
            file_path = os.path.join(full_path, file)
            size = os.path.getsize(file_path)
            print(f"    📄 {file}: {size} bytes")

if __name__ == "__main__":
    test_faiss_real_data()
