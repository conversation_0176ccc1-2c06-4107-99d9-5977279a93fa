#!/usr/bin/env python3
"""
简化的远程FAISS基准测试脚本

修复架构问题：
1. 客户端不再设置DATASET_LOCAL_DIR
2. 所有数据集管理由服务器端处理
3. 客户端只发送测试参数和查询请求
4. 实现正确的client-server架构分离

使用方法:
python simplified_remote_faiss_benchmark.py --host 127.0.0.1 --port 8002 --case-type Performance1536D50K
"""

import argparse
import time
import requests
import json
from datetime import datetime
from pathlib import Path

def check_server_status(host: str, port: int) -> bool:
    """检查服务器状态"""
    try:
        response = requests.get(f"http://{host}:{port}/health", timeout=10)
        if response.status_code == 200:
            health_info = response.json()
            print(f"✅ 服务器状态: {health_info['status']}")
            print(f"📊 已加载数据集: {len(health_info.get('loaded_datasets', []))}")
            print(f"🔧 可用索引: {len(health_info.get('available_indexes', []))}")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

def list_available_datasets(host: str, port: int):
    """列出服务器可用的数据集"""
    try:
        response = requests.get(f"http://{host}:{port}/datasets", timeout=10)
        if response.status_code == 200:
            datasets_info = response.json()
            print(f"\n📁 服务器数据集路径: {datasets_info['dataset_base_path']}")
            print(f"📊 可用数据集:")
            for dataset in datasets_info['available_datasets']:
                print(f"   • {dataset['path']} ({dataset['files']} 个文件)")
            
            if datasets_info['loaded_datasets']:
                print(f"\n🔄 已加载的数据集:")
                for name, info in datasets_info['loaded_datasets'].items():
                    print(f"   • {name}: {info['total_vectors']} 个 {info['vector_dim']} 维向量")
            return True
        else:
            print(f"❌ 获取数据集信息失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取数据集信息失败: {e}")
        return False

def run_server_side_benchmark(host: str, port: int, case_type: str, index_type: str = "Flat", k: int = 100):
    """在服务器端运行完整基准测试"""
    print(f"\n🚀 服务器端基准测试")
    print("=" * 50)
    print(f"📋 测试配置:")
    print(f"   服务器: {host}:{port}")
    print(f"   数据集: {case_type}")
    print(f"   索引类型: {index_type}")
    print(f"   返回结果数: {k}")
    
    try:
        start_time = time.time()
        
        # 发送基准测试请求到服务器
        print(f"\n📤 向服务器发送基准测试请求...")
        response = requests.post(
            f"http://{host}:{port}/benchmark_test",
            json={
                "case_type": case_type,
                "index_type": index_type,
                "k": k,
                "test_queries": 1000,  # 测试1000个查询
                "metric_type": "COSINE"
            },
            timeout=600  # 10分钟超时
        )
        
        if response.status_code == 200:
            result = response.json()
            end_time = time.time()
            
            print(f"✅ 服务器端基准测试完成!")
            print(f"\n📊 性能指标:")
            metrics = result['performance_metrics']
            print(f"   🚀 QPS: {metrics['qps']}")
            print(f"   ⏱️  平均延迟: {metrics['avg_latency_ms']:.2f} ms")
            print(f"   📈 P50延迟: {metrics['p50_latency_ms']:.2f} ms") 
            print(f"   📊 P99延迟: {metrics['p99_latency_ms']:.2f} ms")
            print(f"   ⏰ 总耗时: {metrics['total_time_s']:.2f} s")
            print(f"   🔍 查询总数: {metrics['total_queries']}")
            
            print(f"\n📋 数据集信息:")
            dataset = result['dataset']
            print(f"   📁 数据集: {dataset['name']}")
            print(f"   📐 向量维度: {dataset['vector_dim']}")
            print(f"   📊 总向量数: {dataset['total_vectors']}")
            print(f"   📏 距离度量: {dataset['metric_type']}")
            
            # 保存结果到本地文件
            results_dir = Path("simplified_results")
            results_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = results_dir / f"simplified_faiss_benchmark_{timestamp}.json"
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 结果已保存到: {result_file}")
            return True
            
        else:
            print(f"❌ 服务器端基准测试失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 基准测试失败: {e}")
        return False

def run_traditional_vectordbbench(host: str, port: int, case_type: str, index_type: str = "Flat"):
    """使用传统VectorDBBench方式（但不设置本地数据集路径）"""
    print(f"\n🔧 传统VectorDBBench方式 (客户端简化)")
    print("=" * 50)
    
    try:
        # 注意：不再设置 DATASET_LOCAL_DIR 环境变量
        print(f"✅ 客户端简化：无需设置本地数据集路径")
        
        from vectordb_bench.cli.cli import run
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
        from vectordb_bench.backend.clients.api import MetricType
        
        print(f"✅ VectorDBBench模块导入成功")
        
        # 创建远程FAISS配置
        db_config = FaissConfig(
            host=host,
            port=port,
            index_type=index_type,
            db_label=f'simplified_test_{int(time.time())}'
        )
        
        db_case_config = FaissDBCaseConfig(
            metric_type=MetricType.COSINE
        )
        
        print(f"📋 配置信息:")
        print(f"   数据库: DB.Faiss (远程)")
        print(f"   服务器: {host}:{port}")
        print(f"   索引类型: {index_type}")
        print(f"   案例类型: {case_type}")
        
        print(f"\n🚀 开始VectorDBBench测试...")
        start_time = time.time()
        
        # 运行基准测试
        result = run(
            db=DB.Faiss,
            db_config=db_config,
            db_case_config=db_case_config,
            case_type=case_type,
            k=100,
            num_concurrency=[1],
            concurrency_duration=30,
            task_label=f"Simplified_Remote_FAISS_{index_type}",
            load=True,
            search_serial=True,
            search_concurrent=True,
            drop_old=True,
            dry_run=False
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ VectorDBBench测试完成!")
        print(f"⏰ 总耗时: {duration:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ VectorDBBench测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    parser = argparse.ArgumentParser(description='简化的远程FAISS基准测试')
    parser.add_argument('--host', default='127.0.0.1', help='FAISS服务器主机')
    parser.add_argument('--port', type=int, default=8002, help='FAISS服务器端口')
    parser.add_argument('--case-type', default='Performance1536D50K', 
                       choices=['Performance1536D50K', 'Performance1536D500K', 'Performance768D1M'],
                       help='测试案例类型')
    parser.add_argument('--index-type', default='Flat', 
                       choices=['Flat', 'IVF1024', 'IVF2048', 'IVF4096', 'HNSW'],
                       help='索引类型')
    parser.add_argument('--mode', default='server', 
                       choices=['server', 'vectordbbench'],
                       help='测试模式：server=服务器端测试，vectordbbench=传统框架测试')
    
    args = parser.parse_args()
    
    print("🎯 简化的远程FAISS基准测试")
    print("=" * 50)
    print("🔧 架构修复:")
    print("   ✅ 客户端不再依赖本地数据集路径")
    print("   ✅ 服务器端完整数据集管理")
    print("   ✅ 正确的client-server职责分离")
    print()
    
    # 1. 检查服务器状态
    print("1️⃣ 检查服务器状态")
    if not check_server_status(args.host, args.port):
        print("\n💡 启动增强版FAISS服务器:")
        print("   python -m uvicorn vectordb_bench.backend.clients.faiss.enhanced_server:app --host 0.0.0.0 --port 8002")
        return False
    
    # 2. 列出可用数据集
    print("\n2️⃣ 检查可用数据集")
    if not list_available_datasets(args.host, args.port):
        print("⚠️ 无法获取数据集信息，但继续测试")
    
    # 3. 运行基准测试
    print(f"\n3️⃣ 运行基准测试 (模式: {args.mode})")
    
    if args.mode == 'server':
        # 服务器端完整测试
        success = run_server_side_benchmark(
            host=args.host,
            port=args.port,
            case_type=args.case_type,
            index_type=args.index_type
        )
    else:
        # 传统VectorDBBench方式（但简化客户端）
        success = run_traditional_vectordbbench(
            host=args.host,
            port=args.port,
            case_type=args.case_type,
            index_type=args.index_type
        )
    
    if success:
        print(f"\n🎉 简化的远程FAISS基准测试成功！")
        print(f"✅ 架构修复验证完成")
    else:
        print(f"\n💥 基准测试失败")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
