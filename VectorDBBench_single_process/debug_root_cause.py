#!/usr/bin/env python3
"""
深入调试：找到并解决详细结果生成问题的根因
"""

import logging
import os
import sys
import time
import traceback
from pathlib import Path

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s | %(levelname)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('debug_detailed_results.log')
    ]
)

def enable_detailed_logging():
    """启用详细日志记录"""
    print("🔧 启用详细日志记录")
    
    # 设置VectorDBBench相关模块的日志级别
    modules_to_debug = [
        'vectordb_bench',
        'vectordb_bench.interface',
        'vectordb_bench.backend.task_runner',
        'vectordb_bench.backend.clients.faiss',
        'vectordb_bench.cli.cli'
    ]
    
    for module in modules_to_debug:
        logger = logging.getLogger(module)
        logger.setLevel(logging.DEBUG)
        
    print("✅ 详细日志已启用")

def test_step_by_step_execution():
    """逐步测试执行过程"""
    print("🔍 逐步测试执行过程")
    print("=" * 50)
    
    try:
        # 导入必要模块
        from vectordb_bench.interface import benchmark_runner
        from vectordb_bench.backend.clients import DB
        from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
        from vectordb_bench.backend.cases import CaseType
        from vectordb_bench.backend.clients.api import MetricType
        from vectordb_bench.models import TaskConfig, CaseConfig, ConcurrencySearchConfig
        
        print("✅ 模块导入成功")
        
        # 创建配置
        db_config = FaissConfig(
            host='127.0.0.1',
            port=8002,
            index_type='Flat',
            db_label=f'debug_step_by_step_{int(time.time())}'
        )
        
        db_case_config = FaissDBCaseConfig(
            metric_type=MetricType.COSINE
        )
        
        case_config = CaseConfig(
            case_id=CaseType.Performance1536D50K,
            k=10,  # 减少k值加快测试
            concurrency_search_config=ConcurrencySearchConfig(
                num_concurrency=[1],  # 只用1个并发
                concurrency_duration=10,  # 短测试时间
                concurrency_timeout=60
            )
        )
        
        task_config = TaskConfig(
            db=DB.Faiss,
            db_config=db_config,
            db_case_config=db_case_config,
            case_config=case_config,
            stages=['drop_old', 'load', 'search_serial']  # 简化阶段
        )
        
        print("✅ 配置创建成功")
        print(f"   🔧 任务配置: {task_config}")
        
        # 使用benchmark_runner直接执行
        print(f"\n🚀 通过benchmark_runner执行任务...")
        
        start_time = time.time()
        success = benchmark_runner.run([task_config], task_label='DebugStepByStep')
        
        if not success:
            print(f"❌ 任务提交失败: {benchmark_runner.latest_error}")
            return False
        
        print(f"✅ 任务提交成功，开始监控执行...")
        
        # 监控执行过程
        monitor_count = 0
        while benchmark_runner.has_running() and monitor_count < 60:  # 最多监控60秒
            current_task = benchmark_runner.get_current_task_id()
            total_tasks = benchmark_runner.get_tasks_count()
            print(f"   📊 执行进度: {current_task}/{total_tasks}")
            time.sleep(1)
            monitor_count += 1
        
        execution_time = time.time() - start_time
        print(f"\n⏰ 执行完成，总时间: {execution_time:.2f}s")
        
        if benchmark_runner.has_running():
            print("⚠️  任务仍在运行，可能超时")
            return False
        
        if benchmark_runner.latest_error:
            print(f"❌ 执行过程中出现错误: {benchmark_runner.latest_error}")
            return False
        
        print("✅ 任务执行完成，检查结果文件...")
        
        # 检查结果文件
        return check_generated_results()
        
    except Exception as e:
        print(f"❌ 逐步测试失败: {e}")
        traceback.print_exc()
        return False

def check_generated_results():
    """检查生成的结果文件"""
    print(f"\n📄 检查生成的结果文件")
    print("=" * 30)
    
    # 检查结果目录
    results_paths = [
        Path("vectordb_bench/results/Faiss"),
        Path("results/Faiss"),
        Path("vectordb_bench/results"),
        Path("results")
    ]
    
    found_results = False
    
    for results_path in results_paths:
        if results_path.exists():
            print(f"📁 检查目录: {results_path}")
            result_files = list(results_path.glob("*.json"))
            
            if result_files:
                found_results = True
                # 按修改时间排序，获取最新的
                latest_file = max(result_files, key=lambda x: x.stat().st_mtime)
                
                print(f"   ✅ 找到结果文件: {latest_file}")
                print(f"   📏 文件大小: {latest_file.stat().st_size} bytes")
                print(f"   🕐 修改时间: {time.ctime(latest_file.stat().st_mtime)}")
                
                # 尝试解析结果
                try:
                    import json
                    with open(latest_file, 'r') as f:
                        result_data = json.load(f)
                    
                    print(f"   📊 结果内容:")
                    print(f"      run_id: {result_data.get('run_id', 'N/A')}")
                    print(f"      task_label: {result_data.get('task_label', 'N/A')}")
                    
                    if 'results' in result_data and result_data['results']:
                        result = result_data['results'][0]
                        metrics = result.get('metrics', {})
                        
                        print(f"      🎯 性能指标:")
                        print(f"         QPS: {metrics.get('qps', 'N/A')}")
                        print(f"         时延(P99): {metrics.get('serial_latency_p99', 'N/A')}")
                        print(f"         召回率: {metrics.get('recall', 'N/A')}")
                        print(f"         加载时间: {metrics.get('load_duration', 'N/A')}")
                        
                        if metrics.get('qps') or metrics.get('recall') or metrics.get('serial_latency_p99'):
                            print(f"   🎉 找到详细性能指标！")
                            return True
                        else:
                            print(f"   ⚠️  结果文件存在但缺少性能指标")
                    else:
                        print(f"   ⚠️  结果文件格式异常")
                        
                except Exception as e:
                    print(f"   ❌ 解析结果文件失败: {e}")
            else:
                print(f"   ❌ 目录为空")
        else:
            print(f"❌ 目录不存在: {results_path}")
    
    if not found_results:
        print("❌ 未找到任何结果文件")
        
    return found_results

def analyze_execution_logs():
    """分析执行日志"""
    print(f"\n📋 分析执行日志")
    print("=" * 30)
    
    log_files = [
        'debug_detailed_results.log',
        'logs/vectordb_bench.log'
    ]
    
    for log_file in log_files:
        log_path = Path(log_file)
        if log_path.exists():
            print(f"📄 分析日志: {log_file}")
            
            try:
                with open(log_path, 'r') as f:
                    log_content = f.read()
                
                # 查找关键信息
                if 'Start performance case' in log_content:
                    print("   ✅ 发现性能测试开始日志")
                else:
                    print("   ❌ 未发现性能测试开始日志")
                
                if 'Performance case got result' in log_content:
                    print("   ✅ 发现性能测试结果日志")
                else:
                    print("   ❌ 未发现性能测试结果日志")
                
                if 'Failed to run performance case' in log_content:
                    print("   ⚠️  发现性能测试失败日志")
                
                # 查找错误信息
                error_lines = [line for line in log_content.split('\n') if 'ERROR' in line or 'Exception' in line]
                if error_lines:
                    print("   ⚠️  发现错误信息:")
                    for error_line in error_lines[-3:]:  # 显示最后3个错误
                        print(f"      {error_line}")
                        
            except Exception as e:
                print(f"   ❌ 读取日志失败: {e}")
        else:
            print(f"❌ 日志文件不存在: {log_file}")

def main():
    print("🔬 深入调试：详细结果生成问题根因分析")
    print("=" * 60)
    
    # 启用详细日志
    enable_detailed_logging()
    
    # 逐步测试
    success = test_step_by_step_execution()
    
    # 分析日志
    analyze_execution_logs()
    
    print(f"\n📋 调试总结:")
    print("=" * 20)
    
    if success:
        print("🎉 详细结果生成问题已解决！")
        print("远程FAISS基准测试现在可以生成完整的QPS、时延、召回率等指标。")
    else:
        print("❌ 详细结果生成问题仍然存在")
        print("需要进一步调试和分析日志")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
