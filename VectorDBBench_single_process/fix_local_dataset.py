#!/usr/bin/env python3
"""
Fix for FAISS Local hanging - Create a local dataset source
"""

import os
import sys
import shutil
from pathlib import Path

# Add the VectorDBBench module path
sys.path.insert(0, '/home/<USER>/VectorDBBench')

from vectordb_bench.backend.data_source import DatasetSource, DatasetReader
import pathlib
import logging

log = logging.getLogger(__name__)

class LocalDatasetReader(DatasetReader):
    """Local filesystem dataset reader - no download needed"""
    
    def __init__(self):
        self.source = DatasetSource.S3  # Pretend to be S3 but act locally
        self.remote_root = ""

    def read(self, dataset: str, files: list[str], local_ds_root: pathlib.Path):
        """Read dataset files from local filesystem (no download)"""
        print(f"📂 LocalDatasetReader: Using existing local files for {dataset}")
        
        dataset_path = local_ds_root / dataset
        if not dataset_path.exists():
            print(f"❌ Dataset directory not found: {dataset_path}")
            return
            
        print(f"✅ Found local dataset at: {dataset_path}")
        
        # Check if required files exist
        for file in files:
            if file is None:
                continue
            file_path = dataset_path / file
            if file_path.exists():
                print(f"✅ Found file: {file}")
            else:
                print(f"⚠️ Missing file: {file}")

    def validate_file(self, remote: pathlib.Path, local: pathlib.Path) -> bool:
        """Always return True for local files"""
        return local.exists()

# Monkey patch the DatasetSource to use local reader
def local_reader_patch():
    """Patch the S3 reader to use local filesystem"""
    original_reader = DatasetSource.reader
    
    def patched_reader(self):
        print(f"🔧 Using LocalDatasetReader instead of {self.value}")
        return LocalDatasetReader()
    
    DatasetSource.reader = patched_reader
    return original_reader

if __name__ == "__main__":
    print("🔧 Applying local dataset reader patch...")
    
    # Apply the patch
    original_reader = local_reader_patch()
    
    print("✅ Patch applied! Now run:")
    print("export DATASET_LOCAL_DIR=/home/<USER>/VectorDBBench/dataset")
    print("vectordbbench faisslocalhnsw --case-type Performance1536D50K --m 16 --ef-construction 200 --ef-search 100")
