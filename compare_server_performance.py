#!/usr/bin/env python3
"""
对比单进程和多进程FAISS服务器的性能
"""

import requests
import time
import numpy as np
import threading
import psutil
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

class ServerPerformanceTest:
    def __init__(self):
        self.servers = {
            "单进程版本": "http://10.1.180.64:8005",
            "多进程版本": "http://10.1.180.64:8006"
        }
        self.cpu_data = {}
        self.monitoring = False
    
    def check_server_status(self, name, url):
        """检查服务器状态"""
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                info_response = requests.get(f"{url}/info", timeout=5)
                if info_response.status_code == 200:
                    info = info_response.json()
                    print(f"✅ {name} 连接正常:")
                    print(f"   URL: {url}")
                    print(f"   版本: {info.get('version', 'unknown')}")
                    if 'process_info' in info:
                        proc_info = info['process_info']
                        print(f"   进程ID: {proc_info.get('process_id', 'unknown')}")
                        print(f"   FAISS线程数: {proc_info.get('faiss_threads', 'unknown')}")
                        if 'threads_per_process' in proc_info:
                            print(f"   每进程线程数: {proc_info['threads_per_process']}")
                    return True
            return False
        except Exception as e:
            print(f"❌ {name} 连接失败: {e}")
            return False
    
    def single_search_test(self, url, query_dim=768):
        """单次搜索测试"""
        query = np.random.random(query_dim).tolist()
        try:
            start_time = time.time()
            response = requests.post(
                f"{url}/search",
                json={"query": query, "topk": 100},
                timeout=30
            )
            latency = time.time() - start_time
            
            if response.status_code == 200:
                return True, latency
            else:
                return False, latency
        except Exception as e:
            return False, 0
    
    def concurrent_search_test(self, name, url, concurrency=64, duration=30):
        """并发搜索测试"""
        print(f"\n🚀 {name} 并发测试:")
        print(f"   并发数: {concurrency}")
        print(f"   持续时间: {duration}秒")
        
        results = {"success": 0, "failed": 0, "latencies": []}
        
        def worker():
            end_time = time.time() + duration
            while time.time() < end_time:
                success, latency = self.single_search_test(url)
                if success:
                    results["success"] += 1
                    results["latencies"].append(latency)
                else:
                    results["failed"] += 1
                time.sleep(0.01)  # 小延迟
        
        # 启动CPU监控
        self.start_cpu_monitoring(name)
        
        # 执行并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = [executor.submit(worker) for _ in range(concurrency)]
            for future in as_completed(futures):
                future.result()
        
        actual_duration = time.time() - start_time
        
        # 停止CPU监控
        self.stop_cpu_monitoring(name)
        
        # 计算统计信息
        total_requests = results["success"] + results["failed"]
        qps = results["success"] / actual_duration if actual_duration > 0 else 0
        avg_latency = sum(results["latencies"]) / len(results["latencies"]) if results["latencies"] else 0
        p99_latency = np.percentile(results["latencies"], 99) if results["latencies"] else 0
        
        print(f"   📊 测试结果:")
        print(f"     总请求数: {total_requests}")
        print(f"     成功请求: {results['success']}")
        print(f"     失败请求: {results['failed']}")
        print(f"     成功率: {results['success']/total_requests*100:.1f}%")
        print(f"     QPS: {qps:.1f}")
        print(f"     平均延迟: {avg_latency*1000:.1f}ms")
        print(f"     P99延迟: {p99_latency*1000:.1f}ms")
        
        return {
            "total_requests": total_requests,
            "success": results["success"],
            "failed": results["failed"],
            "qps": qps,
            "avg_latency": avg_latency,
            "p99_latency": p99_latency
        }
    
    def start_cpu_monitoring(self, server_name):
        """启动CPU监控"""
        self.monitoring = True
        self.cpu_data[server_name] = []
        
        def monitor_loop():
            while self.monitoring:
                cpu_percent = psutil.cpu_percent(percpu=True)
                self.cpu_data[server_name].append({
                    'timestamp': time.time(),
                    'cpu_total': psutil.cpu_percent(),
                    'cpu_per_core': cpu_percent
                })
                time.sleep(1)
        
        self.monitor_thread = threading.Thread(target=monitor_loop)
        self.monitor_thread.start()
    
    def stop_cpu_monitoring(self, server_name):
        """停止CPU监控"""
        self.monitoring = False
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join()
        
        # 分析CPU使用率
        if server_name in self.cpu_data and self.cpu_data[server_name]:
            self.analyze_cpu_usage(server_name)
    
    def analyze_cpu_usage(self, server_name):
        """分析CPU使用率"""
        data = self.cpu_data[server_name]
        if not data:
            return
        
        # 计算平均CPU使用率
        avg_cpu_total = sum(d['cpu_total'] for d in data) / len(data)
        max_cpu_total = max(d['cpu_total'] for d in data)
        
        # 计算各核心平均使用率
        core_count = len(data[0]['cpu_per_core'])
        core_avg = [0] * core_count
        
        for snapshot in data:
            for i, usage in enumerate(snapshot['cpu_per_core']):
                core_avg[i] += usage
        
        core_avg = [usage / len(data) for usage in core_avg]
        
        # 统计活跃核心
        active_cores = sum(1 for usage in core_avg if usage > 10)
        high_usage_cores = sum(1 for usage in core_avg if usage > 50)
        
        print(f"   🔥 CPU使用率分析:")
        print(f"     总体CPU使用率: 平均{avg_cpu_total:.1f}%, 峰值{max_cpu_total:.1f}%")
        print(f"     活跃核心数: {active_cores}/{core_count} ({active_cores/core_count*100:.1f}%)")
        print(f"     高负载核心数: {high_usage_cores}/{core_count} ({high_usage_cores/core_count*100:.1f}%)")
        
        # 显示前16个核心的使用率
        print(f"     前16核心使用率: ", end="")
        for i in range(min(16, core_count)):
            if core_avg[i] > 50:
                print(f"🔴{i}:{core_avg[i]:.0f}% ", end="")
            elif core_avg[i] > 10:
                print(f"🟡{i}:{core_avg[i]:.0f}% ", end="")
            else:
                print(f"⚪{i}:{core_avg[i]:.0f}% ", end="")
        print()
        
        return {
            "avg_cpu_total": avg_cpu_total,
            "max_cpu_total": max_cpu_total,
            "active_cores": active_cores,
            "high_usage_cores": high_usage_cores,
            "core_count": core_count
        }
    
    def run_comparison_test(self):
        """运行对比测试"""
        print("🎯 FAISS服务器性能对比测试")
        print("=" * 60)
        
        # 检查服务器状态
        available_servers = {}
        for name, url in self.servers.items():
            if self.check_server_status(name, url):
                available_servers[name] = url
            print()
        
        if len(available_servers) == 0:
            print("❌ 没有可用的服务器")
            return
        
        # 运行性能测试
        results = {}
        test_configs = [
            {"concurrency": 32, "duration": 30},
            {"concurrency": 64, "duration": 30},
            {"concurrency": 128, "duration": 30},
        ]
        
        for config in test_configs:
            print(f"\n" + "="*80)
            print(f"🚀 并发测试: {config['concurrency']} 并发, {config['duration']}秒")
            print("="*80)
            
            for name, url in available_servers.items():
                result = self.concurrent_search_test(
                    name, url, 
                    concurrency=config['concurrency'],
                    duration=config['duration']
                )
                
                if name not in results:
                    results[name] = []
                results[name].append({
                    "config": config,
                    "performance": result
                })
                
                time.sleep(5)  # 服务器恢复时间
        
        # 生成对比报告
        self.generate_comparison_report(results)
    
    def generate_comparison_report(self, results):
        """生成对比报告"""
        print(f"\n" + "="*80)
        print("📊 性能对比报告")
        print("="*80)
        
        for server_name, server_results in results.items():
            print(f"\n🖥️  {server_name}:")
            for result in server_results:
                config = result["config"]
                perf = result["performance"]
                print(f"   并发{config['concurrency']:3d}: QPS={perf['qps']:6.1f}, "
                      f"延迟={perf['avg_latency']*1000:5.1f}ms, "
                      f"成功率={perf['success']/(perf['success']+perf['failed'])*100:5.1f}%")
        
        # 找出最佳性能
        if len(results) >= 2:
            print(f"\n💡 性能对比总结:")
            server_names = list(results.keys())
            server1, server2 = server_names[0], server_names[1]
            
            for i, config in enumerate([{"concurrency": 32}, {"concurrency": 64}, {"concurrency": 128}]):
                if i < len(results[server1]) and i < len(results[server2]):
                    qps1 = results[server1][i]["performance"]["qps"]
                    qps2 = results[server2][i]["performance"]["qps"]
                    
                    if qps2 > qps1:
                        improvement = (qps2 - qps1) / qps1 * 100
                        print(f"   并发{config['concurrency']:3d}: {server2} 比 {server1} 快 {improvement:.1f}% "
                              f"({qps2:.1f} vs {qps1:.1f} QPS)")
                    else:
                        improvement = (qps1 - qps2) / qps2 * 100
                        print(f"   并发{config['concurrency']:3d}: {server1} 比 {server2} 快 {improvement:.1f}% "
                              f"({qps1:.1f} vs {qps2:.1f} QPS)")

def main():
    tester = ServerPerformanceTest()
    tester.run_comparison_test()

if __name__ == "__main__":
    main()
