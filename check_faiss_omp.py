#!/usr/bin/env python3
"""
检查FAISS OpenMP支持
"""

import os
import sys

# 设置环境变量
os.environ['OMP_NUM_THREADS'] = '16'

try:
    import faiss
    print(f"✅ FAISS版本: {faiss.__version__}")
    
    # 检查FAISS是否支持OpenMP
    print(f"\n🔍 检查FAISS OpenMP支持:")
    
    # 尝试获取当前线程数
    try:
        current_threads = faiss.omp_get_num_threads()
        print(f"   当前OpenMP线程数: {current_threads}")
    except Exception as e:
        print(f"   ❌ 无法获取OpenMP线程数: {e}")
    
    # 尝试设置线程数
    try:
        faiss.omp_set_num_threads(16)
        print(f"   ✅ 成功设置OpenMP线程数为16")
    except Exception as e:
        print(f"   ❌ 无法设置OpenMP线程数: {e}")
    
    # 再次检查线程数
    try:
        current_threads = faiss.omp_get_num_threads()
        print(f"   设置后OpenMP线程数: {current_threads}")
    except Exception as e:
        print(f"   ❌ 无法获取设置后的OpenMP线程数: {e}")
    
    # 检查FAISS是否支持并行化
    print(f"\n🔍 检查FAISS并行化支持:")
    
    # 创建不同类型的索引进行测试
    dimension = 768
    
    # 1. HNSW索引
    print(f"   1. 测试HNSW索引...")
    try:
        hnsw_index = faiss.IndexHNSWFlat(dimension, 30)
        print(f"      ✅ HNSW索引创建成功")
        
        # 检查HNSW索引的属性
        if hasattr(hnsw_index, 'hnsw'):
            print(f"      HNSW参数: M={hnsw_index.hnsw.M}, efConstruction={hnsw_index.hnsw.efConstruction}")
    except Exception as e:
        print(f"      ❌ HNSW索引创建失败: {e}")
    
    # 2. IVF索引
    print(f"   2. 测试IVF索引...")
    try:
        quantizer = faiss.IndexFlatL2(dimension)
        ivf_index = faiss.IndexIVFFlat(quantizer, dimension, 100)
        print(f"      ✅ IVF索引创建成功")
    except Exception as e:
        print(f"      ❌ IVF索引创建失败: {e}")
    
    # 3. Flat索引
    print(f"   3. 测试Flat索引...")
    try:
        flat_index = faiss.IndexFlatL2(dimension)
        print(f"      ✅ Flat索引创建成功")
    except Exception as e:
        print(f"      ❌ Flat索引创建失败: {e}")
    
    print(f"\n✅ FAISS OpenMP检查完成")
    
except ImportError as e:
    print(f"❌ FAISS导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 检查失败: {e}")
    sys.exit(1) 