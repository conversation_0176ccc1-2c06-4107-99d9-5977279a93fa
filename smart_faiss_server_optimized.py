#!/usr/bin/env python3
"""
优化版FAISS服务器 - 单进程 + 异步处理 + 线程池
解决CPU利用率打不满的问题
"""

import os
import sys
import time
import argparse
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional
import json
import logging
from datetime import datetime
import psutil
import gc
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
from contextlib import asynccontextmanager

# 在导入FAISS之前设置OpenMP线程数 - 动态配置
# 默认使用所有可用核心，可通过命令行参数覆盖
import psutil
DEFAULT_THREADS = min(psutil.cpu_count(), 16)  # 默认16个线程，但不超过CPU核心数
os.environ['OMP_NUM_THREADS'] = str(DEFAULT_THREADS)
os.environ['MKL_NUM_THREADS'] = str(DEFAULT_THREADS)
os.environ['OPENBLAS_NUM_THREADS'] = str(DEFAULT_THREADS)
os.environ['BLAS_NUM_THREADS'] = str(DEFAULT_THREADS)
os.environ['LAPACK_NUM_THREADS'] = str(DEFAULT_THREADS)

try:
    from fastapi import FastAPI, HTTPException, Request
    from pydantic import BaseModel
    import faiss
except ImportError as e:
    print(f"❌ 依赖缺失: {e}")
    print("💡 请安装: pip install fastapi uvicorn faiss-cpu pandas")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(process)d - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

# 全局变量
SHARED_INDEX = None
SHARED_INDEX_INFO = None
SEARCH_THREAD_POOL = None
DATASET_BASE_PATH = "/nas/yvan.chen/milvus/dataset"
PREBUILT_INDEX_PATH = "/nas/brian.mao/VectorDBBench_single_process"

# 🚦 流量控制配置
FLOW_CONTROL_CONFIG = {
    "batch_size": 10000,
    "base_delay": 0.001,
    "memory_threshold": 85,
    "adaptive_enabled": False,
}

# 数据集配置
DATASET_MAPPING = {
    "Performance768D10M": {
        "dataset": "cohere", 
        "subset": "cohere_large_10m", 
        "dimension": 768, 
        "target_count": 10000000, 
        "path": "cohere/cohere_large_10m", 
        "vectors": 10000000
    }
}

class SearchRequest(BaseModel):
    query: List[float]
    topk: int = 100

class SearchRequestLegacy(BaseModel):
    query: List[float]
    topk: int = 100

class SearchResponseLegacy(BaseModel):
    ids: List[List[int]]
    distances: List[List[float]]

class LoadRequest(BaseModel):
    case_config: Dict

class InsertRequest(BaseModel):
    vectors: List[List[float]]

class LegacyCreateIndexRequest(BaseModel):
    dim: int
    index_type: str
    m: Optional[int] = 30
    ef_construction: Optional[int] = 360
    batch_limit: Optional[int] = None
    incremental: Optional[bool] = False

def load_prebuilt_index():
    """加载预构建索引到内存"""
    global SHARED_INDEX, SHARED_INDEX_INFO
    
    target_index_file = "faiss_hnsw_768d_10m.index"
    index_path = os.path.join(PREBUILT_INDEX_PATH, target_index_file)
    
    if os.path.exists(index_path):
        try:
            logger.info(f"🔄 加载预构建索引: {target_index_file}")
            start_time = time.time()
            
            # 加载索引
            index = faiss.read_index(index_path)
            load_time = time.time() - start_time
            
            SHARED_INDEX = index
            SHARED_INDEX_INFO = {
                "dimension": index.d,
                "total_vectors": index.ntotal,
                "index_type": "HNSW",
                "load_time": load_time
            }
            
            logger.info(f"✅ 索引加载完成: {index.ntotal:,} 向量, 耗时: {load_time:.2f}秒")
            
            # 预热索引
            warmup_index(index, index.d)
            
        except Exception as e:
            logger.error(f"❌ 索引加载失败: {e}")
    else:
        logger.error(f"❌ 索引文件不存在: {index_path}")

def warmup_index(index, dimension):
    """预热索引缓存 - 更激进的预热策略"""
    logger.info("🔥 开始预热索引缓存...")
    try:
        # 增加预热查询数量
        warmup_queries = np.random.random((10000, dimension)).astype('float32')
        
        # 使用实际并发场景的批次大小
        batch_sizes = [96, 128, 192, 256, 320, 384]
        for batch_size in batch_sizes:
            start_time = time.time()
            # 使用实际的topk值
            for topk in [100]:
                for i in range(0, 10000, batch_size):
                    end_idx = min(i + batch_size, 10000)
                    batch = warmup_queries[i:end_idx]
                    index.search(batch, topk)
            
            elapsed = time.time() - start_time
            logger.info(f"✅ 批次大小 {batch_size} 预热完成, 耗时: {elapsed:.2f}秒")
        
        logger.info("🚀 索引预热完成")
    except Exception as e:
        logger.error(f"❌ 索引预热失败: {e}")

def search_in_thread(query_array: np.ndarray, topk: int, ef_search: Optional[int] = None) -> Dict:
    """在线程中执行搜索，释放GIL - 优化版本"""
    if SHARED_INDEX is None:
        raise ValueError("索引未初始化")

    # 设置HNSW参数 - 每个线程独立设置
    if hasattr(SHARED_INDEX, 'hnsw'):
        ef_value = ef_search if ef_search is not None else max(topk * 2, 256)  # 动态调整ef_search
        SHARED_INDEX.hnsw.efSearch = ef_value

    # 执行搜索 - 这里是CPU密集型操作，会利用OpenMP并行
    start_time = time.time()
    D, I = SHARED_INDEX.search(query_array, topk)
    search_time = time.time() - start_time

    # 记录性能指标（可选）
    if search_time > 0.1:  # 只记录较慢的查询
        logger.debug(f"🐌 慢查询: {search_time:.3f}s, batch_size={len(query_array)}, topk={topk}")

    return {"ids": I.tolist(), "distances": D.tolist()}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI lifespan event handler"""
    global SEARCH_THREAD_POOL
    
    # 启动事件
    logger.info(f"🚀 进程 {os.getpid()} 启动")
    
    # 创建线程池用于CPU密集型操作 - 动态配置线程数
    # 使用更多线程来充分利用CPU核心，考虑超线程
    cpu_count = psutil.cpu_count()
    # 对于CPU密集型任务，线程数可以设置为CPU核心数的1.5-2倍
    optimal_workers = min(cpu_count * 2, 64)  # 最多64个worker
    SEARCH_THREAD_POOL = ThreadPoolExecutor(max_workers=optimal_workers, thread_name_prefix="faiss-worker")
    logger.info(f"🧵 创建线程池: {optimal_workers} workers (CPU核心: {cpu_count})")
    
    # 加载索引
    load_prebuilt_index()
    
    logger.info(f"✅ 进程 {os.getpid()} 启动完成")
    
    yield
    
    # 关闭事件
    logger.info(f"🔄 进程 {os.getpid()} 关闭")
    if SEARCH_THREAD_POOL:
        SEARCH_THREAD_POOL.shutdown(wait=True)
    logger.info(f"✅ 进程 {os.getpid()} 关闭完成")

# 创建FastAPI应用，使用lifespan
app = FastAPI(title="Optimized FAISS Server", version="4.0.0", lifespan=lifespan)

@app.get("/")
async def root():
    return {
        "message": "Optimized FAISS Server", 
        "status": "running", 
        "version": "4.0.0",
        "process_id": os.getpid()
    }

@app.post("/load")
async def load_dataset(request: LoadRequest):
    """兼容性接口 - 实际使用预加载索引"""
    return {
        "success": True,
        "message": "使用预加载索引",
        "vectors_loaded": SHARED_INDEX_INFO["total_vectors"] if SHARED_INDEX_INFO else 0
    }

@app.post("/create_index")
async def create_index(request: LegacyCreateIndexRequest):
    """兼容性接口 - 创建索引（实际使用预加载索引）"""
    if SHARED_INDEX_INFO and SHARED_INDEX_INFO["dimension"] == request.dim:
        return {
            "success": True,
            "message": f"使用预加载索引: {request.index_type}",
            "vectors_loaded": SHARED_INDEX_INFO["total_vectors"]
        }
    else:
        raise HTTPException(status_code=400, detail=f"不支持的维度: {request.dim}")

@app.post("/insert_bulk")
async def insert_bulk(request: InsertRequest):
    """兼容性接口 - 批量插入（智能跳过）"""
    if SHARED_INDEX and SHARED_INDEX_INFO["total_vectors"] >= 1000000:
        return {
            "success": True,
            "inserted": len(request.vectors),
            "total": SHARED_INDEX_INFO["total_vectors"],
            "message": "智能缓存生效，跳过插入"
        }
    else:
        raise HTTPException(status_code=400, detail="索引未初始化")

@app.post("/search")
async def search(request: Request):
    """异步搜索端点 - 使用线程池避免阻塞"""
    try:
        data = await request.json()
        query = np.array([data["query"]], dtype="float32")
        topk = data.get("topk", 100)
        ef_search = data.get("ef_search", None)
        
        if SHARED_INDEX is None:
            raise HTTPException(status_code=400, detail="索引未初始化")
        
        # 在线程池中异步执行搜索
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            SEARCH_THREAD_POOL,
            search_in_thread,
            query,
            topk,
            ef_search
        )
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {e}")

@app.post("/batch_search")
async def batch_search(request: Request):
    """异步批量搜索 - 优化并行处理"""
    data = await request.json()
    queries = np.array(data["queries"], dtype="float32")
    topk = data.get("topk", 100)
    ef_search = data.get("ef_search", None)

    if SHARED_INDEX is None:
        raise HTTPException(status_code=400, detail="索引未初始化")

    try:
        # 动态调整批次大小，基于查询数量和CPU核心数
        cpu_count = psutil.cpu_count()
        query_count = len(queries)

        # 优化批次大小：小查询用大批次，大查询用小批次
        if query_count <= 100:
            batch_size = query_count  # 小查询直接处理
        elif query_count <= 1000:
            batch_size = max(32, query_count // cpu_count)  # 中等查询
        else:
            batch_size = 16  # 大查询用小批次，增加并行度

        logger.debug(f"🔍 批量搜索: {query_count} queries, batch_size={batch_size}")

        tasks = []
        loop = asyncio.get_event_loop()

        for i in range(0, len(queries), batch_size):
            batch = queries[i:i+batch_size]
            task = loop.run_in_executor(
                SEARCH_THREAD_POOL,
                search_in_thread,
                batch,
                topk,
                ef_search
            )
            tasks.append(task)

        # 等待所有任务完成
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time

        # 合并结果
        all_ids = []
        all_distances = []
        for result in results:
            all_ids.extend(result["ids"])
            all_distances.extend(result["distances"])

        logger.debug(f"✅ 批量搜索完成: {query_count} queries, {total_time:.3f}s, QPS={query_count/total_time:.1f}")

        return {"ids": all_ids, "distances": all_distances}

    except Exception as e:
        logger.error(f"批量搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量搜索失败: {e}")

@app.post("/search_legacy")
async def search_legacy(request: SearchRequestLegacy):
    """兼容性接口 - 单个搜索"""
    try:
        query = np.array([request.query], dtype="float32")
        
        if SHARED_INDEX is None:
            raise HTTPException(status_code=400, detail="索引未初始化")
        
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            SEARCH_THREAD_POOL,
            search_in_thread,
            query,
            request.topk,
            None
        )
        
        return SearchResponseLegacy(
            ids=[result["ids"][0]],
            distances=[result["distances"][0]]
        )
        
    except Exception as e:
        logger.error(f"❌ 搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {e}")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy", 
        "timestamp": datetime.now().isoformat(),
        "process_id": os.getpid()
    }

@app.get("/info")
async def get_info():
    """获取服务器信息"""
    return {
        "server": "Optimized FAISS Server",
        "version": "4.0.0",
        "status": "ready" if SHARED_INDEX else "not_ready",
        "features": [
            "单进程 + 异步处理",
            "线程池并发",
            "预加载索引",
            "高性能搜索",
            "完整API兼容"
        ],
        "当前状态": {
            "向量数量": SHARED_INDEX_INFO["total_vectors"] if SHARED_INDEX_INFO else 0,
            "索引类型": SHARED_INDEX_INFO["index_type"] if SHARED_INDEX_INFO else "无",
            "维度": SHARED_INDEX_INFO["dimension"] if SHARED_INDEX_INFO else 0,
        },
        "进程信息": {
            "进程ID": os.getpid(),
            "CPU核心": os.cpu_count(),
        }
    }

@app.get("/status")
async def get_status():
    """获取服务器状态"""
    if SHARED_INDEX_INFO:
        return {
            "status": "ready",
            "total_vectors": SHARED_INDEX_INFO["total_vectors"],
            "vectors_count": SHARED_INDEX_INFO["total_vectors"],
            "vectors_loaded": SHARED_INDEX_INFO["total_vectors"],
            "dimension": SHARED_INDEX_INFO["dimension"],
            "index_type": SHARED_INDEX_INFO["index_type"],
            "process_id": os.getpid()
        }
    else:
        return {
            "status": "not_ready", 
            "total_vectors": 0,
            "vectors_count": 0,
            "vectors_loaded": 0,
            "process_id": os.getpid()
        }

def main():
    parser = argparse.ArgumentParser(description="优化版FAISS服务器")
    parser.add_argument("--port", type=int, default=8005, help="服务器端口")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="服务器地址")
    parser.add_argument("--omp-threads", type=int, default=16, help="OMP线程数")
    
    # 🚦 流量控制配置 - 兼容原始服务器参数
    flow_control_group = parser.add_argument_group('流量控制配置')
    flow_control_group.add_argument("--batch-size", type=int, default=10000,
                                   help="数据加载批次大小 (默认: 10000)")
    flow_control_group.add_argument("--base-delay", type=float, default=0.001,
                                   help="基础延迟时间(秒) (默认: 0.001)")
    flow_control_group.add_argument("--memory-threshold", type=int, default=85,
                                   help="内存使用阈值百分比 (默认: 85)")
    
    args = parser.parse_args()
    
    # 🚦 更新流量控制配置
    FLOW_CONTROL_CONFIG.update({
        "batch_size": args.batch_size,
        "base_delay": args.base_delay,
        "memory_threshold": args.memory_threshold,
    })
    
    # 设置OpenMP线程数 - 必须在导入FAISS之前设置
    os.environ['OMP_NUM_THREADS'] = str(args.omp_threads)
    os.environ['MKL_NUM_THREADS'] = str(args.omp_threads)
    os.environ['OPENBLAS_NUM_THREADS'] = str(args.omp_threads)
    os.environ['BLAS_NUM_THREADS'] = str(args.omp_threads)
    os.environ['LAPACK_NUM_THREADS'] = str(args.omp_threads)

    # 重新设置FAISS的线程数
    faiss.omp_set_num_threads(args.omp_threads)

    # 验证线程数设置
    actual_threads = faiss.omp_get_max_threads()
    logger.info(f"🔧 FAISS OpenMP线程数: 设置={args.omp_threads}, 实际={actual_threads}")

    # 设置更激进的OpenMP策略以提高CPU利用率
    os.environ['OMP_WAIT_POLICY'] = 'ACTIVE'      # 活跃等待，提高响应速度
    os.environ['OMP_DYNAMIC'] = 'false'           # 禁用动态调整，使用固定线程数
    os.environ['OMP_NESTED'] = 'true'             # 允许嵌套并行
    os.environ['OMP_PROC_BIND'] = 'spread'        # 将线程分散到不同核心
    os.environ['OMP_PLACES'] = 'cores'            # 绑定到物理核心
    
    print(f"🚀 启动优化版FAISS服务器")
    print(f"📊 配置:")
    print(f"   CPU核心数: {psutil.cpu_count()}")
    print(f"   OMP线程数: {args.omp_threads}")
    print(f"   线程池大小: {min(psutil.cpu_count() * 2, 64)}")
    print(f"   异步处理: 启用")
    print(f"   内存阈值: {args.memory_threshold}%")
    print(f"   批次大小: {args.batch_size}")
    print(f"   基础延迟: {args.base_delay}s")
    print(f"🔧 OpenMP策略:")
    print(f"   等待策略: ACTIVE (活跃等待)")
    print(f"   动态调整: 禁用")
    print(f"   进程绑定: spread (分散到不同核心)")
    print(f"   绑定位置: cores (物理核心)")
    
    try:
        import uvicorn
        
        # 使用单进程模式，但通过线程池提高并发
        uvicorn.run(
            "smart_faiss_server_optimized:app",
            host=args.host,
            port=args.port,
            workers=1,  # 单进程避免内存重复
            log_level="info",
            loop="asyncio",
            limit_concurrency=50000,  # 提高并发限制
            access_log=False,
            backlog=8192,
            timeout_keep_alive=60
        )
    except ImportError:
        print("❌ uvicorn未安装")
        sys.exit(1)

if __name__ == "__main__":
    main() 