#!/bin/bash

# 多进程FAISS服务器启动脚本
# 解决Python GIL限制，真正利用多核CPU

echo "🚀 启动多进程FAISS服务器"
echo "=========================="

# 检查Python版本
PYTHON_CMD="python3.11"
if ! command -v $PYTHON_CMD &> /dev/null; then
    echo "❌ 未找到 $PYTHON_CMD，尝试使用 python3"
    PYTHON_CMD="python3"
    if ! command -v $PYTHON_CMD &> /dev/null; then
        echo "❌ 未找到 Python3，请安装 Python 3.11+"
        exit 1
    fi
fi

echo "✅ 使用Python: $PYTHON_CMD"

# 获取CPU信息
CPU_CORES=$(nproc)
CPU_THREADS=$(nproc --all)
echo "💻 系统信息:"
echo "   物理核心: $CPU_CORES"
echo "   逻辑线程: $CPU_THREADS"

# 计算最优进程数和每进程线程数
# 策略：使用8个进程，每个进程2-4个线程
WORKERS=${1:-8}
THREADS_PER_PROCESS=${2:-$((CPU_CORES / WORKERS))}

# 确保每进程至少2个线程
if [ $THREADS_PER_PROCESS -lt 2 ]; then
    THREADS_PER_PROCESS=2
fi

# 确保总线程数不超过CPU核心数
TOTAL_THREADS=$((WORKERS * THREADS_PER_PROCESS))
if [ $TOTAL_THREADS -gt $CPU_CORES ]; then
    THREADS_PER_PROCESS=$((CPU_CORES / WORKERS))
    if [ $THREADS_PER_PROCESS -lt 1 ]; then
        THREADS_PER_PROCESS=1
    fi
    TOTAL_THREADS=$((WORKERS * THREADS_PER_PROCESS))
fi

echo "🔧 多进程配置:"
echo "   工作进程数: $WORKERS"
echo "   每进程线程数: $THREADS_PER_PROCESS"
echo "   总线程数: $TOTAL_THREADS"
echo "   架构: 多进程 + OpenMP (绕过GIL)"
echo "   端口: 8006"

# 检查是否有旧进程在运行
OLD_PID=$(pgrep -f "smart_faiss_server")
if [ ! -z "$OLD_PID" ]; then
    echo "⚠️  发现旧FAISS服务器进程:"
    ps aux | grep smart_faiss_server | grep -v grep
    read -p "是否终止所有旧进程? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        pkill -f "smart_faiss_server"
        sleep 3
        echo "✅ 旧进程已终止"
    else
        echo "❌ 取消启动"
        exit 1
    fi
fi

# 设置环境变量
export OMP_WAIT_POLICY=ACTIVE
export OMP_PROC_BIND=close
export OMP_PLACES=cores

echo "⚙️  环境变量已设置:"
echo "   OMP_WAIT_POLICY=$OMP_WAIT_POLICY"
echo "   OMP_PROC_BIND=$OMP_PROC_BIND"
echo "   OMP_PLACES=$OMP_PLACES"

# 使用numactl绑定（如果可用）
NUMA_CMD=""
if command -v numactl &> /dev/null; then
    # 绑定到所有可用核心
    NUMA_CMD="numactl --cpunodebind=0 --membind=0"
    echo "🎯 使用NUMA绑定: $NUMA_CMD"
else
    echo "⚠️  numactl不可用，跳过NUMA绑定"
fi

# 启动命令
START_CMD="$NUMA_CMD $PYTHON_CMD smart_faiss_server_multiprocess.py \
    --host 0.0.0.0 \
    --port 8006 \
    --workers $WORKERS \
    --threads-per-process $THREADS_PER_PROCESS"

echo ""
echo "🚀 启动命令:"
echo "$START_CMD"
echo ""

# 询问是否启动
read -p "是否启动多进程服务器? (Y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Nn]$ ]]; then
    echo "❌ 取消启动"
    exit 0
fi

# 启动服务器
echo "🚀 正在启动多进程服务器..."
echo "📝 日志将显示在下方，按 Ctrl+C 停止服务器"
echo "🔗 服务器地址: http://localhost:8006"
echo "=" * 60

# 启动并显示实时日志
exec $START_CMD
