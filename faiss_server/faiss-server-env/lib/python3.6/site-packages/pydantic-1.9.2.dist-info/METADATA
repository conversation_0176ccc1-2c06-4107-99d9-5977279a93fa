Metadata-Version: 2.1
Name: pydantic
Version: 1.9.2
Summary: Data validation and settings management using python type hints
Home-page: https://github.com/samuelcolvin/pydantic
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Classifier: Development Status :: 5 - Production/Stable
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: Unix
Classifier: Operating System :: POSIX :: Linux
Classifier: Environment :: Console
Classifier: Environment :: MacOS X
Classifier: Framework :: Hypothesis
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Internet
Requires-Python: >=3.6.1
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: typing-extensions (>=*******)
Requires-Dist: dataclasses (>=0.6) ; python_version < "3.7"
Provides-Extra: dotenv
Requires-Dist: python-dotenv (>=0.10.4) ; extra == 'dotenv'
Provides-Extra: email
Requires-Dist: email-validator (>=1.0.3) ; extra == 'email'

# pydantic

[![CI](https://github.com/samuelcolvin/pydantic/workflows/CI/badge.svg?event=push)](https://github.com/samuelcolvin/pydantic/actions?query=event%3Apush+branch%3Amaster+workflow%3ACI)
[![Coverage](https://coverage-badge.samuelcolvin.workers.dev/samuelcolvin/pydantic.svg?v=1)](https://coverage-badge.samuelcolvin.workers.dev/redirect/samuelcolvin/pydantic)
[![pypi](https://img.shields.io/pypi/v/pydantic.svg)](https://pypi.python.org/pypi/pydantic)
[![CondaForge](https://img.shields.io/conda/v/conda-forge/pydantic.svg)](https://anaconda.org/conda-forge/pydantic)
[![downloads](https://pepy.tech/badge/pydantic/month)](https://pepy.tech/project/pydantic)
[![versions](https://img.shields.io/pypi/pyversions/pydantic.svg)](https://github.com/samuelcolvin/pydantic)
[![license](https://img.shields.io/github/license/samuelcolvin/pydantic.svg)](https://github.com/samuelcolvin/pydantic/blob/master/LICENSE)

Data validation and settings management using Python type hints.

Fast and extensible, *pydantic* plays nicely with your linters/IDE/brain.
Define how data should be in pure, canonical Python 3.6+; validate it with *pydantic*.

## Help

See [documentation](https://pydantic-docs.helpmanual.io/) for more details.

## Installation

Install using `pip install -U pydantic` or `conda install pydantic -c conda-forge`.
For more installation options to make *pydantic* even faster,
see the [Install](https://pydantic-docs.helpmanual.io/install/) section in the documentation.

## A Simple Example

```py
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel

class User(BaseModel):
    id: int
    name = 'John Doe'
    signup_ts: Optional[datetime] = None
    friends: List[int] = []

external_data = {'id': '123', 'signup_ts': '2017-06-01 12:22', 'friends': [1, '2', b'3']}
user = User(**external_data)
print(user)
#> User id=123 name='John Doe' signup_ts=datetime.datetime(2017, 6, 1, 12, 22) friends=[1, 2, 3]
print(user.id)
#> 123
```

## Contributing

For guidance on setting up a development environment and how to make a
contribution to *pydantic*, see
[Contributing to Pydantic](https://pydantic-docs.helpmanual.io/contributing/).

## Reporting a Security Vulnerability

See our [security policy](https://github.com/samuelcolvin/pydantic/security/policy).


## v1.9.2 (2022-08-11)

**Revert Breaking Change**: _v1.9.1_ introduced a breaking change where model fields were
deep copied by default, this release reverts the default behaviour to match _v1.9.0_ and before, 
while also allow deep-copy behaviour via `copy_on_model_validation = 'deep'`. See [#4092](https://github.com/samuelcolvin/pydantic/issues/4092) for more information.

* Allow for shallow copies of model fields, `Config.copy_on_model_validation` is now a str which must be
  `'none'`, `'deep'`, or `'shallow'` corresponding to not copying, deep copy & shallow copy; default `'shallow'`, 
  [#4093](https://github.com/samuelcolvin/pydantic/issues/4093) by [@timkpaine](https://github.com/timkpaine)

## v1.9.1 (2022-05-19)

Thank you to pydantic's sponsors:
@tiangolo, [@stellargraph](https://github.com/stellargraph), [@JonasKs](https://github.com/JonasKs), [@grillazz](https://github.com/grillazz), [@Mazyod](https://github.com/Mazyod), [@kevinalh](https://github.com/kevinalh), [@chdsbd](https://github.com/chdsbd), [@povilasb](https://github.com/povilasb), [@povilasb](https://github.com/povilasb), [@jina-ai](https://github.com/jina-ai), 
@mainframeindustries, [@robusta-dev](https://github.com/robusta-dev), [@SendCloud](https://github.com/SendCloud), [@rszamszur](https://github.com/rszamszur), [@jodal](https://github.com/jodal), [@hardbyte](https://github.com/hardbyte), [@corleyma](https://github.com/corleyma), [@daddycocoaman](https://github.com/daddycocoaman), 
@Rehket, [@jokull](https://github.com/jokull), [@reillysiemens](https://github.com/reillysiemens), [@westonsteimel](https://github.com/westonsteimel), [@primer-io](https://github.com/primer-io), [@koxudaxi](https://github.com/koxudaxi), [@browniebroke](https://github.com/browniebroke), [@stradivari96](https://github.com/stradivari96), 
@adriangb, [@kamalgill](https://github.com/kamalgill), [@jqueguiner](https://github.com/jqueguiner), [@dev-zero](https://github.com/dev-zero), [@datarootsio](https://github.com/datarootsio), [@RedCarpetUp](https://github.com/RedCarpetUp)
for their kind support.

* Limit the size of `generics._generic_types_cache` and `generics._assigned_parameters` 
  to avoid unlimited increase in memory usage, [#4083](https://github.com/samuelcolvin/pydantic/issues/4083) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add Jupyverse and FPS as Jupyter projects using pydantic, [#4082](https://github.com/samuelcolvin/pydantic/issues/4082) by [@davidbrochart](https://github.com/davidbrochart)
* Speedup `__isinstancecheck__` on pydantic models when the type is not a model, may also avoid memory "leaks", [#4081](https://github.com/samuelcolvin/pydantic/issues/4081) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix in-place modification of `FieldInfo` that caused problems with PEP 593 type aliases, [#4067](https://github.com/samuelcolvin/pydantic/issues/4067) by [@adriangb](https://github.com/adriangb)
* Add support for autocomplete in VS Code via `__dataclass_transform__` when using `pydantic.dataclasses.dataclass`, [#4006](https://github.com/samuelcolvin/pydantic/issues/4006) by [@giuliano-oliveira](https://github.com/giuliano-oliveira)
* Remove benchmarks from codebase and docs, [#3973](https://github.com/samuelcolvin/pydantic/issues/3973) by [@samuelcolvin](https://github.com/samuelcolvin)
* Typing checking with pyright in CI, improve docs on vscode/pylance/pyright, [#3972](https://github.com/samuelcolvin/pydantic/issues/3972) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix nested Python dataclass schema regression, [#3819](https://github.com/samuelcolvin/pydantic/issues/3819) by [@himbeles](https://github.com/himbeles)
* Update documentation about lazy evaluation of sources for Settings, [#3806](https://github.com/samuelcolvin/pydantic/issues/3806) by [@garyd203](https://github.com/garyd203)
* Prevent subclasses of bytes being converted to bytes, [#3706](https://github.com/samuelcolvin/pydantic/issues/3706) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fixed "error checking inheritance of" when using PEP585 and PEP604 type hints, [#3681](https://github.com/samuelcolvin/pydantic/issues/3681) by [@aleksul](https://github.com/aleksul)
* Allow self referencing `ClassVar`s in models, [#3679](https://github.com/samuelcolvin/pydantic/issues/3679) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix issue with self-referencing dataclass, [#3675](https://github.com/samuelcolvin/pydantic/issues/3675) by [@uriyyo](https://github.com/uriyyo)
* Include non-standard port numbers in rendered URLs, [#3652](https://github.com/samuelcolvin/pydantic/issues/3652) by [@dolfinus](https://github.com/dolfinus)
* `Config.copy_on_model_validation` does a deep copy and not a shallow one, [#3641](https://github.com/samuelcolvin/pydantic/issues/3641) by [@PrettyWood](https://github.com/PrettyWood)
* fix: clarify that discriminated unions do not support singletons, [#3636](https://github.com/samuelcolvin/pydantic/issues/3636) by [@tommilligan](https://github.com/tommilligan)
* Add `read_text(encoding='utf-8')` for `setup.py`, [#3625](https://github.com/samuelcolvin/pydantic/issues/3625) by [@hswong3i](https://github.com/hswong3i)
* Fix JSON Schema generation for Discriminated Unions within lists, [#3608](https://github.com/samuelcolvin/pydantic/issues/3608) by [@samuelcolvin](https://github.com/samuelcolvin)

## v1.9.0 (2021-12-31)

Thank you to pydantic's sponsors:
@sthagen, [@timdrijvers](https://github.com/timdrijvers), [@toinbis](https://github.com/toinbis), [@koxudaxi](https://github.com/koxudaxi), [@ginomempin](https://github.com/ginomempin), [@primer-io](https://github.com/primer-io), [@and-semakin](https://github.com/and-semakin), [@westonsteimel](https://github.com/westonsteimel), [@reillysiemens](https://github.com/reillysiemens),
@es3n1n, [@jokull](https://github.com/jokull), [@JonasKs](https://github.com/JonasKs), [@Rehket](https://github.com/Rehket), [@corleyma](https://github.com/corleyma), [@daddycocoaman](https://github.com/daddycocoaman), [@hardbyte](https://github.com/hardbyte), [@datarootsio](https://github.com/datarootsio), [@jodal](https://github.com/jodal), [@aminalaee](https://github.com/aminalaee), [@rafsaf](https://github.com/rafsaf), 
@jqueguiner, [@chdsbd](https://github.com/chdsbd), [@kevinalh](https://github.com/kevinalh), [@Mazyod](https://github.com/Mazyod), [@grillazz](https://github.com/grillazz), [@JonasKs](https://github.com/JonasKs), [@simw](https://github.com/simw), [@leynier](https://github.com/leynier), [@xfenix](https://github.com/xfenix)
for their kind support.

### Highlights

* add python 3.10 support, [#2885](https://github.com/samuelcolvin/pydantic/issues/2885) by [@PrettyWood](https://github.com/PrettyWood)
* [Discriminated unions](https://pydantic-docs.helpmanual.io/usage/types/#discriminated-unions-aka-tagged-unions), [#619](https://github.com/samuelcolvin/pydantic/issues/619) by [@PrettyWood](https://github.com/PrettyWood)
* [`Config.smart_union` for better union logic](https://pydantic-docs.helpmanual.io/usage/model_config/#smart-union), [#2092](https://github.com/samuelcolvin/pydantic/issues/2092) by [@PrettyWood](https://github.com/PrettyWood)
* Binaries for Macos M1 CPUs, [#3498](https://github.com/samuelcolvin/pydantic/issues/3498) by [@samuelcolvin](https://github.com/samuelcolvin)
* Complex types can be set via [nested environment variables](https://pydantic-docs.helpmanual.io/usage/settings/#parsing-environment-variable-values), e.g. `foo___bar`, [#3159](https://github.com/samuelcolvin/pydantic/issues/3159) by [@Air-Mark](https://github.com/Air-Mark)
* add a dark mode to _pydantic_ documentation, [#2913](https://github.com/samuelcolvin/pydantic/issues/2913) by [@gbdlin](https://github.com/gbdlin)
* Add support for autocomplete in VS Code via `__dataclass_transform__`, [#2721](https://github.com/samuelcolvin/pydantic/issues/2721) by [@tiangolo](https://github.com/tiangolo)
* Add "exclude" as a field parameter so that it can be configured using model config, [#660](https://github.com/samuelcolvin/pydantic/issues/660) by [@daviskirk](https://github.com/daviskirk)

### v1.9.0 (2021-12-31) Changes

* Apply `update_forward_refs` to `Config.json_encodes` prevent name clashes in types defined via strings, [#3583](https://github.com/samuelcolvin/pydantic/issues/3583) by [@samuelcolvin](https://github.com/samuelcolvin)
* Extend pydantic's mypy plugin to support mypy versions `0.910`, `0.920`, `0.921` & `0.930`, [#3573](https://github.com/samuelcolvin/pydantic/issues/3573) & [#3594](https://github.com/samuelcolvin/pydantic/issues/3594) by [@PrettyWood](https://github.com/PrettyWood), [@christianbundy](https://github.com/christianbundy), [@samuelcolvin](https://github.com/samuelcolvin)

### v1.9.0a2 (2021-12-24) Changes

* support generic models with discriminated union, [#3551](https://github.com/samuelcolvin/pydantic/issues/3551) by [@PrettyWood](https://github.com/PrettyWood)
* keep old behaviour of `json()` by default, [#3542](https://github.com/samuelcolvin/pydantic/issues/3542) by [@PrettyWood](https://github.com/PrettyWood)
* Removed typing-only `__root__` attribute from `BaseModel`, [#3540](https://github.com/samuelcolvin/pydantic/issues/3540) by [@layday](https://github.com/layday)
* Build Python 3.10 wheels, [#3539](https://github.com/samuelcolvin/pydantic/issues/3539) by [@mbachry](https://github.com/mbachry)
* Fix display of `extra` fields with model `__repr__`, [#3234](https://github.com/samuelcolvin/pydantic/issues/3234) by [@cocolman](https://github.com/cocolman)
* models copied via `Config.copy_on_model_validation` always have all fields, [#3201](https://github.com/samuelcolvin/pydantic/issues/3201) by [@PrettyWood](https://github.com/PrettyWood)
* nested ORM from nested dictionaries, [#3182](https://github.com/samuelcolvin/pydantic/issues/3182) by [@PrettyWood](https://github.com/PrettyWood)
* fix link to discriminated union section by [@PrettyWood](https://github.com/PrettyWood)

### v1.9.0a1 (2021-12-18) Changes

* Add support for `Decimal`-specific validation configurations in `Field()`, additionally to using `condecimal()`, 
  to allow better support from editors and tooling, [#3507](https://github.com/samuelcolvin/pydantic/issues/3507) by [@tiangolo](https://github.com/tiangolo)
* Add `arm64` binaries suitable for MacOS with an M1 CPU to PyPI, [#3498](https://github.com/samuelcolvin/pydantic/issues/3498) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix issue where `None` was considered invalid when using a `Union` type containing `Any` or `object`, [#3444](https://github.com/samuelcolvin/pydantic/issues/3444) by [@tharradine](https://github.com/tharradine)
* When generating field schema, pass optional `field` argument (of type
  `pydantic.fields.ModelField`) to `__modify_schema__()` if present, [#3434](https://github.com/samuelcolvin/pydantic/issues/3434) by [@jasujm](https://github.com/jasujm)
* Fix issue when pydantic fail to parse `typing.ClassVar` string type annotation, [#3401](https://github.com/samuelcolvin/pydantic/issues/3401) by [@uriyyo](https://github.com/uriyyo)
* Mention Python >= 3.9.2 as an alternative to `typing_extensions.TypedDict`, [#3374](https://github.com/samuelcolvin/pydantic/issues/3374) by [@BvB93](https://github.com/BvB93)
* Changed the validator method name in the [Custom Errors example](https://pydantic-docs.helpmanual.io/usage/models/#custom-errors) 
  to more accurately describe what the validator is doing; changed from `name_must_contain_space` to ` value_must_equal_bar`, [#3327](https://github.com/samuelcolvin/pydantic/issues/3327) by [@michaelrios28](https://github.com/michaelrios28)
* Add `AmqpDsn` class, [#3254](https://github.com/samuelcolvin/pydantic/issues/3254) by [@kludex](https://github.com/kludex)
* Always use `Enum` value as default in generated JSON schema, [#3190](https://github.com/samuelcolvin/pydantic/issues/3190) by [@joaommartins](https://github.com/joaommartins)
* Add support for Mypy 0.920, [#3175](https://github.com/samuelcolvin/pydantic/issues/3175) by [@christianbundy](https://github.com/christianbundy)
* `validate_arguments` now supports `extra` customization (used to always be `Extra.forbid`), [#3161](https://github.com/samuelcolvin/pydantic/issues/3161) by [@PrettyWood](https://github.com/PrettyWood)
* Complex types can be set by nested environment variables, [#3159](https://github.com/samuelcolvin/pydantic/issues/3159) by [@Air-Mark](https://github.com/Air-Mark)
* Fix mypy plugin to collect fields based on `pydantic.utils.is_valid_field` so that it ignores untyped private variables, [#3146](https://github.com/samuelcolvin/pydantic/issues/3146) by [@hi-ogawa](https://github.com/hi-ogawa)
* fix `validate_arguments` issue with `Config.validate_all`, [#3135](https://github.com/samuelcolvin/pydantic/issues/3135) by [@PrettyWood](https://github.com/PrettyWood)
* avoid dict coercion when using dict subclasses as field type, [#3122](https://github.com/samuelcolvin/pydantic/issues/3122) by [@PrettyWood](https://github.com/PrettyWood)
* add support for `object` type, [#3062](https://github.com/samuelcolvin/pydantic/issues/3062) by [@PrettyWood](https://github.com/PrettyWood)
* Updates pydantic dataclasses to keep `_special` properties on parent classes, [#3043](https://github.com/samuelcolvin/pydantic/issues/3043) by [@zulrang](https://github.com/zulrang)
* Add a `TypedDict` class for error objects, [#3038](https://github.com/samuelcolvin/pydantic/issues/3038) by [@matthewhughes934](https://github.com/matthewhughes934)
* Fix support for using a subclass of an annotation as a default, [#3018](https://github.com/samuelcolvin/pydantic/issues/3018) by [@JacobHayes](https://github.com/JacobHayes)
* make `create_model_from_typeddict` mypy compliant, [#3008](https://github.com/samuelcolvin/pydantic/issues/3008) by [@PrettyWood](https://github.com/PrettyWood)
* Make multiple inheritance work when using `PrivateAttr`, [#2989](https://github.com/samuelcolvin/pydantic/issues/2989) by [@hmvp](https://github.com/hmvp)
* Parse environment variables as JSON, if they have a `Union` type with a complex subfield, [#2936](https://github.com/samuelcolvin/pydantic/issues/2936) by [@cbartz](https://github.com/cbartz)
* Prevent `StrictStr` permitting `Enum` values where the enum inherits from `str`, [#2929](https://github.com/samuelcolvin/pydantic/issues/2929) by [@samuelcolvin](https://github.com/samuelcolvin)
* Make `SecretsSettingsSource` parse values being assigned to fields of complex types when sourced from a secrets file, 
  just as when sourced from environment variables, [#2917](https://github.com/samuelcolvin/pydantic/issues/2917) by [@davidmreed](https://github.com/davidmreed)
* add a dark mode to _pydantic_ documentation, [#2913](https://github.com/samuelcolvin/pydantic/issues/2913) by [@gbdlin](https://github.com/gbdlin)
* Make `pydantic-mypy` plugin compatible with `pyproject.toml` configuration, consistent with `mypy` changes. 
  See the [doc](https://pydantic-docs.helpmanual.io/mypy_plugin/#configuring-the-plugin) for more information, [#2908](https://github.com/samuelcolvin/pydantic/issues/2908) by [@jrwalk](https://github.com/jrwalk)
* add python 3.10 support, [#2885](https://github.com/samuelcolvin/pydantic/issues/2885) by [@PrettyWood](https://github.com/PrettyWood)
* Correctly parse generic models with `Json[T]`, [#2860](https://github.com/samuelcolvin/pydantic/issues/2860) by [@geekingfrog](https://github.com/geekingfrog)
* Update contrib docs re: python version to use for building docs, [#2856](https://github.com/samuelcolvin/pydantic/issues/2856) by [@paxcodes](https://github.com/paxcodes)
* Clarify documentation about _pydantic_'s support for custom validation and strict type checking, 
  despite _pydantic_ being primarily a parsing library, [#2855](https://github.com/samuelcolvin/pydantic/issues/2855) by [@paxcodes](https://github.com/paxcodes)
* Fix schema generation for `Deque` fields, [#2810](https://github.com/samuelcolvin/pydantic/issues/2810) by [@sergejkozin](https://github.com/sergejkozin)
* fix an edge case when mixing constraints and `Literal`, [#2794](https://github.com/samuelcolvin/pydantic/issues/2794) by [@PrettyWood](https://github.com/PrettyWood)
* Fix postponed annotation resolution for `NamedTuple` and `TypedDict` when they're used directly as the type of fields 
  within Pydantic models, [#2760](https://github.com/samuelcolvin/pydantic/issues/2760) by [@jameysharp](https://github.com/jameysharp)
* Fix bug when `mypy` plugin fails on `construct` method call for `BaseSettings` derived classes, [#2753](https://github.com/samuelcolvin/pydantic/issues/2753) by [@uriyyo](https://github.com/uriyyo)
* Add function overloading for a `pydantic.create_model` function, [#2748](https://github.com/samuelcolvin/pydantic/issues/2748) by [@uriyyo](https://github.com/uriyyo)
* Fix mypy plugin issue with self field declaration, [#2743](https://github.com/samuelcolvin/pydantic/issues/2743) by [@uriyyo](https://github.com/uriyyo)
* The colon at the end of the line "The fields which were supplied when user was initialised:" suggests that the code following it is related.
  Changed it to a period, [#2733](https://github.com/samuelcolvin/pydantic/issues/2733) by [@krisaoe](https://github.com/krisaoe)
* Renamed variable `schema` to `schema_` to avoid shadowing of global variable name, [#2724](https://github.com/samuelcolvin/pydantic/issues/2724) by [@shahriyarr](https://github.com/shahriyarr)
* Add support for autocomplete in VS Code via `__dataclass_transform__`, [#2721](https://github.com/samuelcolvin/pydantic/issues/2721) by [@tiangolo](https://github.com/tiangolo)
* add missing type annotations in `BaseConfig` and handle `max_length = 0`, [#2719](https://github.com/samuelcolvin/pydantic/issues/2719) by [@PrettyWood](https://github.com/PrettyWood)
* Change `orm_mode` checking to allow recursive ORM mode parsing with dicts, [#2718](https://github.com/samuelcolvin/pydantic/issues/2718) by [@nuno-andre](https://github.com/nuno-andre)
* Add episode 313 of the *Talk Python To Me* podcast, where Michael Kennedy and Samuel Colvin discuss *pydantic*, to the docs, [#2712](https://github.com/samuelcolvin/pydantic/issues/2712) by [@RatulMaharaj](https://github.com/RatulMaharaj)
* fix JSON schema generation when a field is of type `NamedTuple` and has a default value, [#2707](https://github.com/samuelcolvin/pydantic/issues/2707) by [@PrettyWood](https://github.com/PrettyWood)
* `Enum` fields now properly support extra kwargs in schema generation, [#2697](https://github.com/samuelcolvin/pydantic/issues/2697) by [@sammchardy](https://github.com/sammchardy)
* Make serialization of referenced pydantic models possible, [#2650](https://github.com/samuelcolvin/pydantic/issues/2650) by [@PrettyWood](https://github.com/PrettyWood)
* Add `uniqueItems` option to `ConstrainedList`, [#2618](https://github.com/samuelcolvin/pydantic/issues/2618) by [@nuno-andre](https://github.com/nuno-andre)
* Try to evaluate forward refs automatically at model creation, [#2588](https://github.com/samuelcolvin/pydantic/issues/2588) by [@uriyyo](https://github.com/uriyyo)
* Switch docs preview and coverage display to use [smokeshow](https://smokeshow.helpmanual.io/), [#2580](https://github.com/samuelcolvin/pydantic/issues/2580) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add `__version__` attribute to pydantic module, [#2572](https://github.com/samuelcolvin/pydantic/issues/2572) by [@paxcodes](https://github.com/paxcodes)
* Add `postgresql+asyncpg`, `postgresql+pg8000`, `postgresql+psycopg2`, `postgresql+psycopg2cffi`, `postgresql+py-postgresql`
  and `postgresql+pygresql` schemes for `PostgresDsn`, [#2567](https://github.com/samuelcolvin/pydantic/issues/2567) by [@postgres-asyncpg](https://github.com/postgres-asyncpg)
* Enable the Hypothesis plugin to generate a constrained decimal when the `decimal_places` argument is specified, [#2524](https://github.com/samuelcolvin/pydantic/issues/2524) by [@cwe5590](https://github.com/cwe5590)
* Allow `collections.abc.Callable` to be used as type in python 3.9, [#2519](https://github.com/samuelcolvin/pydantic/issues/2519) by [@daviskirk](https://github.com/daviskirk)
* Documentation update how to custom compile pydantic when using pip install, small change in `setup.py` 
  to allow for custom CFLAGS when compiling, [#2517](https://github.com/samuelcolvin/pydantic/issues/2517) by [@peterroelants](https://github.com/peterroelants)
* remove side effect of `default_factory` to run it only once even if `Config.validate_all` is set, [#2515](https://github.com/samuelcolvin/pydantic/issues/2515) by [@PrettyWood](https://github.com/PrettyWood)
* Add lookahead to ip regexes for `AnyUrl` hosts. This allows urls with DNS labels
  looking like IPs to validate as they are perfectly valid host names, [#2512](https://github.com/samuelcolvin/pydantic/issues/2512) by [@sbv-csis](https://github.com/sbv-csis)
* Set `minItems` and `maxItems` in generated JSON schema for fixed-length tuples, [#2497](https://github.com/samuelcolvin/pydantic/issues/2497) by [@PrettyWood](https://github.com/PrettyWood)
* Add `strict` argument to `conbytes`, [#2489](https://github.com/samuelcolvin/pydantic/issues/2489) by [@koxudaxi](https://github.com/koxudaxi)
* Support user defined generic field types in generic models, [#2465](https://github.com/samuelcolvin/pydantic/issues/2465) by [@daviskirk](https://github.com/daviskirk)
* Add an example and a short explanation of subclassing `GetterDict` to docs, [#2463](https://github.com/samuelcolvin/pydantic/issues/2463) by [@nuno-andre](https://github.com/nuno-andre)
* add `KafkaDsn` type, `HttpUrl` now has default port 80 for http and 443 for https, [#2447](https://github.com/samuelcolvin/pydantic/issues/2447) by [@MihanixA](https://github.com/MihanixA)
* Add `PastDate` and `FutureDate` types, [#2425](https://github.com/samuelcolvin/pydantic/issues/2425) by [@Kludex](https://github.com/Kludex)
* Support generating schema for `Generic` fields with subtypes, [#2375](https://github.com/samuelcolvin/pydantic/issues/2375) by [@maximberg](https://github.com/maximberg)
* fix(encoder): serialize `NameEmail` to str, [#2341](https://github.com/samuelcolvin/pydantic/issues/2341) by [@alecgerona](https://github.com/alecgerona)
* add `Config.smart_union` to prevent coercion in `Union` if possible, see 
 [the doc](https://pydantic-docs.helpmanual.io/usage/model_config/#smart-union) for more information, [#2092](https://github.com/samuelcolvin/pydantic/issues/2092) by [@PrettyWood](https://github.com/PrettyWood)
* Add ability to use `typing.Counter` as a model field type, [#2060](https://github.com/samuelcolvin/pydantic/issues/2060) by [@uriyyo](https://github.com/uriyyo)
* Add parameterised subclasses to `__bases__` when constructing new parameterised classes, so that `A <: B => A[int] <: B[int]`, [#2007](https://github.com/samuelcolvin/pydantic/issues/2007) by [@diabolo-dan](https://github.com/diabolo-dan)
* Create `FileUrl` type that allows URLs that conform to [RFC 8089](https://tools.ietf.org/html/rfc8089#section-2).
  Add `host_required` parameter, which is `True` by default (`AnyUrl` and subclasses), `False` in `RedisDsn`, `FileUrl`, [#1983](https://github.com/samuelcolvin/pydantic/issues/1983) by [@vgerak](https://github.com/vgerak)
* add `confrozenset()`, analogous to `conset()` and `conlist()`, [#1897](https://github.com/samuelcolvin/pydantic/issues/1897) by [@PrettyWood](https://github.com/PrettyWood)
* stop calling parent class `root_validator` if overridden, [#1895](https://github.com/samuelcolvin/pydantic/issues/1895) by [@PrettyWood](https://github.com/PrettyWood)
* Add `repr` (defaults to `True`) parameter to `Field`, to hide it from the default representation of the `BaseModel`, [#1831](https://github.com/samuelcolvin/pydantic/issues/1831) by [@fnep](https://github.com/fnep)
* Accept empty query/fragment URL parts, [#1807](https://github.com/samuelcolvin/pydantic/issues/1807) by [@xavier](https://github.com/xavier)

## v1.8.2 (2021-05-11)

!!! warning
    A security vulnerability, level "moderate" is fixed in v1.8.2. Please upgrade **ASAP**.
    See security advisory [CVE-2021-29510](https://github.com/samuelcolvin/pydantic/security/advisories/GHSA-5jqp-qgf6-3pvh)

* **Security fix:** Fix `date` and `datetime` parsing so passing either `'infinity'` or `float('inf')` 
  (or their negative values) does not cause an infinite loop, 
  see security advisory [CVE-2021-29510](https://github.com/samuelcolvin/pydantic/security/advisories/GHSA-5jqp-qgf6-3pvh)
* fix schema generation with Enum by generating a valid name, [#2575](https://github.com/samuelcolvin/pydantic/issues/2575) by [@PrettyWood](https://github.com/PrettyWood)
* fix JSON schema generation with a `Literal` of an enum member, [#2536](https://github.com/samuelcolvin/pydantic/issues/2536) by [@PrettyWood](https://github.com/PrettyWood)
* Fix bug with configurations declarations that are passed as
  keyword arguments during class creation, [#2532](https://github.com/samuelcolvin/pydantic/issues/2532) by [@uriyyo](https://github.com/uriyyo)
* Allow passing `json_encoders` in class kwargs, [#2521](https://github.com/samuelcolvin/pydantic/issues/2521) by [@layday](https://github.com/layday)
* support arbitrary types with custom `__eq__`, [#2483](https://github.com/samuelcolvin/pydantic/issues/2483) by [@PrettyWood](https://github.com/PrettyWood)
* support `Annotated` in `validate_arguments` and in generic models with python 3.9, [#2483](https://github.com/samuelcolvin/pydantic/issues/2483) by [@PrettyWood](https://github.com/PrettyWood)

## v1.8.1 (2021-03-03)

Bug fixes for regressions and new features from `v1.8` 

* allow elements of `Config.field` to update elements of a `Field`, [#2461](https://github.com/samuelcolvin/pydantic/issues/2461) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix validation with a `BaseModel` field and a custom root type, [#2449](https://github.com/samuelcolvin/pydantic/issues/2449) by [@PrettyWood](https://github.com/PrettyWood)
* expose `Pattern` encoder to `fastapi`, [#2444](https://github.com/samuelcolvin/pydantic/issues/2444) by [@PrettyWood](https://github.com/PrettyWood)
* enable the Hypothesis plugin to generate a constrained float when the `multiple_of` argument is specified, [#2442](https://github.com/samuelcolvin/pydantic/issues/2442) by [@tobi-lipede-oodle](https://github.com/tobi-lipede-oodle)
* Avoid `RecursionError` when using some types like `Enum` or `Literal` with generic models, [#2436](https://github.com/samuelcolvin/pydantic/issues/2436) by [@PrettyWood](https://github.com/PrettyWood)
* do not overwrite declared `__hash__` in subclasses of a model, [#2422](https://github.com/samuelcolvin/pydantic/issues/2422) by [@PrettyWood](https://github.com/PrettyWood)
* fix `mypy` complaints on `Path` and `UUID` related custom types, [#2418](https://github.com/samuelcolvin/pydantic/issues/2418) by [@PrettyWood](https://github.com/PrettyWood)
* Support properly variable length tuples of compound types, [#2416](https://github.com/samuelcolvin/pydantic/issues/2416) by [@PrettyWood](https://github.com/PrettyWood)

## v1.8 (2021-02-26)

Thank you to pydantic's sponsors:
@jorgecarleitao, [@BCarley](https://github.com/BCarley), [@chdsbd](https://github.com/chdsbd), [@tiangolo](https://github.com/tiangolo), [@matin](https://github.com/matin), [@linusg](https://github.com/linusg), [@kevinalh](https://github.com/kevinalh), [@koxudaxi](https://github.com/koxudaxi), [@timdrijvers](https://github.com/timdrijvers), [@mkeen](https://github.com/mkeen), [@meadsteve](https://github.com/meadsteve), 
@ginomempin, [@primer-io](https://github.com/primer-io), [@and-semakin](https://github.com/and-semakin), [@tomthorogood](https://github.com/tomthorogood), [@AjitZK](https://github.com/AjitZK), [@westonsteimel](https://github.com/westonsteimel), [@Mazyod](https://github.com/Mazyod), [@christippett](https://github.com/christippett), [@CarlosDomingues](https://github.com/CarlosDomingues), 
@Kludex, [@r-m-n](https://github.com/r-m-n)
for their kind support.

### Highlights

* [Hypothesis plugin](https://pydantic-docs.helpmanual.io/hypothesis_plugin/) for testing, [#2097](https://github.com/samuelcolvin/pydantic/issues/2097) by [@Zac-HD](https://github.com/Zac-HD)
* support for [`NamedTuple` and `TypedDict`](https://pydantic-docs.helpmanual.io/usage/types/#annotated-types), [#2216](https://github.com/samuelcolvin/pydantic/issues/2216) by [@PrettyWood](https://github.com/PrettyWood)
* Support [`Annotated` hints on model fields](https://pydantic-docs.helpmanual.io/usage/schema/#typingannotated-fields), [#2147](https://github.com/samuelcolvin/pydantic/issues/2147) by [@JacobHayes](https://github.com/JacobHayes)
* [`frozen` parameter on `Config`](https://pydantic-docs.helpmanual.io/usage/model_config/) to allow models to be hashed, [#1880](https://github.com/samuelcolvin/pydantic/issues/1880) by [@rhuille](https://github.com/rhuille)

### Changes

* **Breaking Change**, remove old deprecation aliases from v1, [#2415](https://github.com/samuelcolvin/pydantic/issues/2415) by [@samuelcolvin](https://github.com/samuelcolvin):
  * remove notes on migrating to v1 in docs
  * remove `Schema` which was replaced by `Field`
  * remove `Config.case_insensitive` which was replaced by `Config.case_sensitive` (default `False`)
  * remove `Config.allow_population_by_alias` which was replaced by `Config.allow_population_by_field_name`
  * remove `model.fields` which was replaced by `model.__fields__`
  * remove `model.to_string()` which was replaced by `str(model)`
  * remove `model.__values__` which was replaced by `model.__dict__`
* **Breaking Change:** always validate only first sublevel items with `each_item`.
  There were indeed some edge cases with some compound types where the validated items were the last sublevel ones, [#1933](https://github.com/samuelcolvin/pydantic/issues/1933) by [@PrettyWood](https://github.com/PrettyWood)
* Update docs extensions to fix local syntax highlighting, [#2400](https://github.com/samuelcolvin/pydantic/issues/2400) by [@daviskirk](https://github.com/daviskirk)
* fix: allow `utils.lenient_issubclass` to handle `typing.GenericAlias` objects like `list[str]` in python >= 3.9, [#2399](https://github.com/samuelcolvin/pydantic/issues/2399) by [@daviskirk](https://github.com/daviskirk)
* Improve field declaration for _pydantic_ `dataclass` by allowing the usage of _pydantic_ `Field` or `'metadata'` kwarg of `dataclasses.field`, [#2384](https://github.com/samuelcolvin/pydantic/issues/2384) by [@PrettyWood](https://github.com/PrettyWood)
* Making `typing-extensions` a required dependency, [#2368](https://github.com/samuelcolvin/pydantic/issues/2368) by [@samuelcolvin](https://github.com/samuelcolvin)
* Make `resolve_annotations` more lenient, allowing for missing modules, [#2363](https://github.com/samuelcolvin/pydantic/issues/2363) by [@samuelcolvin](https://github.com/samuelcolvin)
* Allow configuring models through class kwargs, [#2356](https://github.com/samuelcolvin/pydantic/issues/2356) by [@Bobronium](https://github.com/Bobronium)
* Prevent `Mapping` subclasses from always being coerced to `dict`, [#2325](https://github.com/samuelcolvin/pydantic/issues/2325) by [@ofek](https://github.com/ofek)
* fix: allow `None` for type `Optional[conset / conlist]`, [#2320](https://github.com/samuelcolvin/pydantic/issues/2320) by [@PrettyWood](https://github.com/PrettyWood)
* Support empty tuple type, [#2318](https://github.com/samuelcolvin/pydantic/issues/2318) by [@PrettyWood](https://github.com/PrettyWood)
* fix: `python_requires` metadata to require >=3.6.1, [#2306](https://github.com/samuelcolvin/pydantic/issues/2306) by [@hukkinj1](https://github.com/hukkinj1)
* Properly encode `Decimal` with, or without any decimal places, [#2293](https://github.com/samuelcolvin/pydantic/issues/2293) by [@hultner](https://github.com/hultner)
* fix: update `__fields_set__` in `BaseModel.copy(update=…)`, [#2290](https://github.com/samuelcolvin/pydantic/issues/2290) by [@PrettyWood](https://github.com/PrettyWood)
* fix: keep order of fields with `BaseModel.construct()`, [#2281](https://github.com/samuelcolvin/pydantic/issues/2281) by [@PrettyWood](https://github.com/PrettyWood)
* Support generating schema for Generic fields, [#2262](https://github.com/samuelcolvin/pydantic/issues/2262) by [@maximberg](https://github.com/maximberg)
* Fix `validate_decorator` so `**kwargs` doesn't exclude values when the keyword
  has the same name as the `*args` or `**kwargs` names, [#2251](https://github.com/samuelcolvin/pydantic/issues/2251) by [@cybojenix](https://github.com/cybojenix)
* Prevent overriding positional arguments with keyword arguments in
  `validate_arguments`, as per behaviour with native functions, [#2249](https://github.com/samuelcolvin/pydantic/issues/2249) by [@cybojenix](https://github.com/cybojenix)
* add documentation for `con*` type functions, [#2242](https://github.com/samuelcolvin/pydantic/issues/2242) by [@tayoogunbiyi](https://github.com/tayoogunbiyi)
* Support custom root type (aka `__root__`) when using `parse_obj()` with nested models, [#2238](https://github.com/samuelcolvin/pydantic/issues/2238) by [@PrettyWood](https://github.com/PrettyWood)
* Support custom root type (aka `__root__`) with `from_orm()`, [#2237](https://github.com/samuelcolvin/pydantic/issues/2237) by [@PrettyWood](https://github.com/PrettyWood)
* ensure cythonized functions are left untouched when creating models, based on [#1944](https://github.com/samuelcolvin/pydantic/issues/1944) by [@kollmats](https://github.com/kollmats), [#2228](https://github.com/samuelcolvin/pydantic/issues/2228) by [@samuelcolvin](https://github.com/samuelcolvin)
* Resolve forward refs for stdlib dataclasses converted into _pydantic_ ones, [#2220](https://github.com/samuelcolvin/pydantic/issues/2220) by [@PrettyWood](https://github.com/PrettyWood)
* Add support for `NamedTuple` and `TypedDict` types.
  Those two types are now handled and validated when used inside `BaseModel` or _pydantic_ `dataclass`.
  Two utils are also added `create_model_from_namedtuple` and `create_model_from_typeddict`, [#2216](https://github.com/samuelcolvin/pydantic/issues/2216) by [@PrettyWood](https://github.com/PrettyWood)
* Do not ignore annotated fields when type is `Union[Type[...], ...]`, [#2213](https://github.com/samuelcolvin/pydantic/issues/2213) by [@PrettyWood](https://github.com/PrettyWood)
* Raise a user-friendly `TypeError` when a `root_validator` does not return a `dict` (e.g. `None`), [#2209](https://github.com/samuelcolvin/pydantic/issues/2209) by [@masalim2](https://github.com/masalim2)
* Add a `FrozenSet[str]` type annotation to the `allowed_schemes` argument on the `strict_url` field type, [#2198](https://github.com/samuelcolvin/pydantic/issues/2198) by [@Midnighter](https://github.com/Midnighter)
* add `allow_mutation` constraint to `Field`, [#2195](https://github.com/samuelcolvin/pydantic/issues/2195) by [@sblack-usu](https://github.com/sblack-usu)
* Allow `Field` with a `default_factory` to be used as an argument to a function
  decorated with `validate_arguments`, [#2176](https://github.com/samuelcolvin/pydantic/issues/2176) by [@thomascobb](https://github.com/thomascobb)
* Allow non-existent secrets directory by only issuing a warning, [#2175](https://github.com/samuelcolvin/pydantic/issues/2175) by [@davidolrik](https://github.com/davidolrik)
* fix URL regex to parse fragment without query string, [#2168](https://github.com/samuelcolvin/pydantic/issues/2168) by [@andrewmwhite](https://github.com/andrewmwhite)
* fix: ensure to always return one of the values in `Literal` field type, [#2166](https://github.com/samuelcolvin/pydantic/issues/2166) by [@PrettyWood](https://github.com/PrettyWood)
* Support `typing.Annotated` hints on model fields. A `Field` may now be set in the type hint with `Annotated[..., Field(...)`; all other annotations are ignored but still visible with `get_type_hints(..., include_extras=True)`, [#2147](https://github.com/samuelcolvin/pydantic/issues/2147) by [@JacobHayes](https://github.com/JacobHayes)
* Added `StrictBytes` type as well as `strict=False` option to `ConstrainedBytes`, [#2136](https://github.com/samuelcolvin/pydantic/issues/2136) by [@rlizzo](https://github.com/rlizzo)
* added `Config.anystr_lower` and `to_lower` kwarg to `constr` and `conbytes`, [#2134](https://github.com/samuelcolvin/pydantic/issues/2134) by [@tayoogunbiyi](https://github.com/tayoogunbiyi)
* Support plain `typing.Tuple` type, [#2132](https://github.com/samuelcolvin/pydantic/issues/2132) by [@PrettyWood](https://github.com/PrettyWood)
* Add a bound method `validate` to functions decorated with `validate_arguments`
  to validate parameters without actually calling the function, [#2127](https://github.com/samuelcolvin/pydantic/issues/2127) by [@PrettyWood](https://github.com/PrettyWood)
* Add the ability to customize settings sources (add / disable / change priority order), [#2107](https://github.com/samuelcolvin/pydantic/issues/2107) by [@kozlek](https://github.com/kozlek)
* Fix mypy complaints about most custom _pydantic_ types, [#2098](https://github.com/samuelcolvin/pydantic/issues/2098) by [@PrettyWood](https://github.com/PrettyWood)
* Add a [Hypothesis](https://hypothesis.readthedocs.io/) plugin for easier [property-based testing](https://increment.com/testing/in-praise-of-property-based-testing/) with Pydantic's custom types - [usage details here](https://pydantic-docs.helpmanual.io/hypothesis_plugin/), [#2097](https://github.com/samuelcolvin/pydantic/issues/2097) by [@Zac-HD](https://github.com/Zac-HD)
* add validator for `None`, `NoneType` or `Literal[None]`, [#2095](https://github.com/samuelcolvin/pydantic/issues/2095) by [@PrettyWood](https://github.com/PrettyWood)
* Handle properly fields of type `Callable` with a default value, [#2094](https://github.com/samuelcolvin/pydantic/issues/2094) by [@PrettyWood](https://github.com/PrettyWood)
* Updated `create_model` return type annotation to return type which inherits from `__base__` argument, [#2071](https://github.com/samuelcolvin/pydantic/issues/2071) by [@uriyyo](https://github.com/uriyyo)
* Add merged `json_encoders` inheritance, [#2064](https://github.com/samuelcolvin/pydantic/issues/2064) by [@art049](https://github.com/art049)
* allow overwriting `ClassVar`s in sub-models without having to re-annotate them, [#2061](https://github.com/samuelcolvin/pydantic/issues/2061) by [@layday](https://github.com/layday)
* add default encoder for `Pattern` type, [#2045](https://github.com/samuelcolvin/pydantic/issues/2045) by [@PrettyWood](https://github.com/PrettyWood)
* Add `NonNegativeInt`, `NonPositiveInt`, `NonNegativeFloat`, `NonPositiveFloat`, [#1975](https://github.com/samuelcolvin/pydantic/issues/1975) by [@mdavis-xyz](https://github.com/mdavis-xyz)
* Use % for percentage in string format of colors, [#1960](https://github.com/samuelcolvin/pydantic/issues/1960) by [@EdwardBetts](https://github.com/EdwardBetts)
* Fixed issue causing `KeyError` to be raised when building schema from multiple `BaseModel` with the same names declared in separate classes, [#1912](https://github.com/samuelcolvin/pydantic/issues/1912) by [@JSextonn](https://github.com/JSextonn)
* Add `rediss` (Redis over SSL) protocol to `RedisDsn`
  Allow URLs without `user` part (e.g., `rediss://:pass@localhost`), [#1911](https://github.com/samuelcolvin/pydantic/issues/1911) by [@TrDex](https://github.com/TrDex)
* Add a new `frozen` boolean parameter to `Config` (default: `False`).
  Setting `frozen=True` does everything that `allow_mutation=False` does, and also generates a `__hash__()` method for the model. This makes instances of the model potentially hashable if all the attributes are hashable, [#1880](https://github.com/samuelcolvin/pydantic/issues/1880) by [@rhuille](https://github.com/rhuille)
* fix schema generation with multiple Enums having the same name, [#1857](https://github.com/samuelcolvin/pydantic/issues/1857) by [@PrettyWood](https://github.com/PrettyWood)
* Added support for 13/19 digits VISA credit cards in `PaymentCardNumber` type, [#1416](https://github.com/samuelcolvin/pydantic/issues/1416) by [@AlexanderSov](https://github.com/AlexanderSov)
* fix: prevent `RecursionError` while using recursive `GenericModel`s, [#1370](https://github.com/samuelcolvin/pydantic/issues/1370) by [@xppt](https://github.com/xppt)
* use `enum` for `typing.Literal` in JSON schema, [#1350](https://github.com/samuelcolvin/pydantic/issues/1350) by [@PrettyWood](https://github.com/PrettyWood)
* Fix: some recursive models did not require `update_forward_refs` and silently behaved incorrectly, [#1201](https://github.com/samuelcolvin/pydantic/issues/1201) by [@PrettyWood](https://github.com/PrettyWood)
* Fix bug where generic models with fields where the typevar is nested in another type `a: List[T]` are considered to be concrete. This allows these models to be subclassed and composed as expected, [#947](https://github.com/samuelcolvin/pydantic/issues/947) by [@daviskirk](https://github.com/daviskirk)
* Add `Config.copy_on_model_validation` flag. When set to `False`, _pydantic_ will keep models used as fields
  untouched on validation instead of reconstructing (copying) them, [#265](https://github.com/samuelcolvin/pydantic/issues/265) by [@PrettyWood](https://github.com/PrettyWood)

## v1.7.4 (2021-05-11)

* **Security fix:** Fix `date` and `datetime` parsing so passing either `'infinity'` or `float('inf')` 
  (or their negative values) does not cause an infinite loop,
  See security advisory [CVE-2021-29510](https://github.com/samuelcolvin/pydantic/security/advisories/GHSA-5jqp-qgf6-3pvh)

## v1.7.3 (2020-11-30)

Thank you to pydantic's sponsors:
@timdrijvers, [@BCarley](https://github.com/BCarley), [@chdsbd](https://github.com/chdsbd), [@tiangolo](https://github.com/tiangolo), [@matin](https://github.com/matin), [@linusg](https://github.com/linusg), [@kevinalh](https://github.com/kevinalh), [@jorgecarleitao](https://github.com/jorgecarleitao), [@koxudaxi](https://github.com/koxudaxi), [@primer-api](https://github.com/primer-api),
@mkeen, [@meadsteve](https://github.com/meadsteve) for their kind support.

* fix: set right default value for required (optional) fields, [#2142](https://github.com/samuelcolvin/pydantic/issues/2142) by [@PrettyWood](https://github.com/PrettyWood)
* fix: support `underscore_attrs_are_private` with generic models, [#2138](https://github.com/samuelcolvin/pydantic/issues/2138) by [@PrettyWood](https://github.com/PrettyWood)
* fix: update all modified field values in `root_validator` when `validate_assignment` is on, [#2116](https://github.com/samuelcolvin/pydantic/issues/2116) by [@PrettyWood](https://github.com/PrettyWood)
* Allow pickling of `pydantic.dataclasses.dataclass` dynamically created from a built-in `dataclasses.dataclass`, [#2111](https://github.com/samuelcolvin/pydantic/issues/2111) by [@aimestereo](https://github.com/aimestereo)
* Fix a regression where Enum fields would not propagate keyword arguments to the schema, [#2109](https://github.com/samuelcolvin/pydantic/issues/2109) by [@bm424](https://github.com/bm424)
* Ignore `__doc__` as private attribute when `Config.underscore_attrs_are_private` is set, [#2090](https://github.com/samuelcolvin/pydantic/issues/2090) by [@PrettyWood](https://github.com/PrettyWood)

## v1.7.2 (2020-11-01)

* fix slow `GenericModel` concrete model creation, allow `GenericModel` concrete name reusing in module, [#2078](https://github.com/samuelcolvin/pydantic/issues/2078) by [@Bobronium](https://github.com/Bobronium)
* keep the order of the fields when `validate_assignment` is set, [#2073](https://github.com/samuelcolvin/pydantic/issues/2073) by [@PrettyWood](https://github.com/PrettyWood)
* forward all the params of the stdlib `dataclass` when converted into _pydantic_ `dataclass`, [#2065](https://github.com/samuelcolvin/pydantic/issues/2065) by [@PrettyWood](https://github.com/PrettyWood)

## v1.7.1 (2020-10-28)

Thank you to pydantic's sponsors:
@timdrijvers, [@BCarley](https://github.com/BCarley), [@chdsbd](https://github.com/chdsbd), [@tiangolo](https://github.com/tiangolo), [@matin](https://github.com/matin), [@linusg](https://github.com/linusg), [@kevinalh](https://github.com/kevinalh), [@jorgecarleitao](https://github.com/jorgecarleitao), [@koxudaxi](https://github.com/koxudaxi), [@primer-api](https://github.com/primer-api), [@mkeen](https://github.com/mkeen)
for their kind support.

* fix annotation of `validate_arguments` when passing configuration as argument, [#2055](https://github.com/samuelcolvin/pydantic/issues/2055) by [@layday](https://github.com/layday)
* Fix mypy assignment error when using `PrivateAttr`, [#2048](https://github.com/samuelcolvin/pydantic/issues/2048) by [@aphedges](https://github.com/aphedges)
* fix `underscore_attrs_are_private` causing `TypeError` when overriding `__init__`, [#2047](https://github.com/samuelcolvin/pydantic/issues/2047) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fixed regression introduced in v1.7 involving exception handling in field validators when `validate_assignment=True`, [#2044](https://github.com/samuelcolvin/pydantic/issues/2044) by [@johnsabath](https://github.com/johnsabath)
* fix: _pydantic_ `dataclass` can inherit from stdlib `dataclass`
  and `Config.arbitrary_types_allowed` is supported, [#2042](https://github.com/samuelcolvin/pydantic/issues/2042) by [@PrettyWood](https://github.com/PrettyWood)

## v1.7 (2020-10-26)

Thank you to pydantic's sponsors:
@timdrijvers, [@BCarley](https://github.com/BCarley), [@chdsbd](https://github.com/chdsbd), [@tiangolo](https://github.com/tiangolo), [@matin](https://github.com/matin), [@linusg](https://github.com/linusg), [@kevinalh](https://github.com/kevinalh), [@jorgecarleitao](https://github.com/jorgecarleitao), [@koxudaxi](https://github.com/koxudaxi), [@primer-api](https://github.com/primer-api) 
for their kind support.

### Highlights

* python 3.9 support, thanks [@PrettyWood](https://github.com/PrettyWood)
* [Private model attributes](https://pydantic-docs.helpmanual.io/usage/models/#private-model-attributes), thanks [@Bobronium](https://github.com/Bobronium)
* ["secrets files" support in `BaseSettings`](https://pydantic-docs.helpmanual.io/usage/settings/#secret-support), thanks [@mdgilene](https://github.com/mdgilene)
* [convert stdlib dataclasses to pydantic dataclasses and use stdlib dataclasses in models](https://pydantic-docs.helpmanual.io/usage/dataclasses/#stdlib-dataclasses-and-pydantic-dataclasses), thanks [@PrettyWood](https://github.com/PrettyWood)

### Changes

* **Breaking Change:** remove `__field_defaults__`, add `default_factory` support with `BaseModel.construct`.
  Use `.get_default()` method on fields in `__fields__` attribute instead, [#1732](https://github.com/samuelcolvin/pydantic/issues/1732) by [@PrettyWood](https://github.com/PrettyWood)
* Rearrange CI to run linting as a separate job, split install recipes for different tasks, [#2020](https://github.com/samuelcolvin/pydantic/issues/2020) by [@samuelcolvin](https://github.com/samuelcolvin)
* Allows subclasses of generic models to make some, or all, of the superclass's type parameters concrete, while 
  also defining new type parameters in the subclass, [#2005](https://github.com/samuelcolvin/pydantic/issues/2005) by [@choogeboom](https://github.com/choogeboom)
* Call validator with the correct `values` parameter type in `BaseModel.__setattr__`,
  when `validate_assignment = True` in model config, [#1999](https://github.com/samuelcolvin/pydantic/issues/1999) by [@me-ransh](https://github.com/me-ransh)
* Force `fields.Undefined` to be a singleton object, fixing inherited generic model schemas, [#1981](https://github.com/samuelcolvin/pydantic/issues/1981) by [@daviskirk](https://github.com/daviskirk)
* Include tests in source distributions, [#1976](https://github.com/samuelcolvin/pydantic/issues/1976) by [@sbraz](https://github.com/sbraz)
* Add ability to use `min_length/max_length` constraints with secret types, [#1974](https://github.com/samuelcolvin/pydantic/issues/1974) by [@uriyyo](https://github.com/uriyyo)
* Also check `root_validators` when `validate_assignment` is on, [#1971](https://github.com/samuelcolvin/pydantic/issues/1971) by [@PrettyWood](https://github.com/PrettyWood)
* Fix const validators not running when custom validators are present, [#1957](https://github.com/samuelcolvin/pydantic/issues/1957) by [@hmvp](https://github.com/hmvp)
* add `deque` to field types, [#1935](https://github.com/samuelcolvin/pydantic/issues/1935) by [@wozniakty](https://github.com/wozniakty)
* add basic support for python 3.9, [#1832](https://github.com/samuelcolvin/pydantic/issues/1832) by [@PrettyWood](https://github.com/PrettyWood)
* Fix typo in the anchor of exporting_models.md#modelcopy and incorrect description, [#1821](https://github.com/samuelcolvin/pydantic/issues/1821) by [@KimMachineGun](https://github.com/KimMachineGun)
* Added ability for `BaseSettings` to read "secret files", [#1820](https://github.com/samuelcolvin/pydantic/issues/1820) by [@mdgilene](https://github.com/mdgilene)
* add `parse_raw_as` utility function, [#1812](https://github.com/samuelcolvin/pydantic/issues/1812) by [@PrettyWood](https://github.com/PrettyWood)
* Support home directory relative paths for `dotenv` files (e.g. `~/.env`), [#1803](https://github.com/samuelcolvin/pydantic/issues/1803) by [@PrettyWood](https://github.com/PrettyWood)
* Clarify documentation for `parse_file` to show that the argument
  should be a file *path* not a file-like object, [#1794](https://github.com/samuelcolvin/pydantic/issues/1794) by [@mdavis-xyz](https://github.com/mdavis-xyz)
* Fix false positive from mypy plugin when a class nested within a `BaseModel` is named `Model`, [#1770](https://github.com/samuelcolvin/pydantic/issues/1770) by [@selimb](https://github.com/selimb)
* add basic support of Pattern type in schema generation, [#1767](https://github.com/samuelcolvin/pydantic/issues/1767) by [@PrettyWood](https://github.com/PrettyWood)
* Support custom title, description and default in schema of enums, [#1748](https://github.com/samuelcolvin/pydantic/issues/1748) by [@PrettyWood](https://github.com/PrettyWood)
* Properly represent `Literal` Enums when `use_enum_values` is True, [#1747](https://github.com/samuelcolvin/pydantic/issues/1747) by [@noelevans](https://github.com/noelevans)
* Allows timezone information to be added to strings to be formatted as time objects. Permitted formats are `Z` for UTC 
  or an offset for absolute positive or negative time shifts. Or the timezone data can be omitted, [#1744](https://github.com/samuelcolvin/pydantic/issues/1744) by [@noelevans](https://github.com/noelevans)
* Add stub `__init__` with python 3.6 signature for `ForwardRef`, [#1738](https://github.com/samuelcolvin/pydantic/issues/1738) by [@sirtelemak](https://github.com/sirtelemak)
* Fix behaviour with forward refs and optional fields in nested models, [#1736](https://github.com/samuelcolvin/pydantic/issues/1736) by [@PrettyWood](https://github.com/PrettyWood)
* add `Enum` and `IntEnum` as valid types for fields, [#1735](https://github.com/samuelcolvin/pydantic/issues/1735) by [@PrettyWood](https://github.com/PrettyWood)
* Change default value of `__module__` argument of `create_model` from `None` to `'pydantic.main'`. 
  Set reference of created concrete model to it's module to allow pickling (not applied to models created in 
  functions), [#1686](https://github.com/samuelcolvin/pydantic/issues/1686) by [@Bobronium](https://github.com/Bobronium)
* Add private attributes support, [#1679](https://github.com/samuelcolvin/pydantic/issues/1679) by [@Bobronium](https://github.com/Bobronium)
* add `config` to `@validate_arguments`, [#1663](https://github.com/samuelcolvin/pydantic/issues/1663) by [@samuelcolvin](https://github.com/samuelcolvin)
* Allow descendant Settings models to override env variable names for the fields defined in parent Settings models with 
  `env` in their `Config`. Previously only `env_prefix` configuration option was applicable, [#1561](https://github.com/samuelcolvin/pydantic/issues/1561) by [@ojomio](https://github.com/ojomio)
* Support `ref_template` when creating schema `$ref`s, [#1479](https://github.com/samuelcolvin/pydantic/issues/1479) by [@kilo59](https://github.com/kilo59)
* Add a `__call__` stub to `PyObject` so that mypy will know that it is callable, [#1352](https://github.com/samuelcolvin/pydantic/issues/1352) by [@brianmaissy](https://github.com/brianmaissy)
* `pydantic.dataclasses.dataclass` decorator now supports built-in `dataclasses.dataclass`.
  It is hence possible to convert an existing `dataclass` easily to add *pydantic* validation.
  Moreover nested dataclasses are also supported, [#744](https://github.com/samuelcolvin/pydantic/issues/744) by [@PrettyWood](https://github.com/PrettyWood)

## v1.6.2 (2021-05-11)

* **Security fix:** Fix `date` and `datetime` parsing so passing either `'infinity'` or `float('inf')` 
  (or their negative values) does not cause an infinite loop,
  See security advisory [CVE-2021-29510](https://github.com/samuelcolvin/pydantic/security/advisories/GHSA-5jqp-qgf6-3pvh)

## v1.6.1 (2020-07-15)

* fix validation and parsing of nested models with `default_factory`, [#1710](https://github.com/samuelcolvin/pydantic/issues/1710) by [@PrettyWood](https://github.com/PrettyWood)

## v1.6 (2020-07-11)

Thank you to pydantic's sponsors: [@matin](https://github.com/matin), [@tiangolo](https://github.com/tiangolo), [@chdsbd](https://github.com/chdsbd), [@jorgecarleitao](https://github.com/jorgecarleitao), and 1 anonymous sponsor for their kind support.

* Modify validators for `conlist` and `conset` to not have `always=True`, [#1682](https://github.com/samuelcolvin/pydantic/issues/1682) by [@samuelcolvin](https://github.com/samuelcolvin)
* add port check to `AnyUrl` (can't exceed 65536) ports are 16 insigned bits: `0 <= port <= 2**16-1` src: [rfc793 header format](https://tools.ietf.org/html/rfc793#section-3.1), [#1654](https://github.com/samuelcolvin/pydantic/issues/1654) by [@flapili](https://github.com/flapili)
* Document default `regex` anchoring semantics, [#1648](https://github.com/samuelcolvin/pydantic/issues/1648) by [@yurikhan](https://github.com/yurikhan)
* Use `chain.from_iterable` in class_validators.py. This is a faster and more idiomatic way of using `itertools.chain`.
  Instead of computing all the items in the iterable and storing them in memory, they are computed one-by-one and never
  stored as a huge list. This can save on both runtime and memory space, [#1642](https://github.com/samuelcolvin/pydantic/issues/1642) by [@cool-RR](https://github.com/cool-RR)
* Add `conset()`, analogous to `conlist()`, [#1623](https://github.com/samuelcolvin/pydantic/issues/1623) by [@patrickkwang](https://github.com/patrickkwang)
* make *pydantic* errors (un)pickable, [#1616](https://github.com/samuelcolvin/pydantic/issues/1616) by [@PrettyWood](https://github.com/PrettyWood)
* Allow custom encoding for `dotenv` files, [#1615](https://github.com/samuelcolvin/pydantic/issues/1615) by [@PrettyWood](https://github.com/PrettyWood)
* Ensure `SchemaExtraCallable` is always defined to get type hints on BaseConfig, [#1614](https://github.com/samuelcolvin/pydantic/issues/1614) by [@PrettyWood](https://github.com/PrettyWood)
* Update datetime parser to support negative timestamps, [#1600](https://github.com/samuelcolvin/pydantic/issues/1600) by [@mlbiche](https://github.com/mlbiche)
* Update mypy, remove `AnyType` alias for `Type[Any]`, [#1598](https://github.com/samuelcolvin/pydantic/issues/1598) by [@samuelcolvin](https://github.com/samuelcolvin)
* Adjust handling of root validators so that errors are aggregated from _all_ failing root validators, instead of reporting on only the first root validator to fail, [#1586](https://github.com/samuelcolvin/pydantic/issues/1586) by [@beezee](https://github.com/beezee)
* Make `__modify_schema__` on Enums apply to the enum schema rather than fields that use the enum, [#1581](https://github.com/samuelcolvin/pydantic/issues/1581) by [@therefromhere](https://github.com/therefromhere)
* Fix behavior of `__all__` key when used in conjunction with index keys in advanced include/exclude of fields that are sequences, [#1579](https://github.com/samuelcolvin/pydantic/issues/1579) by [@xspirus](https://github.com/xspirus)
* Subclass validators do not run when referencing a `List` field defined in a parent class when `each_item=True`. Added an example to the docs illustrating this, [#1566](https://github.com/samuelcolvin/pydantic/issues/1566) by [@samueldeklund](https://github.com/samueldeklund)
* change `schema.field_class_to_schema` to support `frozenset` in schema, [#1557](https://github.com/samuelcolvin/pydantic/issues/1557) by [@wangpeibao](https://github.com/wangpeibao)
* Call `__modify_schema__` only for the field schema, [#1552](https://github.com/samuelcolvin/pydantic/issues/1552) by [@PrettyWood](https://github.com/PrettyWood)
* Move the assignment of `field.validate_always` in `fields.py` so the `always` parameter of validators work on inheritance, [#1545](https://github.com/samuelcolvin/pydantic/issues/1545) by [@dcHHH](https://github.com/dcHHH)
* Added support for UUID instantiation through 16 byte strings such as `b'\x12\x34\x56\x78' * 4`. This was done to support `BINARY(16)` columns in sqlalchemy, [#1541](https://github.com/samuelcolvin/pydantic/issues/1541) by [@shawnwall](https://github.com/shawnwall)
* Add a test assertion that `default_factory` can return a singleton, [#1523](https://github.com/samuelcolvin/pydantic/issues/1523) by [@therefromhere](https://github.com/therefromhere)
* Add `NameEmail.__eq__` so duplicate `NameEmail` instances are evaluated as equal, [#1514](https://github.com/samuelcolvin/pydantic/issues/1514) by [@stephen-bunn](https://github.com/stephen-bunn)
* Add datamodel-code-generator link in pydantic document site, [#1500](https://github.com/samuelcolvin/pydantic/issues/1500) by [@koxudaxi](https://github.com/koxudaxi)
* Added a "Discussion of Pydantic" section to the documentation, with a link to "Pydantic Introduction" video by Alexander Hultnér, [#1499](https://github.com/samuelcolvin/pydantic/issues/1499) by [@hultner](https://github.com/hultner)
* Avoid some side effects of `default_factory` by calling it only once
  if possible and by not setting a default value in the schema, [#1491](https://github.com/samuelcolvin/pydantic/issues/1491) by [@PrettyWood](https://github.com/PrettyWood)
* Added docs about dumping dataclasses to JSON, [#1487](https://github.com/samuelcolvin/pydantic/issues/1487) by [@mikegrima](https://github.com/mikegrima)
* Make `BaseModel.__signature__` class-only, so getting `__signature__` from model instance will raise `AttributeError`, [#1466](https://github.com/samuelcolvin/pydantic/issues/1466) by [@Bobronium](https://github.com/Bobronium)
* include `'format': 'password'` in the schema for secret types, [#1424](https://github.com/samuelcolvin/pydantic/issues/1424) by [@atheuz](https://github.com/atheuz)
* Modify schema constraints on `ConstrainedFloat` so that `exclusiveMinimum` and
  minimum are not included in the schema if they are equal to `-math.inf` and
  `exclusiveMaximum` and `maximum` are not included if they are equal to `math.inf`, [#1417](https://github.com/samuelcolvin/pydantic/issues/1417) by [@vdwees](https://github.com/vdwees)
* Squash internal `__root__` dicts in `.dict()` (and, by extension, in `.json()`), [#1414](https://github.com/samuelcolvin/pydantic/issues/1414) by [@patrickkwang](https://github.com/patrickkwang)
* Move `const` validator to post-validators so it validates the parsed value, [#1410](https://github.com/samuelcolvin/pydantic/issues/1410) by [@selimb](https://github.com/selimb)
* Fix model validation to handle nested literals, e.g. `Literal['foo', Literal['bar']]`, [#1364](https://github.com/samuelcolvin/pydantic/issues/1364) by [@DBCerigo](https://github.com/DBCerigo)
* Remove `user_required = True` from `RedisDsn`, neither user nor password are required, [#1275](https://github.com/samuelcolvin/pydantic/issues/1275) by [@samuelcolvin](https://github.com/samuelcolvin)
* Remove extra `allOf` from schema for fields with `Union` and custom `Field`, [#1209](https://github.com/samuelcolvin/pydantic/issues/1209) by [@mostaphaRoudsari](https://github.com/mostaphaRoudsari)
* Updates OpenAPI schema generation to output all enums as separate models.
  Instead of inlining the enum values in the model schema, models now use a `$ref`
  property to point to the enum definition, [#1173](https://github.com/samuelcolvin/pydantic/issues/1173) by [@calvinwyoung](https://github.com/calvinwyoung)

## v1.5.1 (2020-04-23)

* Signature generation with `extra: allow` never uses a field name, [#1418](https://github.com/samuelcolvin/pydantic/issues/1418) by [@prettywood](https://github.com/prettywood)
* Avoid mutating `Field` default value, [#1412](https://github.com/samuelcolvin/pydantic/issues/1412) by [@prettywood](https://github.com/prettywood)

## v1.5 (2020-04-18)

* Make includes/excludes arguments for `.dict()`, `._iter()`, ..., immutable, [#1404](https://github.com/samuelcolvin/pydantic/issues/1404) by [@AlexECX](https://github.com/AlexECX)
* Always use a field's real name with includes/excludes in `model._iter()`, regardless of `by_alias`, [#1397](https://github.com/samuelcolvin/pydantic/issues/1397) by [@AlexECX](https://github.com/AlexECX)
* Update constr regex example to include start and end lines, [#1396](https://github.com/samuelcolvin/pydantic/issues/1396) by [@lmcnearney](https://github.com/lmcnearney)
* Confirm that shallow `model.copy()` does make a shallow copy of attributes, [#1383](https://github.com/samuelcolvin/pydantic/issues/1383) by [@samuelcolvin](https://github.com/samuelcolvin)
* Renaming `model_name` argument of `main.create_model()` to `__model_name` to allow using `model_name` as a field name, [#1367](https://github.com/samuelcolvin/pydantic/issues/1367) by [@kittipatv](https://github.com/kittipatv)
* Replace raising of exception to silent passing  for non-Var attributes in mypy plugin, [#1345](https://github.com/samuelcolvin/pydantic/issues/1345) by [@b0g3r](https://github.com/b0g3r)
* Remove `typing_extensions` dependency for python 3.8, [#1342](https://github.com/samuelcolvin/pydantic/issues/1342) by [@prettywood](https://github.com/prettywood)
* Make `SecretStr` and `SecretBytes` initialization idempotent, [#1330](https://github.com/samuelcolvin/pydantic/issues/1330) by [@atheuz](https://github.com/atheuz)
* document making secret types dumpable using the json method, [#1328](https://github.com/samuelcolvin/pydantic/issues/1328) by [@atheuz](https://github.com/atheuz)
* Move all testing and build to github actions, add windows and macos binaries, 
  thank you [@StephenBrown2](https://github.com/StephenBrown2) for much help, [#1326](https://github.com/samuelcolvin/pydantic/issues/1326) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix card number length check in `PaymentCardNumber`, `PaymentCardBrand` now inherits from `str`, [#1317](https://github.com/samuelcolvin/pydantic/issues/1317) by [@samuelcolvin](https://github.com/samuelcolvin)
* Have `BaseModel` inherit from `Representation` to make mypy happy when overriding `__str__`, [#1310](https://github.com/samuelcolvin/pydantic/issues/1310) by [@FuegoFro](https://github.com/FuegoFro)
* Allow `None` as input to all optional list fields, [#1307](https://github.com/samuelcolvin/pydantic/issues/1307) by [@prettywood](https://github.com/prettywood)
* Add `datetime` field to `default_factory` example, [#1301](https://github.com/samuelcolvin/pydantic/issues/1301) by [@StephenBrown2](https://github.com/StephenBrown2)
* Allow subclasses of known types to be encoded with superclass encoder, [#1291](https://github.com/samuelcolvin/pydantic/issues/1291) by [@StephenBrown2](https://github.com/StephenBrown2)
* Exclude exported fields from all elements of a list/tuple of submodels/dicts with `'__all__'`, [#1286](https://github.com/samuelcolvin/pydantic/issues/1286) by [@masalim2](https://github.com/masalim2)
* Add pydantic.color.Color objects as available input for Color fields, [#1258](https://github.com/samuelcolvin/pydantic/issues/1258) by [@leosussan](https://github.com/leosussan)
* In examples, type nullable fields as `Optional`, so that these are valid mypy annotations, [#1248](https://github.com/samuelcolvin/pydantic/issues/1248) by [@kokes](https://github.com/kokes)
* Make `pattern_validator()` accept pre-compiled `Pattern` objects. Fix `str_validator()` return type to `str`, [#1237](https://github.com/samuelcolvin/pydantic/issues/1237) by [@adamgreg](https://github.com/adamgreg)
* Document how to manage Generics and inheritance, [#1229](https://github.com/samuelcolvin/pydantic/issues/1229) by [@esadruhn](https://github.com/esadruhn)
* `update_forward_refs()` method of BaseModel now copies `__dict__` of class module instead of modyfying it, [#1228](https://github.com/samuelcolvin/pydantic/issues/1228) by [@paul-ilyin](https://github.com/paul-ilyin)
* Support instance methods and class methods with `@validate_arguments`, [#1222](https://github.com/samuelcolvin/pydantic/issues/1222) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add `default_factory` argument to `Field` to create a dynamic default value by passing a zero-argument callable, [#1210](https://github.com/samuelcolvin/pydantic/issues/1210) by [@prettywood](https://github.com/prettywood)
* add support for `NewType` of `List`, `Optional`, etc, [#1207](https://github.com/samuelcolvin/pydantic/issues/1207) by [@Kazy](https://github.com/Kazy)
* fix mypy signature for `root_validator`, [#1192](https://github.com/samuelcolvin/pydantic/issues/1192) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fixed parsing of nested 'custom root type' models, [#1190](https://github.com/samuelcolvin/pydantic/issues/1190) by [@Shados](https://github.com/Shados)
* Add `validate_arguments` function decorator which checks the arguments to a function matches type annotations, [#1179](https://github.com/samuelcolvin/pydantic/issues/1179) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add `__signature__` to models, [#1034](https://github.com/samuelcolvin/pydantic/issues/1034) by [@Bobronium](https://github.com/Bobronium)
* Refactor `._iter()` method, 10x speed boost for `dict(model)`, [#1017](https://github.com/samuelcolvin/pydantic/issues/1017) by [@Bobronium](https://github.com/Bobronium)

## v1.4 (2020-01-24)

* **Breaking Change:** alias precedence logic changed so aliases on a field always take priority over
  an alias from `alias_generator` to avoid buggy/unexpected behaviour,
  see [here](https://pydantic-docs.helpmanual.io/usage/model_config/#alias-precedence) for details, [#1178](https://github.com/samuelcolvin/pydantic/issues/1178) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add support for unicode and punycode in TLDs, [#1182](https://github.com/samuelcolvin/pydantic/issues/1182) by [@jamescurtin](https://github.com/jamescurtin)
* Fix `cls` argument in validators during assignment, [#1172](https://github.com/samuelcolvin/pydantic/issues/1172) by [@samuelcolvin](https://github.com/samuelcolvin)
* completing Luhn algorithm for `PaymentCardNumber`, [#1166](https://github.com/samuelcolvin/pydantic/issues/1166) by [@cuencandres](https://github.com/cuencandres)
* add support for generics that implement `__get_validators__` like a custom data type, [#1159](https://github.com/samuelcolvin/pydantic/issues/1159) by [@tiangolo](https://github.com/tiangolo)
* add support for infinite generators with `Iterable`, [#1152](https://github.com/samuelcolvin/pydantic/issues/1152) by [@tiangolo](https://github.com/tiangolo)
* fix `url_regex` to accept schemas with `+`, `-` and `.` after the first character, [#1142](https://github.com/samuelcolvin/pydantic/issues/1142) by [@samuelcolvin](https://github.com/samuelcolvin)
* move `version_info()` to `version.py`, suggest its use in issues, [#1138](https://github.com/samuelcolvin/pydantic/issues/1138) by [@samuelcolvin](https://github.com/samuelcolvin)
* Improve pydantic import time by roughly 50% by deferring some module loading and regex compilation, [#1127](https://github.com/samuelcolvin/pydantic/issues/1127) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix `EmailStr` and `NameEmail` to accept instances of themselves in cython, [#1126](https://github.com/samuelcolvin/pydantic/issues/1126) by [@koxudaxi](https://github.com/koxudaxi)
* Pass model class to the `Config.schema_extra` callable, [#1125](https://github.com/samuelcolvin/pydantic/issues/1125) by [@therefromhere](https://github.com/therefromhere)
* Fix regex for username and password in URLs, [#1115](https://github.com/samuelcolvin/pydantic/issues/1115) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add support for nested generic models, [#1104](https://github.com/samuelcolvin/pydantic/issues/1104) by [@dmontagu](https://github.com/dmontagu)
* add `__all__` to `__init__.py` to prevent "implicit reexport" errors from mypy, [#1072](https://github.com/samuelcolvin/pydantic/issues/1072) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add support for using "dotenv" files with `BaseSettings`, [#1011](https://github.com/samuelcolvin/pydantic/issues/1011) by [@acnebs](https://github.com/acnebs)

## v1.3 (2019-12-21)

* Change `schema` and `schema_model` to handle dataclasses by using their `__pydantic_model__` feature, [#792](https://github.com/samuelcolvin/pydantic/issues/792) by [@aviramha](https://github.com/aviramha)
* Added option for `root_validator` to be skipped if values validation fails using keyword `skip_on_failure=True`, [#1049](https://github.com/samuelcolvin/pydantic/issues/1049) by [@aviramha](https://github.com/aviramha)
* Allow `Config.schema_extra` to be a callable so that the generated schema can be post-processed, [#1054](https://github.com/samuelcolvin/pydantic/issues/1054) by [@selimb](https://github.com/selimb)
* Update mypy to version 0.750, [#1057](https://github.com/samuelcolvin/pydantic/issues/1057) by [@dmontagu](https://github.com/dmontagu)
* Trick Cython into allowing str subclassing, [#1061](https://github.com/samuelcolvin/pydantic/issues/1061) by [@skewty](https://github.com/skewty)
* Prevent type attributes being added to schema unless the attribute `__schema_attributes__` is `True`, [#1064](https://github.com/samuelcolvin/pydantic/issues/1064) by [@samuelcolvin](https://github.com/samuelcolvin)
* Change `BaseModel.parse_file` to use `Config.json_loads`, [#1067](https://github.com/samuelcolvin/pydantic/issues/1067) by [@kierandarcy](https://github.com/kierandarcy)
* Fix for optional `Json` fields, [#1073](https://github.com/samuelcolvin/pydantic/issues/1073) by [@volker48](https://github.com/volker48)
* Change the default number of threads used when compiling with cython to one,
  allow override via the `CYTHON_NTHREADS` environment variable, [#1074](https://github.com/samuelcolvin/pydantic/issues/1074) by [@samuelcolvin](https://github.com/samuelcolvin)
* Run FastAPI tests during Pydantic's CI tests, [#1075](https://github.com/samuelcolvin/pydantic/issues/1075) by [@tiangolo](https://github.com/tiangolo)
* My mypy strictness constraints, and associated tweaks to type annotations, [#1077](https://github.com/samuelcolvin/pydantic/issues/1077) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add `__eq__` to SecretStr and SecretBytes to allow "value equals", [#1079](https://github.com/samuelcolvin/pydantic/issues/1079) by [@sbv-trueenergy](https://github.com/sbv-trueenergy)
* Fix schema generation for nested None case, [#1088](https://github.com/samuelcolvin/pydantic/issues/1088) by [@lutostag](https://github.com/lutostag)
* Consistent checks for sequence like objects, [#1090](https://github.com/samuelcolvin/pydantic/issues/1090) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix `Config` inheritance on `BaseSettings` when used with `env_prefix`, [#1091](https://github.com/samuelcolvin/pydantic/issues/1091) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix for `__modify_schema__` when it conflicted with `field_class_to_schema*`, [#1102](https://github.com/samuelcolvin/pydantic/issues/1102) by [@samuelcolvin](https://github.com/samuelcolvin)
* docs: Fix explanation of case sensitive environment variable names when populating `BaseSettings` subclass attributes, [#1105](https://github.com/samuelcolvin/pydantic/issues/1105) by [@tribals](https://github.com/tribals)
* Rename django-rest-framework benchmark in documentation, [#1119](https://github.com/samuelcolvin/pydantic/issues/1119) by [@frankie567](https://github.com/frankie567)

## v1.2 (2019-11-28)

* **Possible Breaking Change:** Add support for required `Optional` with `name: Optional[AnyType] = Field(...)`
  and refactor `ModelField` creation to preserve `required` parameter value, [#1031](https://github.com/samuelcolvin/pydantic/issues/1031) by [@tiangolo](https://github.com/tiangolo);
  see [here](https://pydantic-docs.helpmanual.io/usage/models/#required-optional-fields) for details
* Add benchmarks for `cattrs`, [#513](https://github.com/samuelcolvin/pydantic/issues/513) by [@sebastianmika](https://github.com/sebastianmika)
* Add `exclude_none` option to `dict()` and friends, [#587](https://github.com/samuelcolvin/pydantic/issues/587) by [@niknetniko](https://github.com/niknetniko)
* Add benchmarks for `valideer`, [#670](https://github.com/samuelcolvin/pydantic/issues/670) by [@gsakkis](https://github.com/gsakkis)
* Add `parse_obj_as` and `parse_file_as` functions for ad-hoc parsing of data into arbitrary pydantic-compatible types, [#934](https://github.com/samuelcolvin/pydantic/issues/934) by [@dmontagu](https://github.com/dmontagu)
* Add `allow_reuse` argument to validators, thus allowing validator reuse, [#940](https://github.com/samuelcolvin/pydantic/issues/940) by [@dmontagu](https://github.com/dmontagu)
* Add support for mapping types for custom root models, [#958](https://github.com/samuelcolvin/pydantic/issues/958) by [@dmontagu](https://github.com/dmontagu)
* Mypy plugin support for dataclasses, [#966](https://github.com/samuelcolvin/pydantic/issues/966) by [@koxudaxi](https://github.com/koxudaxi)
* Add support for dataclasses default factory, [#968](https://github.com/samuelcolvin/pydantic/issues/968) by [@ahirner](https://github.com/ahirner)
* Add a `ByteSize` type for converting byte string (`1GB`) to plain bytes, [#977](https://github.com/samuelcolvin/pydantic/issues/977) by [@dgasmith](https://github.com/dgasmith)
* Fix mypy complaint about `@root_validator(pre=True)`, [#984](https://github.com/samuelcolvin/pydantic/issues/984) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add manylinux binaries for python 3.8 to pypi, also support manylinux2010, [#994](https://github.com/samuelcolvin/pydantic/issues/994) by [@samuelcolvin](https://github.com/samuelcolvin)
* Adds ByteSize conversion to another unit, [#995](https://github.com/samuelcolvin/pydantic/issues/995) by [@dgasmith](https://github.com/dgasmith)
* Fix `__str__` and `__repr__` inheritance for models, [#1022](https://github.com/samuelcolvin/pydantic/issues/1022) by [@samuelcolvin](https://github.com/samuelcolvin)
* add testimonials section to docs, [#1025](https://github.com/samuelcolvin/pydantic/issues/1025) by [@sullivancolin](https://github.com/sullivancolin)
* Add support for `typing.Literal` for Python 3.8, [#1026](https://github.com/samuelcolvin/pydantic/issues/1026) by [@dmontagu](https://github.com/dmontagu)

## v1.1.1 (2019-11-20)

* Fix bug where use of complex fields on sub-models could cause fields to be incorrectly configured, [#1015](https://github.com/samuelcolvin/pydantic/issues/1015) by [@samuelcolvin](https://github.com/samuelcolvin)

## v1.1 (2019-11-07)

* Add a mypy plugin for type checking `BaseModel.__init__` and more, [#722](https://github.com/samuelcolvin/pydantic/issues/722) by [@dmontagu](https://github.com/dmontagu)
* Change return type typehint for `GenericModel.__class_getitem__` to prevent PyCharm warnings, [#936](https://github.com/samuelcolvin/pydantic/issues/936) by [@dmontagu](https://github.com/dmontagu)
* Fix usage of `Any` to allow `None`, also support `TypeVar` thus allowing use of un-parameterised collection types
  e.g. `Dict` and `List`, [#962](https://github.com/samuelcolvin/pydantic/issues/962) by [@samuelcolvin](https://github.com/samuelcolvin)
* Set `FieldInfo` on subfields to fix schema generation for complex nested types, [#965](https://github.com/samuelcolvin/pydantic/issues/965) by [@samuelcolvin](https://github.com/samuelcolvin)

## v1.0 (2019-10-23)

* **Breaking Change:** deprecate the `Model.fields` property, use `Model.__fields__` instead, [#883](https://github.com/samuelcolvin/pydantic/issues/883) by [@samuelcolvin](https://github.com/samuelcolvin)
* **Breaking Change:** Change the precedence of aliases so child model aliases override parent aliases,
  including using `alias_generator`, [#904](https://github.com/samuelcolvin/pydantic/issues/904) by [@samuelcolvin](https://github.com/samuelcolvin)
* **Breaking change:** Rename `skip_defaults` to `exclude_unset`, and add ability to exclude actual defaults, [#915](https://github.com/samuelcolvin/pydantic/issues/915) by [@dmontagu](https://github.com/dmontagu)
* Add `**kwargs` to `pydantic.main.ModelMetaclass.__new__` so `__init_subclass__` can take custom parameters on extended
  `BaseModel` classes, [#867](https://github.com/samuelcolvin/pydantic/issues/867) by [@retnikt](https://github.com/retnikt)
* Fix field of a type that has a default value, [#880](https://github.com/samuelcolvin/pydantic/issues/880) by [@koxudaxi](https://github.com/koxudaxi)
* Use `FutureWarning` instead of `DeprecationWarning` when `alias` instead of `env` is used for settings models, [#881](https://github.com/samuelcolvin/pydantic/issues/881) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix issue with `BaseSettings` inheritance and `alias` getting set to `None`, [#882](https://github.com/samuelcolvin/pydantic/issues/882) by [@samuelcolvin](https://github.com/samuelcolvin)
* Modify `__repr__` and `__str__` methods to be consistent across all public classes, add `__pretty__` to support
  python-devtools, [#884](https://github.com/samuelcolvin/pydantic/issues/884) by [@samuelcolvin](https://github.com/samuelcolvin)
* deprecation warning for `case_insensitive` on `BaseSettings` config, [#885](https://github.com/samuelcolvin/pydantic/issues/885) by [@samuelcolvin](https://github.com/samuelcolvin)
* For `BaseSettings` merge environment variables and in-code values recursively, as long as they create a valid object
  when merged together, to allow splitting init arguments, [#888](https://github.com/samuelcolvin/pydantic/issues/888) by [@idmitrievsky](https://github.com/idmitrievsky)
* change secret types example, [#890](https://github.com/samuelcolvin/pydantic/issues/890) by [@ashears](https://github.com/ashears)
* Change the signature of `Model.construct()` to be more user-friendly, document `construct()` usage, [#898](https://github.com/samuelcolvin/pydantic/issues/898) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add example for the `construct()` method, [#907](https://github.com/samuelcolvin/pydantic/issues/907) by [@ashears](https://github.com/ashears)
* Improve use of `Field` constraints on complex types, raise an error if constraints are not enforceable,
  also support tuples with an ellipsis `Tuple[X, ...]`, `Sequence` and `FrozenSet` in schema, [#909](https://github.com/samuelcolvin/pydantic/issues/909) by [@samuelcolvin](https://github.com/samuelcolvin)
* update docs for bool missing valid value, [#911](https://github.com/samuelcolvin/pydantic/issues/911) by [@trim21](https://github.com/trim21)
* Better `str`/`repr` logic for `ModelField`, [#912](https://github.com/samuelcolvin/pydantic/issues/912) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix `ConstrainedList`, update schema generation to reflect `min_items` and `max_items` `Field()` arguments, [#917](https://github.com/samuelcolvin/pydantic/issues/917) by [@samuelcolvin](https://github.com/samuelcolvin)
* Allow abstracts sets (eg. dict keys) in the `include` and `exclude` arguments of `dict()`, [#921](https://github.com/samuelcolvin/pydantic/issues/921) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix JSON serialization errors on `ValidationError.json()` by using `pydantic_encoder`, [#922](https://github.com/samuelcolvin/pydantic/issues/922) by [@samuelcolvin](https://github.com/samuelcolvin)
* Clarify usage of `remove_untouched`, improve error message for types with no validators, [#926](https://github.com/samuelcolvin/pydantic/issues/926) by [@retnikt](https://github.com/retnikt)

## v1.0b2 (2019-10-07)

* Mark `StrictBool` typecheck as `bool` to allow for default values without mypy errors, [#690](https://github.com/samuelcolvin/pydantic/issues/690) by [@dmontagu](https://github.com/dmontagu)
* Transfer the documentation build from sphinx to mkdocs, re-write much of the documentation, [#856](https://github.com/samuelcolvin/pydantic/issues/856) by [@samuelcolvin](https://github.com/samuelcolvin)
* Add support for custom naming schemes for `GenericModel` subclasses, [#859](https://github.com/samuelcolvin/pydantic/issues/859) by [@dmontagu](https://github.com/dmontagu)
* Add `if TYPE_CHECKING:` to the excluded lines for test coverage, [#874](https://github.com/samuelcolvin/pydantic/issues/874) by [@dmontagu](https://github.com/dmontagu)
* Rename `allow_population_by_alias` to `allow_population_by_field_name`, remove unnecessary warning about it, [#875](https://github.com/samuelcolvin/pydantic/issues/875) by [@samuelcolvin](https://github.com/samuelcolvin)

## v1.0b1 (2019-10-01)

* **Breaking Change:** rename `Schema` to `Field`, make it a function to placate mypy, [#577](https://github.com/samuelcolvin/pydantic/issues/577) by [@samuelcolvin](https://github.com/samuelcolvin)
* **Breaking Change:** modify parsing behavior for `bool`, [#617](https://github.com/samuelcolvin/pydantic/issues/617) by [@dmontagu](https://github.com/dmontagu)
* **Breaking Change:** `get_validators` is no longer recognised, use `__get_validators__`.
  `Config.ignore_extra` and `Config.allow_extra` are no longer recognised, use `Config.extra`, [#720](https://github.com/samuelcolvin/pydantic/issues/720) by [@samuelcolvin](https://github.com/samuelcolvin)
* **Breaking Change:** modify default config settings for `BaseSettings`; `case_insensitive` renamed to `case_sensitive`,
  default changed to `case_sensitive = False`, `env_prefix` default changed to `''` - e.g. no prefix, [#721](https://github.com/samuelcolvin/pydantic/issues/721) by [@dmontagu](https://github.com/dmontagu)
* **Breaking change:** Implement `root_validator` and rename root errors from `__obj__` to `__root__`, [#729](https://github.com/samuelcolvin/pydantic/issues/729) by [@samuelcolvin](https://github.com/samuelcolvin)
* **Breaking Change:** alter the behaviour of `dict(model)` so that sub-models are nolonger
  converted to dictionaries, [#733](https://github.com/samuelcolvin/pydantic/issues/733) by [@samuelcolvin](https://github.com/samuelcolvin)
* **Breaking change:** Added `initvars` support to `post_init_post_parse`, [#748](https://github.com/samuelcolvin/pydantic/issues/748) by [@Raphael-C-Almeida](https://github.com/Raphael-C-Almeida)
* **Breaking Change:** Make `BaseModel.json()` only serialize the `__root__` key for models with custom root, [#752](https://github.com/samuelcolvin/pydantic/issues/752) by [@dmontagu](https://github.com/dmontagu)
* **Breaking Change:** complete rewrite of `URL` parsing logic, [#755](https://github.com/samuelcolvin/pydantic/issues/755) by [@samuelcolvin](https://github.com/samuelcolvin)
* **Breaking Change:** preserve superclass annotations for field-determination when not provided in subclass, [#757](https://github.com/samuelcolvin/pydantic/issues/757) by [@dmontagu](https://github.com/dmontagu)
* **Breaking Change:** `BaseSettings` now uses the special `env` settings to define which environment variables to
  read, not aliases, [#847](https://github.com/samuelcolvin/pydantic/issues/847) by [@samuelcolvin](https://github.com/samuelcolvin)
* add support for `assert` statements inside validators, [#653](https://github.com/samuelcolvin/pydantic/issues/653) by [@abdusco](https://github.com/abdusco)
* Update documentation to specify the use of `pydantic.dataclasses.dataclass` and subclassing `pydantic.BaseModel`, [#710](https://github.com/samuelcolvin/pydantic/issues/710) by [@maddosaurus](https://github.com/maddosaurus)
* Allow custom JSON decoding and encoding via `json_loads` and `json_dumps` `Config` properties, [#714](https://github.com/samuelcolvin/pydantic/issues/714) by [@samuelcolvin](https://github.com/samuelcolvin)
* make all annotated fields occur in the order declared, [#715](https://github.com/samuelcolvin/pydantic/issues/715) by [@dmontagu](https://github.com/dmontagu)
* use pytest to test `mypy` integration, [#735](https://github.com/samuelcolvin/pydantic/issues/735) by [@dmontagu](https://github.com/dmontagu)
* add `__repr__` method to `ErrorWrapper`, [#738](https://github.com/samuelcolvin/pydantic/issues/738) by [@samuelcolvin](https://github.com/samuelcolvin)
* Added support for `FrozenSet` members in dataclasses, and a better error when attempting to use types from the `typing` module that are not supported by Pydantic, [#745](https://github.com/samuelcolvin/pydantic/issues/745) by [@djpetti](https://github.com/djpetti)
* add documentation for Pycharm Plugin, [#750](https://github.com/samuelcolvin/pydantic/issues/750) by [@koxudaxi](https://github.com/koxudaxi)
* fix broken examples in the docs, [#753](https://github.com/samuelcolvin/pydantic/issues/753) by [@dmontagu](https://github.com/dmontagu)
* moving typing related objects into `pydantic.typing`, [#761](https://github.com/samuelcolvin/pydantic/issues/761) by [@samuelcolvin](https://github.com/samuelcolvin)
* Minor performance improvements to `ErrorWrapper`, `ValidationError` and datetime parsing, [#763](https://github.com/samuelcolvin/pydantic/issues/763) by [@samuelcolvin](https://github.com/samuelcolvin)
* Improvements to `datetime`/`date`/`time`/`timedelta` types: more descriptive errors,
  change errors to `value_error` not `type_error`, support bytes, [#766](https://github.com/samuelcolvin/pydantic/issues/766) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix error messages for `Literal` types with multiple allowed values, [#770](https://github.com/samuelcolvin/pydantic/issues/770) by [@dmontagu](https://github.com/dmontagu)
* Improved auto-generated `title` field in JSON schema by converting underscore to space, [#772](https://github.com/samuelcolvin/pydantic/issues/772) by [@skewty](https://github.com/skewty)
* support `mypy --no-implicit-reexport` for dataclasses, also respect `--no-implicit-reexport` in pydantic itself, [#783](https://github.com/samuelcolvin/pydantic/issues/783) by [@samuelcolvin](https://github.com/samuelcolvin)
* add the `PaymentCardNumber` type, [#790](https://github.com/samuelcolvin/pydantic/issues/790) by [@matin](https://github.com/matin)
* Fix const validations for lists, [#794](https://github.com/samuelcolvin/pydantic/issues/794) by [@hmvp](https://github.com/hmvp)
* Set `additionalProperties` to false in schema for models with extra fields disallowed, [#796](https://github.com/samuelcolvin/pydantic/issues/796) by [@Code0x58](https://github.com/Code0x58)
* `EmailStr` validation method now returns local part case-sensitive per RFC 5321, [#798](https://github.com/samuelcolvin/pydantic/issues/798) by [@henriklindgren](https://github.com/henriklindgren)
* Added ability to validate strictness to `ConstrainedFloat`, `ConstrainedInt` and `ConstrainedStr` and added
  `StrictFloat` and `StrictInt` classes, [#799](https://github.com/samuelcolvin/pydantic/issues/799) by [@DerRidda](https://github.com/DerRidda)
* Improve handling of `None` and `Optional`, replace `whole` with `each_item` (inverse meaning, default `False`)
  on validators, [#803](https://github.com/samuelcolvin/pydantic/issues/803) by [@samuelcolvin](https://github.com/samuelcolvin)
* add support for `Type[T]` type hints, [#807](https://github.com/samuelcolvin/pydantic/issues/807) by [@timonbimon](https://github.com/timonbimon)
* Performance improvements from removing `change_exceptions`, change how pydantic error are constructed, [#819](https://github.com/samuelcolvin/pydantic/issues/819) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix the error message arising when a `BaseModel`-type model field causes a `ValidationError` during parsing, [#820](https://github.com/samuelcolvin/pydantic/issues/820) by [@dmontagu](https://github.com/dmontagu)
* allow `getter_dict` on `Config`, modify `GetterDict` to be more like a `Mapping` object and thus easier to work with, [#821](https://github.com/samuelcolvin/pydantic/issues/821) by [@samuelcolvin](https://github.com/samuelcolvin)
* Only check `TypeVar` param on base `GenericModel` class, [#842](https://github.com/samuelcolvin/pydantic/issues/842) by [@zpencerq](https://github.com/zpencerq)
* rename `Model._schema_cache` -> `Model.__schema_cache__`, `Model._json_encoder` -> `Model.__json_encoder__`,
  `Model._custom_root_type` -> `Model.__custom_root_type__`, [#851](https://github.com/samuelcolvin/pydantic/issues/851) by [@samuelcolvin](https://github.com/samuelcolvin)

## v0.32.2 (2019-08-17)

(Docs are available [here](https://5d584fcca7c9b70007d1c997--pydantic-docs.netlify.com))

* fix `__post_init__` usage with dataclass inheritance, fix [#739](https://github.com/samuelcolvin/pydantic/issues/739) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix required fields validation on GenericModels classes, [#742](https://github.com/samuelcolvin/pydantic/issues/742) by [@amitbl](https://github.com/amitbl)
* fix defining custom `Schema` on `GenericModel` fields, [#754](https://github.com/samuelcolvin/pydantic/issues/754) by [@amitbl](https://github.com/amitbl)

## v0.32.1 (2019-08-08)

* do not validate extra fields when `validate_assignment` is on, [#724](https://github.com/samuelcolvin/pydantic/issues/724) by [@YaraslauZhylko](https://github.com/YaraslauZhylko)

## v0.32 (2019-08-06)

* add model name to `ValidationError` error message, [#676](https://github.com/samuelcolvin/pydantic/issues/676) by [@dmontagu](https://github.com/dmontagu)
* **breaking change**: remove `__getattr__` and rename `__values__` to `__dict__` on `BaseModel`,
  deprecation warning on use `__values__` attr, attributes access speed increased up to 14 times, [#712](https://github.com/samuelcolvin/pydantic/issues/712) by [@Bobronium](https://github.com/Bobronium)
* support `ForwardRef` (without self-referencing annotations) in Python 3.6, [#706](https://github.com/samuelcolvin/pydantic/issues/706) by [@koxudaxi](https://github.com/koxudaxi)
* implement `schema_extra` in `Config` sub-class, [#663](https://github.com/samuelcolvin/pydantic/issues/663) by [@tiangolo](https://github.com/tiangolo)

## v0.31.1 (2019-07-31)

* fix json generation for `EnumError`, [#697](https://github.com/samuelcolvin/pydantic/issues/697) by [@dmontagu](https://github.com/dmontagu)
* update numerous dependencies

## v0.31 (2019-07-24)

* better support for floating point `multiple_of` values, [#652](https://github.com/samuelcolvin/pydantic/issues/652) by [@justindujardin](https://github.com/justindujardin)
* fix schema generation for `NewType` and `Literal`, [#649](https://github.com/samuelcolvin/pydantic/issues/649) by [@dmontagu](https://github.com/dmontagu)
* fix `alias_generator` and field config conflict, [#645](https://github.com/samuelcolvin/pydantic/issues/645) by [@gmetzker](https://github.com/gmetzker) and [#658](https://github.com/samuelcolvin/pydantic/issues/658) by [@Bobronium](https://github.com/Bobronium)
* more detailed message for `EnumError`, [#673](https://github.com/samuelcolvin/pydantic/issues/673) by [@dmontagu](https://github.com/dmontagu)
* add advanced exclude support for `dict`, `json` and `copy`, [#648](https://github.com/samuelcolvin/pydantic/issues/648) by [@Bobronium](https://github.com/Bobronium)
* fix bug in `GenericModel` for models with concrete parameterized fields, [#672](https://github.com/samuelcolvin/pydantic/issues/672) by [@dmontagu](https://github.com/dmontagu)
* add documentation for `Literal` type, [#651](https://github.com/samuelcolvin/pydantic/issues/651) by [@dmontagu](https://github.com/dmontagu)
* add `Config.keep_untouched` for custom descriptors support, [#679](https://github.com/samuelcolvin/pydantic/issues/679) by [@Bobronium](https://github.com/Bobronium)
* use `inspect.cleandoc` internally to get model description, [#657](https://github.com/samuelcolvin/pydantic/issues/657) by [@tiangolo](https://github.com/tiangolo)
* add `Color` to schema generation, by [@euri10](https://github.com/euri10)
* add documentation for Literal type, [#651](https://github.com/samuelcolvin/pydantic/issues/651) by [@dmontagu](https://github.com/dmontagu)

## v0.30.1 (2019-07-15)

* fix so nested classes which inherit and change `__init__` are correctly processed while still allowing `self` as a
  parameter, [#644](https://github.com/samuelcolvin/pydantic/issues/644) by [@lnaden](https://github.com/lnaden) and [@dgasmith](https://github.com/dgasmith)

## v0.30 (2019-07-07)

* enforce single quotes in code, [#612](https://github.com/samuelcolvin/pydantic/issues/612) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix infinite recursion with dataclass inheritance and `__post_init__`, [#606](https://github.com/samuelcolvin/pydantic/issues/606) by [@Hanaasagi](https://github.com/Hanaasagi)
* fix default values for `GenericModel`, [#610](https://github.com/samuelcolvin/pydantic/issues/610) by [@dmontagu](https://github.com/dmontagu)
* clarify that self-referencing models require python 3.7+, [#616](https://github.com/samuelcolvin/pydantic/issues/616) by [@vlcinsky](https://github.com/vlcinsky)
* fix truncate for types, [#611](https://github.com/samuelcolvin/pydantic/issues/611) by [@dmontagu](https://github.com/dmontagu)
* add `alias_generator` support, [#622](https://github.com/samuelcolvin/pydantic/issues/622) by [@Bobronium](https://github.com/Bobronium)
* fix unparameterized generic type schema generation, [#625](https://github.com/samuelcolvin/pydantic/issues/625) by [@dmontagu](https://github.com/dmontagu)
* fix schema generation with multiple/circular references to the same model, [#621](https://github.com/samuelcolvin/pydantic/issues/621) by [@tiangolo](https://github.com/tiangolo) and [@wongpat](https://github.com/wongpat)
* support custom root types, [#628](https://github.com/samuelcolvin/pydantic/issues/628) by [@koxudaxi](https://github.com/koxudaxi)
* support `self` as a field name in `parse_obj`, [#632](https://github.com/samuelcolvin/pydantic/issues/632) by [@samuelcolvin](https://github.com/samuelcolvin)

## v0.29 (2019-06-19)

* support dataclasses.InitVar, [#592](https://github.com/samuelcolvin/pydantic/issues/592) by [@pfrederiks](https://github.com/pfrederiks)
* Updated documentation to elucidate the usage of `Union` when defining multiple types under an attribute's
  annotation and showcase how the type-order can affect marshalling of provided values, [#594](https://github.com/samuelcolvin/pydantic/issues/594) by [@somada141](https://github.com/somada141)
* add `conlist` type, [#583](https://github.com/samuelcolvin/pydantic/issues/583) by [@hmvp](https://github.com/hmvp)
* add support for generics, [#595](https://github.com/samuelcolvin/pydantic/issues/595) by [@dmontagu](https://github.com/dmontagu)

## v0.28 (2019-06-06)

* fix support for JSON Schema generation when using models with circular references in Python 3.7, [#572](https://github.com/samuelcolvin/pydantic/issues/572) by [@tiangolo](https://github.com/tiangolo)
* support `__post_init_post_parse__` on dataclasses, [#567](https://github.com/samuelcolvin/pydantic/issues/567) by [@sevaho](https://github.com/sevaho)
* allow dumping dataclasses to JSON, [#575](https://github.com/samuelcolvin/pydantic/issues/575) by [@samuelcolvin](https://github.com/samuelcolvin) and [@DanielOberg](https://github.com/DanielOberg)
* ORM mode, [#562](https://github.com/samuelcolvin/pydantic/issues/562) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix `pydantic.compiled` on ipython, [#573](https://github.com/samuelcolvin/pydantic/issues/573) by [@dmontagu](https://github.com/dmontagu) and [@samuelcolvin](https://github.com/samuelcolvin)
* add `StrictBool` type, [#579](https://github.com/samuelcolvin/pydantic/issues/579) by [@cazgp](https://github.com/cazgp)

## v0.27 (2019-05-30)

* **breaking change**  `_pydantic_post_init` to execute dataclass' original `__post_init__` before
  validation, [#560](https://github.com/samuelcolvin/pydantic/issues/560) by [@HeavenVolkoff](https://github.com/HeavenVolkoff)
* fix handling of generic types without specified parameters, [#550](https://github.com/samuelcolvin/pydantic/issues/550) by [@dmontagu](https://github.com/dmontagu)
* **breaking change** (maybe): this is the first release compiled with **cython**, see the docs and please
  submit an issue if you run into problems

## v0.27.0a1 (2019-05-26)

* fix JSON Schema for `list`, `tuple`, and `set`, [#540](https://github.com/samuelcolvin/pydantic/issues/540) by [@tiangolo](https://github.com/tiangolo)
* compiling with cython, `manylinux` binaries, some other performance improvements, [#548](https://github.com/samuelcolvin/pydantic/issues/548) by [@samuelcolvin](https://github.com/samuelcolvin)

## v0.26 (2019-05-22)

* fix to schema generation for `IPvAnyAddress`, `IPvAnyInterface`, `IPvAnyNetwork` [#498](https://github.com/samuelcolvin/pydantic/issues/498) by [@pilosus](https://github.com/pilosus)
* fix variable length tuples support, [#495](https://github.com/samuelcolvin/pydantic/issues/495) by [@pilosus](https://github.com/pilosus)
* fix return type hint for `create_model`, [#526](https://github.com/samuelcolvin/pydantic/issues/526) by [@dmontagu](https://github.com/dmontagu)
* **Breaking Change:** fix `.dict(skip_keys=True)` skipping values set via alias (this involves changing
  `validate_model()` to always returns `Tuple[Dict[str, Any], Set[str], Optional[ValidationError]]`), [#517](https://github.com/samuelcolvin/pydantic/issues/517) by [@sommd](https://github.com/sommd)
* fix to schema generation for `IPv4Address`, `IPv6Address`, `IPv4Interface`,
  `IPv6Interface`, `IPv4Network`, `IPv6Network` [#532](https://github.com/samuelcolvin/pydantic/issues/532) by [@euri10](https://github.com/euri10)
* add `Color` type, [#504](https://github.com/samuelcolvin/pydantic/issues/504) by [@pilosus](https://github.com/pilosus) and [@samuelcolvin](https://github.com/samuelcolvin)

## v0.25 (2019-05-05)

* Improve documentation on self-referencing models and annotations, [#487](https://github.com/samuelcolvin/pydantic/issues/487) by [@theenglishway](https://github.com/theenglishway)
* fix `.dict()` with extra keys, [#490](https://github.com/samuelcolvin/pydantic/issues/490) by [@JaewonKim](https://github.com/JaewonKim)
* support `const` keyword in `Schema`, [#434](https://github.com/samuelcolvin/pydantic/issues/434) by [@Sean1708](https://github.com/Sean1708)

## v0.24 (2019-04-23)

* fix handling `ForwardRef` in sub-types, like `Union`, [#464](https://github.com/samuelcolvin/pydantic/issues/464) by [@tiangolo](https://github.com/tiangolo)
* fix secret serialization, [#465](https://github.com/samuelcolvin/pydantic/issues/465) by [@atheuz](https://github.com/atheuz)
* Support custom validators for dataclasses, [#454](https://github.com/samuelcolvin/pydantic/issues/454) by [@primal100](https://github.com/primal100)
* fix `parse_obj` to cope with dict-like objects, [#472](https://github.com/samuelcolvin/pydantic/issues/472) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix to schema generation in nested dataclass-based models, [#474](https://github.com/samuelcolvin/pydantic/issues/474) by [@NoAnyLove](https://github.com/NoAnyLove)
* fix `json` for `Path`, `FilePath`, and `DirectoryPath` objects, [#473](https://github.com/samuelcolvin/pydantic/issues/473) by [@mikegoodspeed](https://github.com/mikegoodspeed)

## v0.23 (2019-04-04)

* improve documentation for contributing section, [#441](https://github.com/samuelcolvin/pydantic/issues/441) by [@pilosus](https://github.com/pilosus)
* improve README.rst to include essential information about the package, [#446](https://github.com/samuelcolvin/pydantic/issues/446) by [@pilosus](https://github.com/pilosus)
* `IntEnum` support, [#444](https://github.com/samuelcolvin/pydantic/issues/444) by [@potykion](https://github.com/potykion)
* fix PyObject callable value, [#409](https://github.com/samuelcolvin/pydantic/issues/409) by [@pilosus](https://github.com/pilosus)
* fix `black` deprecation warnings after update, [#451](https://github.com/samuelcolvin/pydantic/issues/451) by [@pilosus](https://github.com/pilosus)
* fix `ForwardRef` collection bug, [#450](https://github.com/samuelcolvin/pydantic/issues/450) by [@tigerwings](https://github.com/tigerwings)
* Support specialized `ClassVars`, [#455](https://github.com/samuelcolvin/pydantic/issues/455) by [@tyrylu](https://github.com/tyrylu)
* fix JSON serialization for `ipaddress` types, [#333](https://github.com/samuelcolvin/pydantic/issues/333) by [@pilosus](https://github.com/pilosus)
* add `SecretStr` and `SecretBytes` types, [#452](https://github.com/samuelcolvin/pydantic/issues/452) by [@atheuz](https://github.com/atheuz)

## v0.22 (2019-03-29)

* add `IPv{4,6,Any}Network` and `IPv{4,6,Any}Interface` types from `ipaddress` stdlib, [#333](https://github.com/samuelcolvin/pydantic/issues/333) by [@pilosus](https://github.com/pilosus)
* add docs for `datetime` types, [#386](https://github.com/samuelcolvin/pydantic/issues/386) by [@pilosus](https://github.com/pilosus)
* fix to schema generation in dataclass-based models, [#408](https://github.com/samuelcolvin/pydantic/issues/408) by [@pilosus](https://github.com/pilosus)
* fix path in nested models, [#437](https://github.com/samuelcolvin/pydantic/issues/437) by [@kataev](https://github.com/kataev)
* add `Sequence` support, [#304](https://github.com/samuelcolvin/pydantic/issues/304) by [@pilosus](https://github.com/pilosus)

## v0.21.0 (2019-03-15)

* fix typo in `NoneIsNotAllowedError` message, [#414](https://github.com/samuelcolvin/pydantic/issues/414) by [@YaraslauZhylko](https://github.com/YaraslauZhylko)
* add `IPvAnyAddress`, `IPv4Address` and `IPv6Address` types, [#333](https://github.com/samuelcolvin/pydantic/issues/333) by [@pilosus](https://github.com/pilosus)

## v0.20.1 (2019-02-26)

* fix type hints of `parse_obj` and similar methods, [#405](https://github.com/samuelcolvin/pydantic/issues/405) by [@erosennin](https://github.com/erosennin)
* fix submodel validation, [#403](https://github.com/samuelcolvin/pydantic/issues/403) by [@samuelcolvin](https://github.com/samuelcolvin)
* correct type hints for `ValidationError.json`, [#406](https://github.com/samuelcolvin/pydantic/issues/406) by [@layday](https://github.com/layday)

## v0.20.0 (2019-02-18)

* fix tests for python 3.8, [#396](https://github.com/samuelcolvin/pydantic/issues/396) by [@samuelcolvin](https://github.com/samuelcolvin)
* Adds fields to the `dir` method for autocompletion in interactive sessions, [#398](https://github.com/samuelcolvin/pydantic/issues/398) by [@dgasmith](https://github.com/dgasmith)
* support `ForwardRef` (and therefore `from __future__ import annotations`) with dataclasses, [#397](https://github.com/samuelcolvin/pydantic/issues/397) by [@samuelcolvin](https://github.com/samuelcolvin)

## v0.20.0a1 (2019-02-13)

* **breaking change** (maybe): more sophisticated argument parsing for validators, any subset of
  `values`, `config` and `field` is now permitted, eg. `(cls, value, field)`,
  however the variadic key word argument ("`**kwargs`") **must** be called `kwargs`, [#388](https://github.com/samuelcolvin/pydantic/issues/388) by [@samuelcolvin](https://github.com/samuelcolvin)
* **breaking change**: Adds `skip_defaults` argument to `BaseModel.dict()` to allow skipping of fields that
  were not explicitly set, signature of `Model.construct()` changed, [#389](https://github.com/samuelcolvin/pydantic/issues/389) by [@dgasmith](https://github.com/dgasmith)
* add `py.typed` marker file for PEP-561 support, [#391](https://github.com/samuelcolvin/pydantic/issues/391) by [@je-l](https://github.com/je-l)
* Fix `extra` behaviour for multiple inheritance/mix-ins, [#394](https://github.com/samuelcolvin/pydantic/issues/394) by [@YaraslauZhylko](https://github.com/YaraslauZhylko)

## v0.19.0 (2019-02-04)

* Support `Callable` type hint, fix [#279](https://github.com/samuelcolvin/pydantic/issues/279) by [@proofit404](https://github.com/proofit404)
* Fix schema for fields with `validator` decorator, fix [#375](https://github.com/samuelcolvin/pydantic/issues/375) by [@tiangolo](https://github.com/tiangolo)
* Add `multiple_of` constraint to `ConstrainedDecimal`, `ConstrainedFloat`, `ConstrainedInt`
  and their related types `condecimal`, `confloat`, and `conint` [#371](https://github.com/samuelcolvin/pydantic/issues/371), thanks [@StephenBrown2](https://github.com/StephenBrown2)
* Deprecated `ignore_extra` and `allow_extra` Config fields in favor of `extra`, [#352](https://github.com/samuelcolvin/pydantic/issues/352) by [@liiight](https://github.com/liiight)
* Add type annotations to all functions, test fully with mypy, [#373](https://github.com/samuelcolvin/pydantic/issues/373) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix for 'missing' error with `validate_all` or `validate_always`, [#381](https://github.com/samuelcolvin/pydantic/issues/381) by [@samuelcolvin](https://github.com/samuelcolvin)
* Change the second/millisecond watershed for date/datetime parsing to `2e10`, [#385](https://github.com/samuelcolvin/pydantic/issues/385) by [@samuelcolvin](https://github.com/samuelcolvin)

## v0.18.2 (2019-01-22)

* Fix to schema generation with `Optional` fields, fix [#361](https://github.com/samuelcolvin/pydantic/issues/361) by [@samuelcolvin](https://github.com/samuelcolvin)

## v0.18.1 (2019-01-17)

* add `ConstrainedBytes` and `conbytes` types, [#315](https://github.com/samuelcolvin/pydantic/issues/315) [@Gr1N](https://github.com/Gr1N)
* adding `MANIFEST.in` to include license in package `.tar.gz`, [#358](https://github.com/samuelcolvin/pydantic/issues/358) by [@samuelcolvin](https://github.com/samuelcolvin)

## v0.18.0 (2019-01-13)

* **breaking change**: don't call validators on keys of dictionaries, [#254](https://github.com/samuelcolvin/pydantic/issues/254) by [@samuelcolvin](https://github.com/samuelcolvin)
* Fix validators with `always=True` when the default is `None` or the type is optional, also prevent
  `whole` validators being called for sub-fields, fix [#132](https://github.com/samuelcolvin/pydantic/issues/132) by [@samuelcolvin](https://github.com/samuelcolvin)
* improve documentation for settings priority and allow it to be easily changed, [#343](https://github.com/samuelcolvin/pydantic/issues/343) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix `ignore_extra=False` and `allow_population_by_alias=True`, fix [#257](https://github.com/samuelcolvin/pydantic/issues/257) by [@samuelcolvin](https://github.com/samuelcolvin)
* **breaking change**: Set `BaseConfig` attributes `min_anystr_length` and `max_anystr_length` to
  `None` by default, fix [#349](https://github.com/samuelcolvin/pydantic/issues/349) in [#350](https://github.com/samuelcolvin/pydantic/issues/350) by [@tiangolo](https://github.com/tiangolo)
* add support for postponed annotations, [#348](https://github.com/samuelcolvin/pydantic/issues/348) by [@samuelcolvin](https://github.com/samuelcolvin)

## v0.17.0 (2018-12-27)

* fix schema for `timedelta` as number, [#325](https://github.com/samuelcolvin/pydantic/issues/325) by [@tiangolo](https://github.com/tiangolo)
* prevent validators being called repeatedly after inheritance, [#327](https://github.com/samuelcolvin/pydantic/issues/327) by [@samuelcolvin](https://github.com/samuelcolvin)
* prevent duplicate validator check in ipython, fix [#312](https://github.com/samuelcolvin/pydantic/issues/312) by [@samuelcolvin](https://github.com/samuelcolvin)
* add "Using Pydantic" section to docs, [#323](https://github.com/samuelcolvin/pydantic/issues/323) by [@tiangolo](https://github.com/tiangolo) & [#326](https://github.com/samuelcolvin/pydantic/issues/326) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix schema generation for fields annotated as `: dict`, `: list`,
  `: tuple` and `: set`, [#330](https://github.com/samuelcolvin/pydantic/issues/330) & [#335](https://github.com/samuelcolvin/pydantic/issues/335) by [@nkonin](https://github.com/nkonin)
* add support for constrained strings as dict keys in schema, [#332](https://github.com/samuelcolvin/pydantic/issues/332) by [@tiangolo](https://github.com/tiangolo)
* support for passing Config class in dataclasses decorator, [#276](https://github.com/samuelcolvin/pydantic/issues/276) by [@jarekkar](https://github.com/jarekkar)
  (**breaking change**: this supersedes the `validate_assignment` argument with `config`)
* support for nested dataclasses, [#334](https://github.com/samuelcolvin/pydantic/issues/334) by [@samuelcolvin](https://github.com/samuelcolvin)
* better errors when getting an `ImportError` with `PyObject`, [#309](https://github.com/samuelcolvin/pydantic/issues/309) by [@samuelcolvin](https://github.com/samuelcolvin)
* rename `get_validators` to `__get_validators__`, deprecation warning on use of old name, [#338](https://github.com/samuelcolvin/pydantic/issues/338) by [@samuelcolvin](https://github.com/samuelcolvin)
* support `ClassVar` by excluding such attributes from fields, [#184](https://github.com/samuelcolvin/pydantic/issues/184) by [@samuelcolvin](https://github.com/samuelcolvin)

## v0.16.1 (2018-12-10)

* fix `create_model` to correctly use the passed `__config__`, [#320](https://github.com/samuelcolvin/pydantic/issues/320) by [@hugoduncan](https://github.com/hugoduncan)

## v0.16.0 (2018-12-03)

* **breaking change**: refactor schema generation to be compatible with JSON Schema and OpenAPI specs, [#308](https://github.com/samuelcolvin/pydantic/issues/308) by [@tiangolo](https://github.com/tiangolo)
* add `schema` to `schema` module to generate top-level schemas from base models, [#308](https://github.com/samuelcolvin/pydantic/issues/308) by [@tiangolo](https://github.com/tiangolo)
* add additional fields to `Schema` class to declare validation for `str` and numeric values, [#311](https://github.com/samuelcolvin/pydantic/issues/311) by [@tiangolo](https://github.com/tiangolo)
* rename `_schema` to `schema` on fields, [#318](https://github.com/samuelcolvin/pydantic/issues/318) by [@samuelcolvin](https://github.com/samuelcolvin)
* add `case_insensitive` option to `BaseSettings` `Config`, [#277](https://github.com/samuelcolvin/pydantic/issues/277) by [@jasonkuhrt](https://github.com/jasonkuhrt)

## v0.15.0 (2018-11-18)

* move codebase to use black, [#287](https://github.com/samuelcolvin/pydantic/issues/287) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix alias use in settings, [#286](https://github.com/samuelcolvin/pydantic/issues/286) by [@jasonkuhrt](https://github.com/jasonkuhrt) and [@samuelcolvin](https://github.com/samuelcolvin)
* fix datetime parsing in `parse_date`, [#298](https://github.com/samuelcolvin/pydantic/issues/298) by [@samuelcolvin](https://github.com/samuelcolvin)
* allow dataclass inheritance, fix [#293](https://github.com/samuelcolvin/pydantic/issues/293) by [@samuelcolvin](https://github.com/samuelcolvin)
* fix `PyObject = None`, fix [#305](https://github.com/samuelcolvin/pydantic/issues/305) by [@samuelcolvin](https://github.com/samuelcolvin)
* allow `Pattern` type, fix [#303](https://github.com/samuelcolvin/pydantic/issues/303) by [@samuelcolvin](https://github.com/samuelcolvin)

## v0.14.0 (2018-10-02)

* dataclasses decorator, [#269](https://github.com/samuelcolvin/pydantic/issues/269) by [@Gaunt](https://github.com/Gaunt) and [@samuelcolvin](https://github.com/samuelcolvin)

## v0.13.1 (2018-09-21)

* fix issue where int_validator doesn't cast a `bool` to an `int` [#264](https://github.com/samuelcolvin/pydantic/issues/264) by [@nphyatt](https://github.com/nphyatt)
* add deep copy support for `BaseModel.copy()` [#249](https://github.com/samuelcolvin/pydantic/issues/249), [@gangefors](https://github.com/gangefors)

## v0.13.0 (2018-08-25)

* raise an exception if a field's name shadows an existing `BaseModel` attribute [#242](https://github.com/samuelcolvin/pydantic/issues/242)
* add `UrlStr` and `urlstr` types [#236](https://github.com/samuelcolvin/pydantic/issues/236)
* timedelta json encoding ISO8601 and total seconds, custom json encoders [#247](https://github.com/samuelcolvin/pydantic/issues/247), by [@cfkanesan](https://github.com/cfkanesan) and [@samuelcolvin](https://github.com/samuelcolvin)
* allow `timedelta` objects as values for properties of type `timedelta` (matches `datetime` etc. behavior) [#247](https://github.com/samuelcolvin/pydantic/issues/247)

## v0.12.1 (2018-07-31)

* fix schema generation for fields defined using `typing.Any` [#237](https://github.com/samuelcolvin/pydantic/issues/237)

## v0.12.0 (2018-07-31)

* add `by_alias` argument in `.dict()` and `.json()` model methods [#205](https://github.com/samuelcolvin/pydantic/issues/205)
* add Json type support [#214](https://github.com/samuelcolvin/pydantic/issues/214)
* support tuples [#227](https://github.com/samuelcolvin/pydantic/issues/227)
* major improvements and changes to schema [#213](https://github.com/samuelcolvin/pydantic/issues/213)

## v0.11.2 (2018-07-05)

* add `NewType` support [#115](https://github.com/samuelcolvin/pydantic/issues/115)
* fix `list`, `set` & `tuple` validation [#225](https://github.com/samuelcolvin/pydantic/issues/225)
* separate out `validate_model` method, allow errors to be returned along with valid values [#221](https://github.com/samuelcolvin/pydantic/issues/221)

## v0.11.1 (2018-07-02)

* support Python 3.7 [#216](https://github.com/samuelcolvin/pydantic/issues/216), thanks [@layday](https://github.com/layday)
* Allow arbitrary types in model [#209](https://github.com/samuelcolvin/pydantic/issues/209), thanks [@oldPadavan](https://github.com/oldPadavan)

## v0.11.0 (2018-06-28)

* make `list`, `tuple` and `set` types stricter [#86](https://github.com/samuelcolvin/pydantic/issues/86)
* **breaking change**: remove msgpack parsing [#201](https://github.com/samuelcolvin/pydantic/issues/201)
* add `FilePath` and `DirectoryPath` types [#10](https://github.com/samuelcolvin/pydantic/issues/10)
* model schema generation [#190](https://github.com/samuelcolvin/pydantic/issues/190)
* JSON serialisation of models and schemas [#133](https://github.com/samuelcolvin/pydantic/issues/133)

## v0.10.0 (2018-06-11)

* add `Config.allow_population_by_alias` [#160](https://github.com/samuelcolvin/pydantic/issues/160), thanks [@bendemaree](https://github.com/bendemaree)
* **breaking change**: new errors format [#179](https://github.com/samuelcolvin/pydantic/issues/179), thanks [@Gr1N](https://github.com/Gr1N)
* **breaking change**: removed `Config.min_number_size` and `Config.max_number_size` [#183](https://github.com/samuelcolvin/pydantic/issues/183), thanks [@Gr1N](https://github.com/Gr1N)
* **breaking change**: correct behaviour of `lt` and `gt` arguments to `conint` etc. [#188](https://github.com/samuelcolvin/pydantic/issues/188)
  for the old behaviour use `le` and `ge` [#194](https://github.com/samuelcolvin/pydantic/issues/194), thanks [@jaheba](https://github.com/jaheba)
* added error context and ability to redefine error message templates using `Config.error_msg_templates` [#183](https://github.com/samuelcolvin/pydantic/issues/183),
  thanks [@Gr1N](https://github.com/Gr1N)
* fix typo in validator exception [#150](https://github.com/samuelcolvin/pydantic/issues/150)
* copy defaults to model values, so different models don't share objects [#154](https://github.com/samuelcolvin/pydantic/issues/154)

## v0.9.1 (2018-05-10)

* allow custom `get_field_config` on config classes [#159](https://github.com/samuelcolvin/pydantic/issues/159)
* add `UUID1`, `UUID3`, `UUID4` and `UUID5` types [#167](https://github.com/samuelcolvin/pydantic/issues/167), thanks [@Gr1N](https://github.com/Gr1N)
* modify some inconsistent docstrings and annotations [#173](https://github.com/samuelcolvin/pydantic/issues/173), thanks [@YannLuo](https://github.com/YannLuo)
* fix type annotations for exotic types [#171](https://github.com/samuelcolvin/pydantic/issues/171), thanks [@Gr1N](https://github.com/Gr1N)
* re-use type validators in exotic types [#171](https://github.com/samuelcolvin/pydantic/issues/171)
* scheduled monthly requirements updates [#168](https://github.com/samuelcolvin/pydantic/issues/168)
* add `Decimal`, `ConstrainedDecimal` and `condecimal` types [#170](https://github.com/samuelcolvin/pydantic/issues/170), thanks [@Gr1N](https://github.com/Gr1N)

## v0.9.0 (2018-04-28)

* tweak email-validator import error message [#145](https://github.com/samuelcolvin/pydantic/issues/145)
* fix parse error of `parse_date()` and `parse_datetime()` when input is 0 [#144](https://github.com/samuelcolvin/pydantic/issues/144), thanks [@YannLuo](https://github.com/YannLuo)
* add `Config.anystr_strip_whitespace` and `strip_whitespace` kwarg to `constr`,
  by default values is `False` [#163](https://github.com/samuelcolvin/pydantic/issues/163), thanks [@Gr1N](https://github.com/Gr1N)
* add `ConstrainedFloat`, `confloat`, `PositiveFloat` and `NegativeFloat` types [#166](https://github.com/samuelcolvin/pydantic/issues/166), thanks [@Gr1N](https://github.com/Gr1N)

## v0.8.0 (2018-03-25)

* fix type annotation for `inherit_config` [#139](https://github.com/samuelcolvin/pydantic/issues/139)
* **breaking change**: check for invalid field names in validators [#140](https://github.com/samuelcolvin/pydantic/issues/140)
* validate attributes of parent models [#141](https://github.com/samuelcolvin/pydantic/issues/141)
* **breaking change**: email validation now uses
  [email-validator](https://github.com/JoshData/python-email-validator) [#142](https://github.com/samuelcolvin/pydantic/issues/142)

## v0.7.1 (2018-02-07)

* fix bug with `create_model` modifying the base class

## v0.7.0 (2018-02-06)

* added compatibility with abstract base classes (ABCs) [#123](https://github.com/samuelcolvin/pydantic/issues/123)
* add `create_model` method [#113](https://github.com/samuelcolvin/pydantic/issues/113) [#125](https://github.com/samuelcolvin/pydantic/issues/125)
* **breaking change**: rename `.config` to `.__config__` on a model
* **breaking change**: remove deprecated `.values()` on a model, use `.dict()` instead
* remove use of `OrderedDict` and use simple dict [#126](https://github.com/samuelcolvin/pydantic/issues/126)
* add `Config.use_enum_values` [#127](https://github.com/samuelcolvin/pydantic/issues/127)
* add wildcard validators of the form `@validate('*')` [#128](https://github.com/samuelcolvin/pydantic/issues/128)

## v0.6.4 (2018-02-01)

* allow python date and times objects [#122](https://github.com/samuelcolvin/pydantic/issues/122)

## v0.6.3 (2017-11-26)

* fix direct install without `README.rst` present

## v0.6.2 (2017-11-13)

* errors for invalid validator use
* safer check for complex models in `Settings`

## v0.6.1 (2017-11-08)

* prevent duplicate validators, [#101](https://github.com/samuelcolvin/pydantic/issues/101)
* add `always` kwarg to validators, [#102](https://github.com/samuelcolvin/pydantic/issues/102)

## v0.6.0 (2017-11-07)

* assignment validation [#94](https://github.com/samuelcolvin/pydantic/issues/94), thanks petroswork!
* JSON in environment variables for complex types, [#96](https://github.com/samuelcolvin/pydantic/issues/96)
* add `validator` decorators for complex validation, [#97](https://github.com/samuelcolvin/pydantic/issues/97)
* depreciate `values(...)` and replace with `.dict(...)`, [#99](https://github.com/samuelcolvin/pydantic/issues/99)

## v0.5.0 (2017-10-23)

* add `UUID` validation [#89](https://github.com/samuelcolvin/pydantic/issues/89)
* remove `index` and `track` from error object (json) if they're null [#90](https://github.com/samuelcolvin/pydantic/issues/90)
* improve the error text when a list is provided rather than a dict [#90](https://github.com/samuelcolvin/pydantic/issues/90)
* add benchmarks table to docs [#91](https://github.com/samuelcolvin/pydantic/issues/91)

## v0.4.0 (2017-07-08)

* show length in string validation error
* fix aliases in config during inheritance [#55](https://github.com/samuelcolvin/pydantic/issues/55)
* simplify error display
* use unicode ellipsis in `truncate`
* add `parse_obj`, `parse_raw` and `parse_file` helper functions [#58](https://github.com/samuelcolvin/pydantic/issues/58)
* switch annotation only fields to come first in fields list not last

## v0.3.0 (2017-06-21)

* immutable models via `config.allow_mutation = False`, associated cleanup and performance improvement [#44](https://github.com/samuelcolvin/pydantic/issues/44)
* immutable helper methods `construct()` and `copy()` [#53](https://github.com/samuelcolvin/pydantic/issues/53)
* allow pickling of models [#53](https://github.com/samuelcolvin/pydantic/issues/53)
* `setattr` is removed as `__setattr__` is now intelligent [#44](https://github.com/samuelcolvin/pydantic/issues/44)
* `raise_exception` removed, Models now always raise exceptions [#44](https://github.com/samuelcolvin/pydantic/issues/44)
* instance method validators removed
* django-restful-framework benchmarks added [#47](https://github.com/samuelcolvin/pydantic/issues/47)
* fix inheritance bug [#49](https://github.com/samuelcolvin/pydantic/issues/49)
* make str type stricter so list, dict etc are not coerced to strings. [#52](https://github.com/samuelcolvin/pydantic/issues/52)
* add `StrictStr` which only always strings as input [#52](https://github.com/samuelcolvin/pydantic/issues/52)

## v0.2.1 (2017-06-07)

* pypi and travis together messed up the deploy of `v0.2` this should fix it

## v0.2.0 (2017-06-07)

* **breaking change**: `values()` on a model is now a method not a property,
  takes `include` and `exclude` arguments
* allow annotation only fields to support mypy
* add pretty `to_string(pretty=True)` method for models

## v0.1.0 (2017-06-03)

* add docs
* add history
