Metadata-Version: 2.1
Name: uvicorn
Version: 0.16.0
Summary: The lightning-fast ASGI server.
Home-page: https://www.uvicorn.org/
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Project-URL: Funding, https://github.com/sponsors/encode
Project-URL: Source, https://github.com/encode/uvicorn
Project-URL: Changelog, https://github.com/encode/uvicorn/blob/master/CHANGELOG.md
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Topic :: Internet :: WWW/HTTP
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Description-Content-Type: text/markdown
Requires-Dist: asgiref (>=3.4.0)
Requires-Dist: click (>=7.0)
Requires-Dist: h11 (>=0.8)
Requires-Dist: typing-extensions ; python_version < "3.8"
Provides-Extra: standard
Requires-Dist: httptools (<0.4.0,>=0.2.0) ; extra == 'standard'
Requires-Dist: watchgod (>=0.6) ; extra == 'standard'
Requires-Dist: python-dotenv (>=0.13) ; extra == 'standard'
Requires-Dist: PyYAML (>=5.1) ; extra == 'standard'
Requires-Dist: websockets (>=9.1) ; (python_version < "3.7") and extra == 'standard'
Requires-Dist: websockets (>=10.0) ; (python_version >= "3.7") and extra == 'standard'
Requires-Dist: uvloop (!=0.15.0,!=0.15.1,>=0.14.0) ; (sys_platform != "win32" and (sys_platform != "cygwin" and platform_python_implementation != "PyPy")) and extra == 'standard'
Requires-Dist: colorama (>=0.4) ; (sys_platform == "win32") and extra == 'standard'

<p align="center">
  <img width="320" height="320" src="https://raw.githubusercontent.com/tomchristie/uvicorn/master/docs/uvicorn.png" alt='uvicorn'>
</p>

<p align="center">
<em>The lightning-fast ASGI server.</em>
</p>

---

[![Build Status](https://github.com/encode/uvicorn/workflows/Test%20Suite/badge.svg)](https://github.com/encode/uvicorn/actions)
[![Package version](https://badge.fury.io/py/uvicorn.svg)](https://pypi.python.org/pypi/uvicorn)

**Documentation**: [https://www.uvicorn.org](https://www.uvicorn.org)

**Requirements**: Python 3.6+ (For Python 3.5 support, install version 0.8.6.)

Uvicorn is a lightning-fast ASGI server implementation, using [uvloop][uvloop] and [httptools][httptools].

Until recently Python has lacked a minimal low-level server/application interface for
asyncio frameworks. The [ASGI specification][asgi] fills this gap, and means we're now able to
start building a common set of tooling usable across all asyncio frameworks.

Uvicorn currently supports HTTP/1.1 and WebSockets. Support for HTTP/2 is planned.

## Quickstart

Install using `pip`:

```shell
$ pip install uvicorn
```

This will install uvicorn with minimal (pure Python) dependencies.

```shell
$ pip install uvicorn[standard]
```

This will install uvicorn with "Cython-based" dependencies (where possible) and other "optional extras".

In this context, "Cython-based" means the following:

- the event loop `uvloop` will be installed and used if possible.
- the http protocol will be handled by `httptools` if possible.

Moreover, "optional extras" means that:

- the websocket protocol will be handled by `websockets` (should you want to use `wsproto` you'd need to install it manually) if possible.
- the `--reload` flag in development mode will use `watchgod`.
- windows users will have `colorama` installed for the colored logs.
- `python-dotenv` will be installed should you want to use the `--env-file` option.
- `PyYAML` will be installed to allow you to provide a `.yaml` file to `--log-config`, if desired.

Create an application, in `example.py`:

```python
async def app(scope, receive, send):
    assert scope['type'] == 'http'

    await send({
        'type': 'http.response.start',
        'status': 200,
        'headers': [
            [b'content-type', b'text/plain'],
        ],
    })
    await send({
        'type': 'http.response.body',
        'body': b'Hello, world!',
    })
```

Run the server:

```shell
$ uvicorn example:app
```

---

<p align="center"><i>Uvicorn is <a href="https://github.com/encode/uvicorn/blob/master/LICENSE.md">BSD licensed</a> code.<br/>Designed & built in Brighton, England.</i><br/>&mdash; 🦄  &mdash;</p>

[uvloop]: https://github.com/MagicStack/uvloop
[httptools]: https://github.com/MagicStack/httptools
[asgi]: https://asgi.readthedocs.io/en/latest/


