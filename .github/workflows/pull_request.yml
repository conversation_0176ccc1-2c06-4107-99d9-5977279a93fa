name: Test on pull request

on:
  pull_request:
    branches:
      - main
      - vdbbench_*

jobs:
  build:
    name: Run Python Tests
    strategy:
      matrix:
        python-version: [3.11, 3.12]
        os: [ubuntu-latest, windows-latest]
    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Fetch tags
        run: |
          git fetch --prune --unshallow --tags

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e ".[test]"

      - name: Run coding checks
        run: |
          make lint

      - name: Test with pytest
        run: |
          make unittest
