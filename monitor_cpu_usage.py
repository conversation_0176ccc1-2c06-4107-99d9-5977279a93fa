#!/usr/bin/env python3
"""
CPU利用率监控脚本 - 诊断FAISS服务器性能问题
"""

import psutil
import time
import subprocess
import sys
import threading
from datetime import datetime
import json

class CPUMonitor:
    def __init__(self, interval=1.0):
        self.interval = interval
        self.monitoring = False
        self.data = []
        
    def get_process_info(self, process_name="python"):
        """获取指定进程的详细信息"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'num_threads']):
            try:
                if process_name.lower() in proc.info['name'].lower():
                    # 获取更详细的CPU信息
                    proc_obj = psutil.Process(proc.info['pid'])
                    cpu_times = proc_obj.cpu_times()
                    
                    processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cpu_percent': proc.info['cpu_percent'],
                        'memory_percent': proc.info['memory_percent'],
                        'num_threads': proc.info['num_threads'],
                        'cpu_times': {
                            'user': cpu_times.user,
                            'system': cpu_times.system,
                        },
                        'cmdline': ' '.join(proc_obj.cmdline()[:3])  # 只显示前3个参数
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return processes
    
    def get_system_info(self):
        """获取系统整体信息"""
        cpu_count = psutil.cpu_count()
        cpu_count_logical = psutil.cpu_count(logical=True)
        cpu_percent_per_core = psutil.cpu_percent(percpu=True)
        memory = psutil.virtual_memory()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'cpu_count_physical': cpu_count,
            'cpu_count_logical': cpu_count_logical,
            'cpu_percent_total': psutil.cpu_percent(),
            'cpu_percent_per_core': cpu_percent_per_core,
            'memory_percent': memory.percent,
            'memory_available_gb': memory.available / (1024**3),
            'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        }
    
    def check_openmp_settings(self):
        """检查OpenMP环境变量设置"""
        import os
        openmp_vars = [
            'OMP_NUM_THREADS',
            'MKL_NUM_THREADS', 
            'OPENBLAS_NUM_THREADS',
            'BLAS_NUM_THREADS',
            'LAPACK_NUM_THREADS',
            'OMP_WAIT_POLICY',
            'OMP_DYNAMIC',
            'OMP_NESTED',
            'OMP_PROC_BIND',
            'OMP_PLACES'
        ]
        
        settings = {}
        for var in openmp_vars:
            settings[var] = os.environ.get(var, 'NOT_SET')
        
        return settings
    
    def monitor_once(self):
        """执行一次监控"""
        print("=" * 80)
        print(f"🔍 CPU利用率监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 系统信息
        sys_info = self.get_system_info()
        print(f"💻 系统信息:")
        print(f"   物理核心: {sys_info['cpu_count_physical']}")
        print(f"   逻辑核心: {sys_info['cpu_count_logical']}")
        print(f"   总CPU使用率: {sys_info['cpu_percent_total']:.1f}%")
        print(f"   内存使用率: {sys_info['memory_percent']:.1f}%")
        if sys_info['load_average']:
            print(f"   负载平均: {sys_info['load_average']}")
        
        # 每核心CPU使用率
        print(f"\n🔥 各核心CPU使用率:")
        for i, cpu_pct in enumerate(sys_info['cpu_percent_per_core']):
            status = "🔴" if cpu_pct > 80 else "🟡" if cpu_pct > 50 else "🟢"
            print(f"   核心{i:2d}: {status} {cpu_pct:5.1f}%")
        
        # Python进程信息
        python_procs = self.get_process_info("python")
        if python_procs:
            print(f"\n🐍 Python进程信息:")
            for proc in python_procs:
                print(f"   PID {proc['pid']:6d}: CPU={proc['cpu_percent']:5.1f}% "
                      f"MEM={proc['memory_percent']:5.1f}% "
                      f"线程={proc['num_threads']:3d} "
                      f"CMD={proc['cmdline']}")
        
        # OpenMP设置
        openmp_settings = self.check_openmp_settings()
        print(f"\n⚙️  OpenMP环境变量:")
        for var, value in openmp_settings.items():
            status = "✅" if value != 'NOT_SET' else "❌"
            print(f"   {status} {var:20s}: {value}")
        
        return sys_info, python_procs, openmp_settings
    
    def start_continuous_monitoring(self, duration=60):
        """开始连续监控"""
        print(f"🚀 开始连续监控 {duration} 秒...")
        self.monitoring = True
        
        def monitor_loop():
            start_time = time.time()
            while self.monitoring and (time.time() - start_time) < duration:
                sys_info, python_procs, _ = self.monitor_once()
                self.data.append({
                    'timestamp': time.time(),
                    'system': sys_info,
                    'processes': python_procs
                })
                time.sleep(self.interval)
        
        monitor_thread = threading.Thread(target=monitor_loop)
        monitor_thread.start()
        monitor_thread.join()
        
        self.monitoring = False
        print(f"✅ 监控完成，共收集 {len(self.data)} 个数据点")
    
    def analyze_data(self):
        """分析收集的数据"""
        if not self.data:
            print("❌ 没有数据可分析")
            return
        
        print("\n📊 性能分析报告:")
        print("=" * 50)
        
        # CPU使用率统计
        cpu_usage = [d['system']['cpu_percent_total'] for d in self.data]
        print(f"🔥 CPU使用率统计:")
        print(f"   平均: {sum(cpu_usage)/len(cpu_usage):.1f}%")
        print(f"   最大: {max(cpu_usage):.1f}%")
        print(f"   最小: {min(cpu_usage):.1f}%")
        
        # 核心使用率分析
        core_count = len(self.data[0]['system']['cpu_percent_per_core'])
        core_usage_avg = [0] * core_count
        
        for d in self.data:
            for i, usage in enumerate(d['system']['cpu_percent_per_core']):
                core_usage_avg[i] += usage
        
        core_usage_avg = [usage / len(self.data) for usage in core_usage_avg]
        
        print(f"\n🎯 各核心平均使用率:")
        active_cores = sum(1 for usage in core_usage_avg if usage > 10)
        for i, avg_usage in enumerate(core_usage_avg):
            status = "🔴" if avg_usage > 50 else "🟡" if avg_usage > 10 else "⚪"
            print(f"   核心{i:2d}: {status} {avg_usage:5.1f}%")
        
        print(f"\n💡 诊断结果:")
        print(f"   活跃核心数: {active_cores}/{core_count}")
        if active_cores < core_count * 0.5:
            print(f"   ⚠️  CPU利用率不足！只有 {active_cores} 个核心在工作")
            print(f"   🔧 建议检查:")
            print(f"      - OpenMP线程数设置")
            print(f"      - FAISS索引类型和参数")
            print(f"      - 服务器线程池配置")
            print(f"      - 客户端并发模式")
        else:
            print(f"   ✅ CPU利用率良好，{active_cores} 个核心在工作")

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        duration = int(sys.argv[2]) if len(sys.argv) > 2 else 60
        monitor = CPUMonitor(interval=2.0)
        monitor.start_continuous_monitoring(duration)
        monitor.analyze_data()
    else:
        monitor = CPUMonitor()
        monitor.monitor_once()
        
        print(f"\n💡 使用提示:")
        print(f"   单次监控: python {sys.argv[0]}")
        print(f"   连续监控: python {sys.argv[0]} --continuous [秒数]")

if __name__ == "__main__":
    main()
