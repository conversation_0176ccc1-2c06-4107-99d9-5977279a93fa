#!/usr/bin/env python3
"""
测试环境变量设置
"""

import os
import sys

print("🔍 设置前的环境变量:")
print(f"   OMP_NUM_THREADS: {os.environ.get('OMP_NUM_THREADS', '未设置')}")
print(f"   MKL_NUM_THREADS: {os.environ.get('MKL_NUM_THREADS', '未设置')}")
print(f"   OPENBLAS_NUM_THREADS: {os.environ.get('OPENBLAS_NUM_THREADS', '未设置')}")
print(f"   BLAS_NUM_THREADS: {os.environ.get('BLAS_NUM_THREADS', '未设置')}")
print(f"   LAPACK_NUM_THREADS: {os.environ.get('LAPACK_NUM_THREADS', '未设置')}")

print(f"\n🔧 设置环境变量...")
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['MKL_NUM_THREADS'] = '16'
os.environ['OPENBLAS_NUM_THREADS'] = '16'
os.environ['BLAS_NUM_THREADS'] = '16'
os.environ['LAPACK_NUM_THREADS'] = '16'

print(f"\n✅ 设置后的环境变量:")
print(f"   OMP_NUM_THREADS: {os.environ.get('OMP_NUM_THREADS', '未设置')}")
print(f"   MKL_NUM_THREADS: {os.environ.get('MKL_NUM_THREADS', '未设置')}")
print(f"   OPENBLAS_NUM_THREADS: {os.environ.get('OPENBLAS_NUM_THREADS', '未设置')}")
print(f"   BLAS_NUM_THREADS: {os.environ.get('BLAS_NUM_THREADS', '未设置')}")
print(f"   LAPACK_NUM_THREADS: {os.environ.get('LAPACK_NUM_THREADS', '未设置')}")

print(f"\n🧪 导入FAISS...")
try:
    import faiss
    print(f"   FAISS版本: {faiss.__version__}")
    
    # 设置FAISS线程数
    faiss.omp_set_num_threads(16)
    print(f"   FAISS线程数已设置为: 16")
    
    print(f"\n✅ 测试完成")
    
except ImportError as e:
    print(f"❌ FAISS导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 测试失败: {e}")
    sys.exit(1) 