#!/usr/bin/env python3
"""
直接测试FAISS OpenMP设置
"""

import os
import sys
import numpy as np
import time
import threading

# 在导入FAISS之前设置环境变量
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['MKL_NUM_THREADS'] = '16'
os.environ['OPENBLAS_NUM_THREADS'] = '16'
os.environ['BLAS_NUM_THREADS'] = '16'
os.environ['LAPACK_NUM_THREADS'] = '16'

print("🔍 环境变量设置:")
print(f"   OMP_NUM_THREADS: {os.environ.get('OMP_NUM_THREADS', '未设置')}")
print(f"   MKL_NUM_THREADS: {os.environ.get('MKL_NUM_THREADS', '未设置')}")
print(f"   OPENBLAS_NUM_THREADS: {os.environ.get('OPENBLAS_NUM_THREADS', '未设置')}")
print(f"   BLAS_NUM_THREADS: {os.environ.get('BLAS_NUM_THREADS', '未设置')}")
print(f"   LAPACK_NUM_THREADS: {os.environ.get('LAPACK_NUM_THREADS', '未设置')}")

try:
    import faiss
    print(f"\n✅ FAISS导入成功")
    print(f"   FAISS版本: {faiss.__version__}")
    
    # 设置FAISS线程数
    faiss.omp_set_num_threads(16)
    print(f"   FAISS线程数已设置为: 16")
    
    # 创建测试索引
    print(f"\n🧪 创建测试索引...")
    dimension = 768
    index = faiss.IndexHNSWFlat(dimension, 30)
    index.hnsw.efConstruction = 360
    index.hnsw.efSearch = 100
    
    # 生成测试数据
    print(f"   生成测试数据...")
    nb = 100000  # 100K向量
    vectors = np.random.random((nb, dimension)).astype('float32')
    
    # 添加向量到索引
    print(f"   添加向量到索引...")
    index.add(vectors)
    
    # 执行搜索测试
    print(f"   执行搜索测试...")
    query = np.random.random((1, dimension)).astype('float32')
    
    # 执行长时间搜索以观察CPU使用率
    search_count = 10000  # 增加到10000次
    print(f"   执行 {search_count} 次搜索...")
    
    start_time = time.time()
    
    for i in range(search_count):
        D, I = index.search(query, 100)
        if i % 1000 == 0:
            print(f"     完成 {i}/{search_count} 次搜索")
    
    end_time = time.time()
    print(f"   {search_count}次搜索耗时: {end_time - start_time:.3f}秒")
    print(f"   平均每次搜索: {(end_time - start_time)/search_count*1000:.2f}毫秒")
    print(f"   QPS: {search_count/(end_time - start_time):.2f}")
    
    print(f"\n✅ FAISS OpenMP测试完成")
    
except ImportError as e:
    print(f"❌ FAISS导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 测试失败: {e}")
    sys.exit(1) 