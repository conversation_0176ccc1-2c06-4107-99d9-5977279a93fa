#!/usr/bin/env python3
"""
测试CPU利用率修复效果
"""

import subprocess
import time
import sys
import threading
import requests
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import psutil

class CPUUtilizationTest:
    def __init__(self, server_url="http://10.1.180.64:8005"):
        self.server_url = server_url
        self.monitoring = False
        self.cpu_data = []
        
    def check_server_status(self):
        """检查服务器状态"""
        try:
            response = requests.get(f"{self.server_url}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ 服务器连接正常: {self.server_url}")
                return True
            else:
                print(f"❌ 服务器响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接服务器: {e}")
            return False
    
    def get_server_info(self):
        """获取服务器信息"""
        try:
            response = requests.get(f"{self.server_url}/info", timeout=10)
            if response.status_code == 200:
                info = response.json()
                print(f"📊 服务器信息:")
                print(f"   版本: {info.get('version', 'unknown')}")
                print(f"   状态: {info.get('status', 'unknown')}")
                if '当前状态' in info:
                    state = info['当前状态']
                    print(f"   向量数量: {state.get('向量数量', 0):,}")
                    print(f"   索引类型: {state.get('索引类型', 'unknown')}")
                    print(f"   维度: {state.get('维度', 0)}")
                if '进程信息' in info:
                    proc_info = info['进程信息']
                    print(f"   进程ID: {proc_info.get('进程ID', 'unknown')}")
                    print(f"   CPU核心: {proc_info.get('CPU核心', 'unknown')}")
                return True
        except Exception as e:
            print(f"❌ 获取服务器信息失败: {e}")
            return False
    
    def single_search_test(self, query_dim=768):
        """单次搜索测试"""
        query = np.random.random(query_dim).tolist()
        try:
            start_time = time.time()
            response = requests.post(
                f"{self.server_url}/search",
                json={"query": query, "topk": 100},
                timeout=30
            )
            latency = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                return True, latency, len(result.get('ids', []))
            else:
                return False, latency, 0
        except Exception as e:
            return False, 0, 0
    
    def concurrent_search_test(self, concurrency=16, duration=30, query_dim=768):
        """并发搜索测试"""
        print(f"🚀 开始并发搜索测试:")
        print(f"   并发数: {concurrency}")
        print(f"   持续时间: {duration}秒")
        print(f"   查询维度: {query_dim}")
        
        results = {"success": 0, "failed": 0, "latencies": []}
        
        def worker():
            end_time = time.time() + duration
            while time.time() < end_time:
                success, latency, result_count = self.single_search_test(query_dim)
                if success:
                    results["success"] += 1
                    results["latencies"].append(latency)
                else:
                    results["failed"] += 1
                time.sleep(0.01)  # 小延迟避免过度压力
        
        # 启动并发测试
        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = [executor.submit(worker) for _ in range(concurrency)]
            
            # 等待完成
            for future in futures:
                future.result()
        
        # 计算统计信息
        total_requests = results["success"] + results["failed"]
        qps = results["success"] / duration if duration > 0 else 0
        avg_latency = sum(results["latencies"]) / len(results["latencies"]) if results["latencies"] else 0
        p99_latency = np.percentile(results["latencies"], 99) if results["latencies"] else 0
        
        print(f"\n📊 测试结果:")
        print(f"   总请求数: {total_requests}")
        print(f"   成功请求: {results['success']}")
        print(f"   失败请求: {results['failed']}")
        print(f"   成功率: {results['success']/total_requests*100:.1f}%")
        print(f"   QPS: {qps:.1f}")
        print(f"   平均延迟: {avg_latency*1000:.1f}ms")
        print(f"   P99延迟: {p99_latency*1000:.1f}ms")
        
        return results
    
    def monitor_cpu_during_test(self, duration):
        """在测试期间监控CPU使用率"""
        self.monitoring = True
        self.cpu_data = []
        
        def monitor_loop():
            while self.monitoring:
                cpu_percent = psutil.cpu_percent(percpu=True)
                self.cpu_data.append({
                    'timestamp': time.time(),
                    'cpu_total': psutil.cpu_percent(),
                    'cpu_per_core': cpu_percent
                })
                time.sleep(1)
        
        monitor_thread = threading.Thread(target=monitor_loop)
        monitor_thread.start()
        
        return monitor_thread
    
    def stop_monitoring(self, monitor_thread):
        """停止CPU监控"""
        self.monitoring = False
        monitor_thread.join()
        
        if self.cpu_data:
            print(f"\n🔥 CPU使用率分析:")
            
            # 计算平均CPU使用率
            avg_cpu_total = sum(d['cpu_total'] for d in self.cpu_data) / len(self.cpu_data)
            max_cpu_total = max(d['cpu_total'] for d in self.cpu_data)
            
            print(f"   总体CPU使用率:")
            print(f"     平均: {avg_cpu_total:.1f}%")
            print(f"     峰值: {max_cpu_total:.1f}%")
            
            # 计算各核心平均使用率
            core_count = len(self.cpu_data[0]['cpu_per_core'])
            core_avg = [0] * core_count
            
            for data in self.cpu_data:
                for i, usage in enumerate(data['cpu_per_core']):
                    core_avg[i] += usage
            
            core_avg = [usage / len(self.cpu_data) for usage in core_avg]
            
            print(f"   各核心平均使用率:")
            active_cores = 0
            for i, avg_usage in enumerate(core_avg):
                if avg_usage > 10:  # 认为使用率>10%的核心是活跃的
                    active_cores += 1
                status = "🔴" if avg_usage > 70 else "🟡" if avg_usage > 30 else "🟢" if avg_usage > 10 else "⚪"
                print(f"     核心{i:2d}: {status} {avg_usage:5.1f}%")
            
            print(f"\n💡 CPU利用率诊断:")
            print(f"   活跃核心数: {active_cores}/{core_count}")
            utilization_ratio = active_cores / core_count
            
            if utilization_ratio < 0.3:
                print(f"   ❌ CPU利用率严重不足！只有{utilization_ratio*100:.1f}%的核心在工作")
                print(f"   🔧 问题可能在于:")
                print(f"      - OpenMP线程数设置过低")
                print(f"      - FAISS索引不支持多线程")
                print(f"      - 服务器线程池配置不当")
                print(f"      - GIL限制（如果使用Python线程）")
            elif utilization_ratio < 0.6:
                print(f"   ⚠️  CPU利用率偏低，{utilization_ratio*100:.1f}%的核心在工作")
                print(f"   🔧 可以进一步优化")
            else:
                print(f"   ✅ CPU利用率良好！{utilization_ratio*100:.1f}%的核心在工作")
    
    def run_full_test(self):
        """运行完整测试"""
        print("🎯 FAISS服务器CPU利用率测试")
        print("=" * 50)
        
        # 1. 检查服务器状态
        if not self.check_server_status():
            return False
        
        # 2. 获取服务器信息
        self.get_server_info()
        
        # 3. 单次搜索测试
        print(f"\n🧪 单次搜索测试...")
        success, latency, result_count = self.single_search_test()
        if success:
            print(f"   ✅ 单次搜索成功: {latency*1000:.1f}ms, 返回{result_count}个结果")
        else:
            print(f"   ❌ 单次搜索失败")
            return False
        
        # 4. 并发搜索测试 + CPU监控
        test_configs = [
            {"concurrency": 16, "duration": 30},
            {"concurrency": 64, "duration": 30},
            {"concurrency": 128, "duration": 30},
        ]
        
        for config in test_configs:
            print(f"\n" + "="*60)
            print(f"🚀 并发测试: {config['concurrency']} 并发")
            
            # 启动CPU监控
            monitor_thread = self.monitor_cpu_during_test(config['duration'])
            
            # 运行并发测试
            results = self.concurrent_search_test(
                concurrency=config['concurrency'],
                duration=config['duration']
            )
            
            # 停止监控并分析
            self.stop_monitoring(monitor_thread)
            
            time.sleep(2)  # 短暂休息
        
        print(f"\n✅ 测试完成！")
        return True

def main():
    server_url = sys.argv[1] if len(sys.argv) > 1 else "http://10.1.180.64:8005"
    
    print(f"🎯 测试目标服务器: {server_url}")
    
    tester = CPUUtilizationTest(server_url)
    tester.run_full_test()

if __name__ == "__main__":
    main()
