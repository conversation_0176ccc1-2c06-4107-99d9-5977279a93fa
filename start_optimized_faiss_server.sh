#!/bin/bash

# 优化版FAISS服务器启动脚本
# 解决CPU利用率只有1个核的问题

echo "🚀 启动优化版FAISS服务器"
echo "=========================="

# 检查Python版本
PYTHON_CMD="python3.11"
if ! command -v $PYTHON_CMD &> /dev/null; then
    echo "❌ 未找到 $PYTHON_CMD，尝试使用 python3"
    PYTHON_CMD="python3"
    if ! command -v $PYTHON_CMD &> /dev/null; then
        echo "❌ 未找到 Python3，请安装 Python 3.11+"
        exit 1
    fi
fi

echo "✅ 使用Python: $PYTHON_CMD"

# 获取CPU信息
CPU_CORES=$(nproc)
CPU_THREADS=$(nproc --all)
echo "💻 系统信息:"
echo "   物理核心: $CPU_CORES"
echo "   逻辑线程: $CPU_THREADS"

# 计算最优线程数
# 对于CPU密集型任务，通常设置为物理核心数
OMP_THREADS=${1:-$CPU_CORES}
if [ $OMP_THREADS -gt $CPU_THREADS ]; then
    echo "⚠️  线程数 $OMP_THREADS 超过系统最大值 $CPU_THREADS，调整为 $CPU_THREADS"
    OMP_THREADS=$CPU_THREADS
fi

echo "🔧 配置参数:"
echo "   OpenMP线程数: $OMP_THREADS"
echo "   批次大小: 10000"
echo "   基础延迟: 0.001s"
echo "   内存阈值: 85%"
echo "   端口: 8005"

# 设置环境变量（在启动前设置，确保生效）
export OMP_NUM_THREADS=$OMP_THREADS
export MKL_NUM_THREADS=$OMP_THREADS
export OPENBLAS_NUM_THREADS=$OMP_THREADS
export BLAS_NUM_THREADS=$OMP_THREADS
export LAPACK_NUM_THREADS=$OMP_THREADS

# 设置更激进的OpenMP策略
export OMP_WAIT_POLICY=ACTIVE
export OMP_DYNAMIC=false
export OMP_NESTED=true
export OMP_PROC_BIND=spread
export OMP_PLACES=cores

echo "⚙️  环境变量已设置:"
echo "   OMP_NUM_THREADS=$OMP_NUM_THREADS"
echo "   OMP_WAIT_POLICY=$OMP_WAIT_POLICY"
echo "   OMP_PROC_BIND=$OMP_PROC_BIND"
echo "   OMP_PLACES=$OMP_PLACES"

# 检查是否有旧进程在运行
OLD_PID=$(pgrep -f "smart_faiss_server_optimized.py")
if [ ! -z "$OLD_PID" ]; then
    echo "⚠️  发现旧进程 PID: $OLD_PID"
    read -p "是否终止旧进程? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        kill -TERM $OLD_PID
        sleep 2
        if kill -0 $OLD_PID 2>/dev/null; then
            echo "强制终止进程..."
            kill -KILL $OLD_PID
        fi
        echo "✅ 旧进程已终止"
    else
        echo "❌ 取消启动"
        exit 1
    fi
fi

# 使用numactl绑定到指定CPU核心（如果可用）
NUMA_CMD=""
if command -v numactl &> /dev/null; then
    # 绑定到前16个核心（0-15）
    CORE_LIST="0-$((OMP_THREADS-1))"
    NUMA_CMD="numactl --physcpubind=$CORE_LIST --localalloc"
    echo "🎯 使用NUMA绑定: $NUMA_CMD"
else
    echo "⚠️  numactl不可用，跳过CPU绑定"
fi

# 启动命令
START_CMD="$NUMA_CMD $PYTHON_CMD smart_faiss_server_optimized.py \
    --batch-size 10000 \
    --base-delay 0.001 \
    --memory-threshold 85 \
    --omp-threads $OMP_THREADS \
    --port 8005"

echo ""
echo "🚀 启动命令:"
echo "$START_CMD"
echo ""

# 询问是否启动
read -p "是否启动服务器? (Y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Nn]$ ]]; then
    echo "❌ 取消启动"
    exit 0
fi

# 启动服务器
echo "🚀 正在启动服务器..."
echo "📝 日志将显示在下方，按 Ctrl+C 停止服务器"
echo "=" * 60

# 启动并显示实时日志
exec $START_CMD
