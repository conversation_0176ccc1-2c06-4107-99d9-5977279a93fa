#!/usr/bin/env python3
"""
直接测试FAISS多线程性能 - 绕过HTTP服务器
"""

import os
import time
import numpy as np
import psutil
import threading
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp

# 设置OpenMP环境变量
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['MKL_NUM_THREADS'] = '16'
os.environ['OPENBLAS_NUM_THREADS'] = '16'
os.environ['OMP_WAIT_POLICY'] = 'ACTIVE'
os.environ['OMP_DYNAMIC'] = 'false'
os.environ['OMP_PROC_BIND'] = 'spread'
os.environ['OMP_PLACES'] = 'cores'

try:
    import faiss
except ImportError:
    print("❌ FAISS未安装")
    exit(1)

def create_test_index(n_vectors=1000000, dim=768):
    """创建测试索引"""
    print(f"🔧 创建测试索引: {n_vectors:,} 向量, {dim} 维度")
    
    # 创建随机数据
    vectors = np.random.random((n_vectors, dim)).astype('float32')
    
    # 创建HNSW索引
    index = faiss.IndexHNSWFlat(dim, 32)
    index.hnsw.efConstruction = 200
    
    print(f"🚀 开始训练索引...")
    start_time = time.time()
    index.add(vectors)
    train_time = time.time() - start_time
    print(f"✅ 索引训练完成: {train_time:.2f}秒")
    
    return index

def test_single_thread_search(index, queries, topk=100):
    """测试单线程搜索性能"""
    print(f"\n🧪 单线程搜索测试")
    
    # 设置FAISS为单线程
    faiss.omp_set_num_threads(1)
    print(f"   FAISS线程数: {faiss.omp_get_max_threads()}")
    
    start_time = time.time()
    D, I = index.search(queries, topk)
    search_time = time.time() - start_time
    
    qps = len(queries) / search_time
    print(f"   搜索时间: {search_time:.3f}秒")
    print(f"   QPS: {qps:.1f}")
    
    return qps, search_time

def test_multi_thread_search(index, queries, num_threads=16, topk=100):
    """测试多线程搜索性能"""
    print(f"\n🚀 多线程搜索测试 ({num_threads} 线程)")
    
    # 设置FAISS多线程
    faiss.omp_set_num_threads(num_threads)
    print(f"   FAISS线程数: {faiss.omp_get_max_threads()}")
    
    start_time = time.time()
    D, I = index.search(queries, topk)
    search_time = time.time() - start_time
    
    qps = len(queries) / search_time
    print(f"   搜索时间: {search_time:.3f}秒")
    print(f"   QPS: {qps:.1f}")
    
    return qps, search_time

def monitor_cpu_usage(duration=10):
    """监控CPU使用率"""
    cpu_data = []
    
    def monitor_loop():
        end_time = time.time() + duration
        while time.time() < end_time:
            cpu_percent = psutil.cpu_percent(percpu=True)
            cpu_data.append(cpu_percent)
            time.sleep(0.5)
    
    monitor_thread = threading.Thread(target=monitor_loop)
    monitor_thread.start()
    
    return monitor_thread, cpu_data

def analyze_cpu_usage(cpu_data):
    """分析CPU使用率数据"""
    if not cpu_data:
        return
    
    # 计算平均使用率
    core_count = len(cpu_data[0])
    core_avg = [0] * core_count
    
    for snapshot in cpu_data:
        for i, usage in enumerate(snapshot):
            core_avg[i] += usage
    
    core_avg = [usage / len(cpu_data) for usage in core_avg]
    
    print(f"   CPU使用率分析:")
    active_cores = 0
    for i, avg_usage in enumerate(core_avg):
        if avg_usage > 10:
            active_cores += 1
        status = "🔴" if avg_usage > 70 else "🟡" if avg_usage > 30 else "🟢" if avg_usage > 10 else "⚪"
        print(f"     核心{i:2d}: {status} {avg_usage:5.1f}%")
    
    print(f"   活跃核心: {active_cores}/{core_count}")
    return active_cores

def test_concurrent_requests(index, queries, concurrency=16, topk=100):
    """测试并发请求"""
    print(f"\n🔥 并发请求测试 ({concurrency} 并发)")
    
    # 设置FAISS多线程
    faiss.omp_set_num_threads(16)
    
    def single_search():
        # 每个线程搜索一个查询
        query_idx = np.random.randint(0, len(queries))
        query = queries[query_idx:query_idx+1]
        D, I = index.search(query, topk)
        return len(I)
    
    # 启动CPU监控
    monitor_thread, cpu_data = monitor_cpu_usage(10)
    
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = [executor.submit(single_search) for _ in range(concurrency * 50)]  # 每个线程执行50次
        results = [f.result() for f in futures]
    
    search_time = time.time() - start_time
    monitor_thread.join()
    
    total_searches = len(results)
    qps = total_searches / search_time
    
    print(f"   总搜索次数: {total_searches}")
    print(f"   搜索时间: {search_time:.3f}秒")
    print(f"   QPS: {qps:.1f}")
    
    active_cores = analyze_cpu_usage(cpu_data)
    return qps, active_cores

def main():
    print("🎯 FAISS多线程性能直接测试")
    print("=" * 50)
    
    # 显示系统信息
    cpu_count = psutil.cpu_count()
    print(f"💻 系统信息:")
    print(f"   CPU核心数: {cpu_count}")
    print(f"   当前FAISS线程数: {faiss.omp_get_max_threads()}")
    
    # 显示OpenMP环境变量
    print(f"⚙️  OpenMP设置:")
    openmp_vars = ['OMP_NUM_THREADS', 'MKL_NUM_THREADS', 'OMP_WAIT_POLICY', 'OMP_PROC_BIND']
    for var in openmp_vars:
        value = os.environ.get(var, 'NOT_SET')
        print(f"   {var}: {value}")
    
    # 创建测试数据
    print(f"\n📊 创建测试数据...")
    index = create_test_index(100000, 768)  # 较小的索引用于快速测试
    queries = np.random.random((1000, 768)).astype('float32')
    
    # 测试1: 单线程 vs 多线程搜索
    single_qps, _ = test_single_thread_search(index, queries)
    multi_qps, _ = test_multi_thread_search(index, queries, 16)
    
    speedup = multi_qps / single_qps if single_qps > 0 else 0
    print(f"\n📈 性能对比:")
    print(f"   单线程QPS: {single_qps:.1f}")
    print(f"   多线程QPS: {multi_qps:.1f}")
    print(f"   加速比: {speedup:.1f}x")
    
    if speedup < 2:
        print(f"   ⚠️  多线程加速效果不明显！")
    else:
        print(f"   ✅ 多线程加速效果良好")
    
    # 测试2: 并发请求
    concurrent_qps, active_cores = test_concurrent_requests(index, queries, 16)
    
    print(f"\n💡 诊断结果:")
    if active_cores < cpu_count * 0.5:
        print(f"   ❌ CPU利用率不足！只有{active_cores}个核心活跃")
        print(f"   🔧 可能的原因:")
        print(f"      - Python GIL限制")
        print(f"      - FAISS内部线程同步问题")
        print(f"      - OpenMP配置问题")
        print(f"      - 索引类型不支持多线程")
    else:
        print(f"   ✅ CPU利用率良好！{active_cores}个核心活跃")
    
    # 建议
    print(f"\n💡 优化建议:")
    if speedup < 2:
        print(f"   1. 检查FAISS编译是否支持OpenMP")
        print(f"   2. 尝试使用更大的批次大小")
        print(f"   3. 考虑使用多进程而非多线程")
    
    if active_cores < cpu_count * 0.5:
        print(f"   4. 使用多进程架构绕过GIL限制")
        print(f"   5. 检查NUMA绑定设置")
        print(f"   6. 考虑使用GPU版本的FAISS")

if __name__ == "__main__":
    main()
